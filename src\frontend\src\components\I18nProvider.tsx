// =====================================================
// I18N PROVIDER - PROVEDOR DE INTERNACIONALIZAÇÃO
// Aguarda a inicialização do i18n antes de renderizar
// =====================================================

import React, { Suspense, useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

interface I18nProviderProps {
  children: React.ReactNode
}

function I18nContent({ children }: I18nProviderProps) {
  const { i18n } = useTranslation()
  const [isReady, setIsReady] = useState(i18n.isInitialized)

  useEffect(() => {
    if (i18n.isInitialized) {
      setIsReady(true)
    } else {
      const handleInitialized = () => setIsReady(true)
      i18n.on('initialized', handleInitialized)
      return () => i18n.off('initialized', handleInitialized)
    }
  }, [i18n])

  if (!isReady) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return <>{children}</>
}

export function I18nProvider({ children }: I18nProviderProps) {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    }>
      <I18nContent>{children}</I18nContent>
    </Suspense>
  )
}