import { insert } from 'capfunctions';
import axios from 'axios';

export const WHATSAPP_TOKEN = process.env.WHATSAPP_TOKEN;
export const PHONE_NUMBER_ID = process.env.WHATSAPP_PHONE_NUMBER_ID;
export const VERIFY_TOKEN = process.env.WHATSAPP_VERIFY_TOKEN;

export const sendMessage = async ({ number, message }) => {
  if (!number || !message) {
    return {
      status: false,
      message: 'Número ou mensagem não informados',
    };
  }

  console.log('capcorpconf/sendMessage()', { number, message });

  try {
    const response = await axios.post(
      `https://graph.facebook.com/v18.0/${PHONE_NUMBER_ID}/messages`,
      {
        messaging_product: 'whatsapp',
        to: number,
        type: 'text',
        text: { body: message },
      },
      {
        headers: {
          Authorization: `Bearer ${WHATSAPP_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );

    const entity = {
      mwTelefone: number,
      mwDataMens: 'current_timestamp',
      mwEnvRec: 'E',
      mwTelefoneFrom: '',
      mwType: '',
      mwPayload: '',
      mwText: '',
      mwPayloadEnviado: JSON.stringify({
        messaging_product: 'whatsapp',
        to: number,
        type: 'text',
        text: { body: message },
      }),
    };

    await insert('capMensagemWhatsapp', entity, {
      body: {
        newToken: 'system',
        userName: 'system',
      },
    });

    return { status: true, data: response.data, message: 'Mensagem enviada com sucesso' };
  } catch (error) {
    console.error('capcorpconf/sendMessage() Error:', error);

    return { status: false, message: error.response?.data || error.message };
  }
};

export const sendTemplateMessage = async (event) => {
  try {
    console.log('sendTemplateMessage.event ', event);
    const { phoneNumber, templateName, language, parambody, buttons } = event.body;

    const response = await axios.post(
      `https://graph.facebook.com/v22.0/${PHONE_NUMBER_ID}/messages`,
      {
        messaging_product: 'whatsapp',
        to: phoneNumber,
        type: 'template',
        template: {
          name: templateName,
          language: { code: language },
          components: [
            {
              type: 'body',
              parameters: Object.keys(parambody).map((key) => ({
                type: 'text',
                parameter_name: key,
                text: parambody[key],
              })),
            },
            ...buttons.map((button, index) => ({
              type: 'button',
              sub_type: 'quick_reply',
              index,
              parameters: [
                {
                  type: 'payload',
                  payload: button,
                },
              ],
            })),
          ],
        },
      },
      {
        headers: {
          Authorization: `Bearer ${WHATSAPP_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );

    response.data;
  } catch (error) {
    return error;
  }
};
