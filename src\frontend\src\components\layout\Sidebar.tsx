// =====================================================
// SIDEBAR COMPONENT - COMPONENTE DE BARRA LATERAL
// Navegação principal do sistema
// =====================================================

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { NavLink, useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import {
  LayoutDashboard,
  Users,
  UserCheck,
  Building2,
  Calendar,
  CreditCard,
  MessageSquare,
  Shield,
  BarChart3,
  Settings,
  ChevronLeft,
  ChevronRight,
  LogOut,
  User
} from 'lucide-react'
import { cn } from '@/utils'
import { useAuth, usePermissions } from '@/hooks/useAuth'
import type { MenuItem } from '@/types'

interface SidebarProps {
  isOpen: boolean
  onToggle: () => void
}

export function Sidebar({ isOpen, onToggle }: SidebarProps) {
  const { t } = useTranslation()
  const location = useLocation()
  const { user, logout } = useAuth()
  const { hasPermission, hasAnyGroup } = usePermissions()

  // Menu items com permissões
  const menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: t('navigation.dashboard'),
      icon: LayoutDashboard,
      path: '/dashboard'
    },
    {
      id: 'users',
      label: t('navigation.users'),
      icon: Users,
      path: '/users',
      permission: 'users.read'
    },
    {
      id: 'professionals',
      label: t('navigation.professionals'),
      icon: UserCheck,
      path: '/professionals',
      permission: 'professionals.read'
    },
    {
      id: 'clients',
      label: t('navigation.clients'),
      icon: Building2,
      path: '/clients',
      permission: 'clients.read'
    },
    {
      id: 'shifts',
      label: t('navigation.shifts'),
      icon: Calendar,
      path: '/shifts',
      permission: 'shifts.read'
    },
    {
      id: 'advances',
      label: t('navigation.advances'),
      icon: CreditCard,
      path: '/advances',
      permission: 'advances.read'
    },
    {
      id: 'whatsapp',
      label: t('navigation.whatsapp'),
      icon: MessageSquare,
      path: '/whatsapp',
      permission: 'whatsapp.read'
    },
    {
      id: 'audit',
      label: t('navigation.audit'),
      icon: Shield,
      path: '/audit',
      permission: 'audit.read'
    },
    {
      id: 'reports',
      label: t('navigation.reports'),
      icon: BarChart3,
      path: '/reports',
      permission: 'reports.read'
    },
    {
      id: 'settings',
      label: t('navigation.settings'),
      icon: Settings,
      path: '/settings',
      permission: 'settings.read'
    }
  ]

  // Filtrar itens baseado nas permissões
  const visibleItems = menuItems.filter(item => {
    if (!item.permission) return true
    return hasPermission(item.permission)
  })

  const handleLogout = () => {
    logout()
  }

  return (
    <>
      {/* Overlay para mobile */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-black/50 lg:hidden"
            onClick={onToggle}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.aside
        initial={false}
        animate={{
          width: isOpen ? 280 : 80,
          x: 0
        }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className={cn(
          'fixed left-0 top-0 z-50 h-full bg-white border-r border-secondary-200',
          'dark:bg-secondary-900 dark:border-secondary-700',
          'flex flex-col shadow-lg lg:shadow-none'
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-secondary-200 dark:border-secondary-700">
          <AnimatePresence mode="wait">
            {isOpen ? (
              <motion.div
                key="logo-full"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="flex items-center space-x-3"
              >
                <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">GS</span>
                </div>
                <div>
                  <h1 className="text-lg font-bold text-secondary-900 dark:text-secondary-100">
                    Sistema GS2
                  </h1>
                  <p className="text-xs text-secondary-500 dark:text-secondary-400">
                    Gestão de Saúde
                  </p>
                </div>
              </motion.div>
            ) : (
              <motion.div
                key="logo-mini"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center mx-auto"
              >
                <span className="text-white font-bold text-lg">GS</span>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Toggle Button */}
          <button
            onClick={onToggle}
            className={cn(
              'p-1.5 rounded-lg transition-colors',
              'hover:bg-secondary-100 dark:hover:bg-secondary-800',
              'text-secondary-500 hover:text-secondary-700',
              'dark:text-secondary-400 dark:hover:text-secondary-200',
              !isOpen && 'hidden lg:block'
            )}
          >
            {isOpen ? (
              <ChevronLeft className="w-5 h-5" />
            ) : (
              <ChevronRight className="w-5 h-5" />
            )}
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {visibleItems.map((item) => {
            const isActive = location.pathname === item.path || 
                           location.pathname.startsWith(item.path + '/')
            
            return (
              <NavLink
                key={item.id}
                to={item.path!}
                className={({ isActive: linkActive }) =>
                  cn(
                    'flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200',
                    'text-secondary-700 dark:text-secondary-300',
                    'hover:bg-secondary-100 dark:hover:bg-secondary-800',
                    'hover:text-secondary-900 dark:hover:text-secondary-100',
                    (isActive || linkActive) && [
                      'bg-primary-50 text-primary-700 border border-primary-200',
                      'dark:bg-primary-900/20 dark:text-primary-300 dark:border-primary-800'
                    ]
                  )
                }
              >
                <item.icon className={cn(
                  'w-5 h-5 flex-shrink-0',
                  isActive && 'text-primary-600 dark:text-primary-400'
                )} />
                
                <AnimatePresence>
                  {isOpen && (
                    <motion.span
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      className="font-medium truncate"
                    >
                      {item.label}
                    </motion.span>
                  )}
                </AnimatePresence>

                {/* Badge */}
                {item.badge && isOpen && (
                  <motion.span
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="ml-auto px-2 py-0.5 text-xs font-medium bg-primary-100 text-primary-700 rounded-full dark:bg-primary-900/30 dark:text-primary-300"
                  >
                    {item.badge}
                  </motion.span>
                )}
              </NavLink>
            )
          })}
        </nav>

        {/* User Section */}
        <div className="p-4 border-t border-secondary-200 dark:border-secondary-700">
          {/* User Info */}
          <div className={cn(
            'flex items-center space-x-3 p-3 rounded-lg mb-3',
            'bg-secondary-50 dark:bg-secondary-800'
          )}>
            <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center flex-shrink-0">
              <User className="w-4 h-4 text-white" />
            </div>
            
            <AnimatePresence>
              {isOpen && user && (
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  className="flex-1 min-w-0"
                >
                  <p className="text-sm font-medium text-secondary-900 dark:text-secondary-100 truncate">
                    {user.name}
                  </p>
                  <p className="text-xs text-secondary-500 dark:text-secondary-400 truncate">
                    {user.groups?.[0]?.name || 'Usuário'}
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Logout Button */}
          <button
            onClick={handleLogout}
            className={cn(
              'flex items-center space-x-3 w-full px-3 py-2.5 rounded-lg transition-all duration-200',
              'text-error-600 dark:text-error-400',
              'hover:bg-error-50 dark:hover:bg-error-900/20',
              'hover:text-error-700 dark:hover:text-error-300'
            )}
          >
            <LogOut className="w-5 h-5 flex-shrink-0" />
            
            <AnimatePresence>
              {isOpen && (
                <motion.span
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  className="font-medium"
                >
                  {t('common.logout')}
                </motion.span>
              )}
            </AnimatePresence>
          </button>
        </div>
      </motion.aside>
    </>
  )
}
