/**
 * @typedef {'Aberto'|'Solicitado'|'Aprovado'|'Executado'|'AprovExecucao'|'Antecipado'} SituacaoType
 * @typedef {'manual'|'diario'|'semanal'|'mensal'} TipoFechamentoType
 *
 * @typedef {Object} capInstSaudePlantaoType
 * @property {string} isInstSaude
 * @property {string} laNome
 * @property {string} esEspecialidade
 * @property {string} ocNrContrato
 * @property {string} clCliente
 * @property {string} ceTipoPagamento
 * @property {number} opNrPlantao
 * @property {string|null} usCPFUsuarioAprovador
 * @property {Date|null} opDataDivulgacao
 * @property {number|null} opAtivo
 * @property {TipoFechamentoType|null} opTipoFechamento
 * @property {Date|null} opDataFechamento
 * @property {Date} opPeriodoIni
 * @property {Date} opPeriodoFim
 * @property {string|null} psCPF
 * @property {Date|null} opDatarecebimento
 * @property {Date|null} opDataAntecipacao
 * @property {number} opQtdHorasRequisitada
 * @property {string|null} opQtdHorasRealizadas
 * @property {number|null} opValorFixo
 * @property {number|null} opValorHora
 * @property {number|null} opValorUnit
 * @property {string|null} opChaveAcesso
 * @property {SituacaoType|null} opSituacao
 * @property {Date|null} dtInclusao
 * @property {Date|null} dtModificacao
 * @property {number|null} opDiaFechamento
 */

/**
 * @typedef {'EnviadoAprovacao'|'Aprovado'|'Fechado'} OcSituacaoType
 * 
 * @typedef {Object} capOperPlantaoCheckType
 * @property {string} isInstSaude
 * @property {string} laNome
 * @property {string} esEspecialidade
 * @property {string} ocNrContrato
 * @property {string} clCliente
 * @property {string} ceTipoPagamento
 * @property {number} opNrPlantao
 * @property {Date} agData
 * @property {Date} ocCheckIn
 * @property {Date|null} ocCheckOut
 * @property {string|null} ocQtRealizadas
 * @property {string|null} ocQtAprovadas
 * @property {string|null} ocQtGlosadas
 * @property {string|null} pcJustificativa
 * @property {number|null} codFechamento
 * @property {number} ocCheckAprovado
 * @property {string|null} ocUsuarioAprovacao
 * @property {OcSituacaoType|null} ocSituacao
 */

/**
 * @typedef {Object} capInstSaudePlantaoAgendaType
 * @property {string} isInstSaude
 * @property {string} laNome
 * @property {string} esEspecialidade
 * @property {string} ocNrContrato
 * @property {string} clCliente
 * @property {string} ceTipoPagamento
 * @property {number} opNrPlantao
 * @property {Date} agData
 * @property {number|null} agDiaSem
 * @property {Date|null} agHoraIni
 * @property {Date|null} agHoraFim
 * @property {Date|null} agDataIni
 * @property {Date|null} agDataFim
 * @property {Date|null} agIntervalo
 * @property {Date} dtInclusao
 * @property {Date} dtModificacao
 * @property {number} agAtivo
 */

export {};
