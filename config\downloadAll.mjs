import fs from 'fs';
import { execTerminal, getArg } from './functions.mjs';
import lambdas from './lambdas.mjs';

const profile = getArg('profile');
const region = getArg('region');

if (!profile || !region) {
  console.log(
    '\x1b[31m%s\x1b[0m',
    '\nVocê precisa passar "profile" e "region"\n'
  );
  process.exit();
}

const main = async () => {
    const logs = [];
    try {
        const functions = lambdas[region];
        console.log(`Functions to download: ${functions}`); // Debug log

        // Execute the first downloadFunction command
        console.log('Running first downloadFunction command...');
        await execTerminal(
            `yarn downloadFunction profile=${profile} region=${region} lambdaName=${functions[0]} silent=true`
        );
        console.log('First downloadFunction command executed successfully.');

        // Remove the return statement to allow the loop to run
        // return

        for await (let lambda of functions) {
            console.log(`Downloading lambda: ${lambda}`); // Debug log
            const result = await execTerminal(
                `yarn downloadFunction profile=${profile} region=${region} lambdaName=${lambda} silent=true`
            );
            console.log(`Result for ${lambda}: ${result}`);
            logs.push(result);
        }

        console.log('All functions downloaded successfully.');
    } catch (error) {
        logs.push(error.message);
        console.log('\n');
        console.log('\x1b[31m%s\x1b[0m', 'Catch no downloadAll():', error.message);
    } finally {
        // Optionally, write logs to a file or handle them as needed
        console.log('Logs:', logs);
        process.exit();
    }
};

main();