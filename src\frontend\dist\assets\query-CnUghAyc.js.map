{"version": 3, "file": "query-CnUghAyc.js", "sources": ["../../node_modules/react/cjs/react-jsx-runtime.production.min.js", "../../node_modules/react/jsx-runtime.js", "../../node_modules/@tanstack/query-core/build/modern/subscribable.js", "../../node_modules/@tanstack/query-core/build/modern/utils.js", "../../node_modules/@tanstack/query-core/build/modern/focusManager.js", "../../node_modules/@tanstack/query-core/build/modern/onlineManager.js", "../../node_modules/@tanstack/query-core/build/modern/thenable.js", "../../node_modules/@tanstack/query-core/build/modern/retryer.js", "../../node_modules/@tanstack/query-core/build/modern/notifyManager.js", "../../node_modules/@tanstack/query-core/build/modern/removable.js", "../../node_modules/@tanstack/query-core/build/modern/query.js", "../../node_modules/@tanstack/query-core/build/modern/queryCache.js", "../../node_modules/@tanstack/query-core/build/modern/mutation.js", "../../node_modules/@tanstack/query-core/build/modern/mutationCache.js", "../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js", "../../node_modules/@tanstack/query-core/build/modern/queryClient.js", "../../node_modules/@tanstack/query-core/build/modern/queryObserver.js", "../../node_modules/@tanstack/query-core/build/modern/mutationObserver.js", "../../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js", "../../node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js", "../../node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js", "../../node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js", "../../node_modules/@tanstack/react-query/build/modern/suspense.js", "../../node_modules/@tanstack/react-query/build/modern/useBaseQuery.js", "../../node_modules/@tanstack/react-query/build/modern/useQuery.js", "../../node_modules/@tanstack/react-query/build/modern/useMutation.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var f=require(\"react\"),k=Symbol.for(\"react.element\"),l=Symbol.for(\"react.fragment\"),m=Object.prototype.hasOwnProperty,n=f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,p={key:!0,ref:!0,__self:!0,__source:!0};\nfunction q(c,a,g){var b,d={},e=null,h=null;void 0!==g&&(e=\"\"+g);void 0!==a.key&&(e=\"\"+a.key);void 0!==a.ref&&(h=a.ref);for(b in a)m.call(a,b)&&!p.hasOwnProperty(b)&&(d[b]=a[b]);if(c&&c.defaultProps)for(b in a=c.defaultProps,a)void 0===d[b]&&(d[b]=a[b]);return{$$typeof:k,type:c,key:e,ref:h,props:d,_owner:n.current}}exports.Fragment=l;exports.jsx=q;exports.jsxs=q;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "// src/subscribable.ts\nvar Subscribable = class {\n  constructor() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    this.listeners.add(listener);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(listener);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {\n  }\n  onUnsubscribe() {\n  }\n};\nexport {\n  Subscribable\n};\n//# sourceMappingURL=subscribable.js.map", "// src/utils.ts\nvar isServer = typeof window === \"undefined\" || \"Deno\" in globalThis;\nfunction noop() {\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n  return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n  return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = \"all\",\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n  if (type !== \"all\") {\n    const isActive = query.isActive();\n    if (type === \"active\" && !isActive) {\n      return false;\n    }\n    if (type === \"inactive\" && isActive) {\n      return false;\n    }\n  }\n  if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n    return false;\n  }\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const { exact, status, predicate, mutationKey } = filters;\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n  if (status && mutation.state.status !== status) {\n    return false;\n  }\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = options?.queryKeyHashFn || hashKey;\n  return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n  return JSON.stringify(\n    queryKey,\n    (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n      result[key] = val[key];\n      return result;\n    }, {}) : val\n  );\n}\nfunction partialMatchKey(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    return Object.keys(b).every((key) => partialMatchKey(a[key], b[key]));\n  }\n  return false;\n}\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n  const array = isPlainArray(a) && isPlainArray(b);\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aItems = array ? a : Object.keys(a);\n    const aSize = aItems.length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    const aItemsSet = new Set(aItems);\n    let equalItems = 0;\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      if ((!array && aItemsSet.has(key) || array) && a[key] === void 0 && b[key] === void 0) {\n        copy[key] = void 0;\n        equalItems++;\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key]);\n        if (copy[key] === a[key] && a[key] !== void 0) {\n          equalItems++;\n        }\n      }\n    }\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n  return b;\n}\nfunction shallowEqualObjects(a, b) {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (ctor === void 0) {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout);\n  });\n}\nfunction replaceData(prevData, data, options) {\n  if (typeof options.structuralSharing === \"function\") {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    if (process.env.NODE_ENV !== \"production\") {\n      try {\n        return replaceEqualDeep(prevData, data);\n      } catch (error) {\n        console.error(\n          `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`\n        );\n        throw error;\n      }\n    }\n    return replaceEqualDeep(prevData, data);\n  }\n  return data;\n}\nfunction keepPreviousData(previousData) {\n  return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n  const newItems = [...items, item];\n  return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n  const newItems = [item, ...items];\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`\n      );\n    }\n  }\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise;\n  }\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () => Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n  }\n  return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n  if (typeof throwOnError === \"function\") {\n    return throwOnError(...params);\n  }\n  return !!throwOnError;\n}\nexport {\n  addToEnd,\n  addToStart,\n  ensureQueryFn,\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  isPlainArray,\n  isPlainObject,\n  isServer,\n  isValidTimeout,\n  keepPreviousData,\n  matchMutation,\n  matchQuery,\n  noop,\n  partialMatchKey,\n  replaceData,\n  replaceEqualDeep,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  shouldThrowError,\n  skipToken,\n  sleep,\n  timeUntilStale\n};\n//# sourceMappingURL=utils.js.map", "// src/focusManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar FocusManager = class extends Subscribable {\n  #focused;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onFocus) => {\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus();\n        window.addEventListener(\"visibilitychange\", listener, false);\n        return () => {\n          window.removeEventListener(\"visibilitychange\", listener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === \"boolean\") {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n  setFocused(focused) {\n    const changed = this.#focused !== focused;\n    if (changed) {\n      this.#focused = focused;\n      this.onFocus();\n    }\n  }\n  onFocus() {\n    const isFocused = this.isFocused();\n    this.listeners.forEach((listener) => {\n      listener(isFocused);\n    });\n  }\n  isFocused() {\n    if (typeof this.#focused === \"boolean\") {\n      return this.#focused;\n    }\n    return globalThis.document?.visibilityState !== \"hidden\";\n  }\n};\nvar focusManager = new FocusManager();\nexport {\n  FocusManager,\n  focusManager\n};\n//# sourceMappingURL=focusManager.js.map", "// src/onlineManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar OnlineManager = class extends Subscribable {\n  #online = true;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onOnline) => {\n      if (!isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true);\n        const offlineListener = () => onOnline(false);\n        window.addEventListener(\"online\", onlineListener, false);\n        window.addEventListener(\"offline\", offlineListener, false);\n        return () => {\n          window.removeEventListener(\"online\", onlineListener);\n          window.removeEventListener(\"offline\", offlineListener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(this.setOnline.bind(this));\n  }\n  setOnline(online) {\n    const changed = this.#online !== online;\n    if (changed) {\n      this.#online = online;\n      this.listeners.forEach((listener) => {\n        listener(online);\n      });\n    }\n  }\n  isOnline() {\n    return this.#online;\n  }\n};\nvar onlineManager = new OnlineManager();\nexport {\n  OnlineManager,\n  onlineManager\n};\n//# sourceMappingURL=onlineManager.js.map", "// src/thenable.ts\nimport { noop } from \"./utils.js\";\nfunction pendingThenable() {\n  let resolve;\n  let reject;\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  thenable.status = \"pending\";\n  thenable.catch(() => {\n  });\n  function finalize(data) {\n    Object.assign(thenable, data);\n    delete thenable.resolve;\n    delete thenable.reject;\n  }\n  thenable.resolve = (value) => {\n    finalize({\n      status: \"fulfilled\",\n      value\n    });\n    resolve(value);\n  };\n  thenable.reject = (reason) => {\n    finalize({\n      status: \"rejected\",\n      reason\n    });\n    reject(reason);\n  };\n  return thenable;\n}\nfunction tryResolveSync(promise) {\n  let data;\n  promise.then((result) => {\n    data = result;\n    return result;\n  }, noop)?.catch(noop);\n  if (data !== void 0) {\n    return { data };\n  }\n  return void 0;\n}\nexport {\n  pendingThenable,\n  tryResolveSync\n};\n//# sourceMappingURL=thenable.js.map", "// src/retryer.ts\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { pendingThenable } from \"./thenable.js\";\nimport { isServer, sleep } from \"./utils.js\";\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n  return (networkMode ?? \"online\") === \"online\" ? onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n  constructor(options) {\n    super(\"CancelledError\");\n    this.revert = options?.revert;\n    this.silent = options?.silent;\n  }\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let continueFn;\n  const thenable = pendingThenable();\n  const isResolved = () => thenable.status !== \"pending\";\n  const cancel = (cancelOptions) => {\n    if (!isResolved()) {\n      reject(new CancelledError(cancelOptions));\n      config.abort?.();\n    }\n  };\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n  const canContinue = () => focusManager.isFocused() && (config.networkMode === \"always\" || onlineManager.isOnline()) && config.canRun();\n  const canStart = () => canFetch(config.networkMode) && config.canRun();\n  const resolve = (value) => {\n    if (!isResolved()) {\n      continueFn?.();\n      thenable.resolve(value);\n    }\n  };\n  const reject = (value) => {\n    if (!isResolved()) {\n      continueFn?.();\n      thenable.reject(value);\n    }\n  };\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved() || canContinue()) {\n          continueResolve(value);\n        }\n      };\n      config.onPause?.();\n    }).then(() => {\n      continueFn = void 0;\n      if (!isResolved()) {\n        config.onContinue?.();\n      }\n    });\n  };\n  const run = () => {\n    if (isResolved()) {\n      return;\n    }\n    let promiseOrValue;\n    const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n    try {\n      promiseOrValue = initialPromise ?? config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n    Promise.resolve(promiseOrValue).then(resolve).catch((error) => {\n      if (isResolved()) {\n        return;\n      }\n      const retry = config.retry ?? (isServer ? 0 : 3);\n      const retryDelay = config.retryDelay ?? defaultRetryDelay;\n      const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n      if (isRetryCancelled || !shouldRetry) {\n        reject(error);\n        return;\n      }\n      failureCount++;\n      config.onFail?.(failureCount, error);\n      sleep(delay).then(() => {\n        return canContinue() ? void 0 : pause();\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  };\n  return {\n    promise: thenable,\n    status: () => thenable.status,\n    cancel,\n    continue: () => {\n      continueFn?.();\n      return thenable;\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      if (canStart()) {\n        run();\n      } else {\n        pause().then(run);\n      }\n      return thenable;\n    }\n  };\n}\nexport {\n  CancelledError,\n  canFetch,\n  createRetryer,\n  isCancelledError\n};\n//# sourceMappingURL=retryer.js.map", "// src/notifyManager.ts\nvar defaultScheduler = (cb) => setTimeout(cb, 0);\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n  let notifyFn = (callback) => {\n    callback();\n  };\n  let batchNotifyFn = (callback) => {\n    callback();\n  };\n  let scheduleFn = defaultScheduler;\n  const schedule = (callback) => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  return {\n    batch: (callback) => {\n      let result;\n      transactions++;\n      try {\n        result = callback();\n      } finally {\n        transactions--;\n        if (!transactions) {\n          flush();\n        }\n      }\n      return result;\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: (callback) => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args);\n        });\n      };\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: (fn) => {\n      notifyFn = fn;\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: (fn) => {\n      batchNotifyFn = fn;\n    },\n    setScheduler: (fn) => {\n      scheduleFn = fn;\n    }\n  };\n}\nvar notifyManager = createNotifyManager();\nexport {\n  createNotifyManager,\n  defaultScheduler,\n  notifyManager\n};\n//# sourceMappingURL=notifyManager.js.map", "// src/removable.ts\nimport { isServer, isValidTimeout } from \"./utils.js\";\nvar Removable = class {\n  #gcTimeout;\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if (isValidTimeout(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.gcTime);\n    }\n  }\n  updateGcTime(newGcTime) {\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (isServer ? Infinity : 5 * 60 * 1e3)\n    );\n  }\n  clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout);\n      this.#gcTimeout = void 0;\n    }\n  }\n};\nexport {\n  Removable\n};\n//# sourceMappingURL=removable.js.map", "// src/query.ts\nimport {\n  ensureQueryFn,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  skipToken,\n  timeUntilStale\n} from \"./utils.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { CancelledError, canFetch, createRetryer } from \"./retryer.js\";\nimport { Removable } from \"./removable.js\";\nvar Query = class extends Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #client;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#client = config.client;\n    this.#cache = this.#client.getQueryCache();\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = { ...this.#defaultOptions, ...options };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = replaceData(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({ type: \"setState\", state, setStateOptions });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({ silent: true });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(\n      (observer) => resolveEnabled(observer.options.enabled, this) !== false\n    );\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStatic() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => resolveStaleTime(observer.options.staleTime, this) === \"static\"\n      );\n    }\n    return false;\n  }\n  isStale() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale\n      );\n    }\n    return this.state.data === void 0 || this.state.isInvalidated;\n  }\n  isStaleByTime(staleTime = 0) {\n    if (this.state.data === void 0) {\n      return true;\n    }\n    if (staleTime === \"static\") {\n      return false;\n    }\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    return !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({ type: \"observerAdded\", query: this, observer });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({ type: \"observerRemoved\", query: this, observer });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: \"invalidate\" });\n    }\n  }\n  async fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\" && // If the promise in the retyer is already rejected, we have to definitely\n    // re-start the fetch; there is a chance that the query is still in a\n    // pending state when that happens\n    this.#retryer?.status() !== \"rejected\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({ silent: true });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`\n        );\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = (object) => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions);\n      const createQueryFnContext = () => {\n        const queryFnContext2 = {\n          client: this.#client,\n          queryKey: this.queryKey,\n          meta: this.meta\n        };\n        addSignalProperty(queryFnContext2);\n        return queryFnContext2;\n      };\n      const queryFnContext = createQueryFnContext();\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn,\n          queryFnContext,\n          this\n        );\n      }\n      return queryFn(queryFnContext);\n    };\n    const createFetchContext = () => {\n      const context2 = {\n        fetchOptions,\n        options: this.options,\n        queryKey: this.queryKey,\n        client: this.#client,\n        state: this.state,\n        fetchFn\n      };\n      addSignalProperty(context2);\n      return context2;\n    };\n    const context = createFetchContext();\n    this.options.behavior?.onFetch(context, this);\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({ type: \"fetch\", meta: context.fetchOptions?.meta });\n    }\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue: () => {\n        this.#dispatch({ type: \"continue\" });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    try {\n      const data = await this.#retryer.start();\n      if (data === void 0) {\n        if (process.env.NODE_ENV !== \"production\") {\n          console.error(\n            `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`\n          );\n        }\n        throw new Error(`${this.queryHash} data is undefined`);\n      }\n      this.setData(data);\n      this.#cache.config.onSuccess?.(data, this);\n      this.#cache.config.onSettled?.(\n        data,\n        this.state.error,\n        this\n      );\n      return data;\n    } catch (error) {\n      if (error instanceof CancelledError) {\n        if (error.silent) {\n          return this.#retryer.promise;\n        } else if (error.revert) {\n          this.setState({\n            ...this.#revertState,\n            fetchStatus: \"idle\"\n          });\n          return this.state.data;\n        }\n      }\n      this.#dispatch({\n        type: \"error\",\n        error\n      });\n      this.#cache.config.onError?.(\n        error,\n        this\n      );\n      this.#cache.config.onSettled?.(\n        this.state.data,\n        error,\n        this\n      );\n      throw error;\n    } finally {\n      this.scheduleGc();\n    }\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          const newState = {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            }\n          };\n          this.#revertState = action.manual ? newState : void 0;\n          return newState;\n        case \"error\":\n          const error = action.error;\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({ query: this, type: \"updated\", action });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? \"fetching\" : \"paused\",\n    ...data === void 0 && {\n      error: null,\n      status: \"pending\"\n    }\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\nexport {\n  Query,\n  fetchState\n};\n//# sourceMappingURL=query.js.map", "// src/queryCache.ts\nimport { hashQueryKeyByOptions, matchQuery } from \"./utils.js\";\nimport { Query } from \"./query.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar QueryCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#queries = /* @__PURE__ */ new Map();\n  }\n  #queries;\n  build(client, options, state) {\n    const queryKey = options.queryKey;\n    const queryHash = options.queryHash ?? hashQueryKeyByOptions(queryKey, options);\n    let query = this.get(queryHash);\n    if (!query) {\n      query = new Query({\n        client,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n    return query;\n  }\n  add(query) {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query);\n      this.notify({\n        type: \"added\",\n        query\n      });\n    }\n  }\n  remove(query) {\n    const queryInMap = this.#queries.get(query.queryHash);\n    if (queryInMap) {\n      query.destroy();\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash);\n      }\n      this.notify({ type: \"removed\", query });\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query);\n      });\n    });\n  }\n  get(queryHash) {\n    return this.#queries.get(queryHash);\n  }\n  getAll() {\n    return [...this.#queries.values()];\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (query) => matchQuery(defaultedFilters, query)\n    );\n  }\n  findAll(filters = {}) {\n    const queries = this.getAll();\n    return Object.keys(filters).length > 0 ? queries.filter((query) => matchQuery(filters, query)) : queries;\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  onFocus() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus();\n      });\n    });\n  }\n  onOnline() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline();\n      });\n    });\n  }\n};\nexport {\n  QueryCache\n};\n//# sourceMappingURL=queryCache.js.map", "// src/mutation.ts\nimport { notify<PERSON>anager } from \"./notifyManager.js\";\nimport { Removable } from \"./removable.js\";\nimport { createRetryer } from \"./retryer.js\";\nvar Mutation = class extends Removable {\n  #observers;\n  #mutationCache;\n  #retryer;\n  constructor(config) {\n    super();\n    this.mutationId = config.mutationId;\n    this.#mutationCache = config.mutationCache;\n    this.#observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n  setOptions(options) {\n    this.options = options;\n    this.updateGcTime(this.options.gcTime);\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  addObserver(observer) {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer);\n      this.clearGcTimeout();\n      this.#mutationCache.notify({\n        type: \"observerAdded\",\n        mutation: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    this.#observers = this.#observers.filter((x) => x !== observer);\n    this.scheduleGc();\n    this.#mutationCache.notify({\n      type: \"observerRemoved\",\n      mutation: this,\n      observer\n    });\n  }\n  optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === \"pending\") {\n        this.scheduleGc();\n      } else {\n        this.#mutationCache.remove(this);\n      }\n    }\n  }\n  continue() {\n    return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n    this.execute(this.state.variables);\n  }\n  async execute(variables) {\n    const onContinue = () => {\n      this.#dispatch({ type: \"continue\" });\n    };\n    this.#retryer = createRetryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error(\"No mutationFn found\"));\n        }\n        return this.options.mutationFn(variables);\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this)\n    });\n    const restored = this.state.status === \"pending\";\n    const isPaused = !this.#retryer.canStart();\n    try {\n      if (restored) {\n        onContinue();\n      } else {\n        this.#dispatch({ type: \"pending\", variables, isPaused });\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this\n        );\n        const context = await this.options.onMutate?.(variables);\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: \"pending\",\n            context,\n            variables,\n            isPaused\n          });\n        }\n      }\n      const data = await this.#retryer.start();\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSuccess?.(data, variables, this.state.context);\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSettled?.(data, null, variables, this.state.context);\n      this.#dispatch({ type: \"success\", data });\n      return data;\n    } catch (error) {\n      try {\n        await this.#mutationCache.config.onError?.(\n          error,\n          variables,\n          this.state.context,\n          this\n        );\n        await this.options.onError?.(\n          error,\n          variables,\n          this.state.context\n        );\n        await this.#mutationCache.config.onSettled?.(\n          void 0,\n          error,\n          this.state.variables,\n          this.state.context,\n          this\n        );\n        await this.options.onSettled?.(\n          void 0,\n          error,\n          variables,\n          this.state.context\n        );\n        throw error;\n      } finally {\n        this.#dispatch({ type: \"error\", error });\n      }\n    } finally {\n      this.#mutationCache.runNext(this);\n    }\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            isPaused: true\n          };\n        case \"continue\":\n          return {\n            ...state,\n            isPaused: false\n          };\n        case \"pending\":\n          return {\n            ...state,\n            context: action.context,\n            data: void 0,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: \"pending\",\n            variables: action.variables,\n            submittedAt: Date.now()\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: \"success\",\n            isPaused: false\n          };\n        case \"error\":\n          return {\n            ...state,\n            data: void 0,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: \"error\"\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action);\n      });\n      this.#mutationCache.notify({\n        mutation: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction getDefaultState() {\n  return {\n    context: void 0,\n    data: void 0,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: \"idle\",\n    variables: void 0,\n    submittedAt: 0\n  };\n}\nexport {\n  Mutation,\n  getDefaultState\n};\n//# sourceMappingURL=mutation.js.map", "// src/mutationCache.ts\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Mutation } from \"./mutation.js\";\nimport { matchMutation, noop } from \"./utils.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar MutationCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#mutations = /* @__PURE__ */ new Set();\n    this.#scopes = /* @__PURE__ */ new Map();\n    this.#mutationId = 0;\n  }\n  #mutations;\n  #scopes;\n  #mutationId;\n  build(client, options, state) {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state\n    });\n    this.add(mutation);\n    return mutation;\n  }\n  add(mutation) {\n    this.#mutations.add(mutation);\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const scopedMutations = this.#scopes.get(scope);\n      if (scopedMutations) {\n        scopedMutations.push(mutation);\n      } else {\n        this.#scopes.set(scope, [mutation]);\n      }\n    }\n    this.notify({ type: \"added\", mutation });\n  }\n  remove(mutation) {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation);\n      if (typeof scope === \"string\") {\n        const scopedMutations = this.#scopes.get(scope);\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation);\n            if (index !== -1) {\n              scopedMutations.splice(index, 1);\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope);\n          }\n        }\n      }\n    }\n    this.notify({ type: \"removed\", mutation });\n  }\n  canRun(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const mutationsWithSameScope = this.#scopes.get(scope);\n      const firstPendingMutation = mutationsWithSameScope?.find(\n        (m) => m.state.status === \"pending\"\n      );\n      return !firstPendingMutation || firstPendingMutation === mutation;\n    } else {\n      return true;\n    }\n  }\n  runNext(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const foundMutation = this.#scopes.get(scope)?.find((m) => m !== mutation && m.state.isPaused);\n      return foundMutation?.continue() ?? Promise.resolve();\n    } else {\n      return Promise.resolve();\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.#mutations.forEach((mutation) => {\n        this.notify({ type: \"removed\", mutation });\n      });\n      this.#mutations.clear();\n      this.#scopes.clear();\n    });\n  }\n  getAll() {\n    return Array.from(this.#mutations);\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (mutation) => matchMutation(defaultedFilters, mutation)\n    );\n  }\n  findAll(filters = {}) {\n    return this.getAll().filter((mutation) => matchMutation(filters, mutation));\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  resumePausedMutations() {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused);\n    return notifyManager.batch(\n      () => Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(noop))\n      )\n    );\n  }\n};\nfunction scopeFor(mutation) {\n  return mutation.options.scope?.id;\n}\nexport {\n  MutationCache\n};\n//# sourceMappingURL=mutationCache.js.map", "// src/infiniteQueryBehavior.ts\nimport { addToEnd, addToStart, ensureQueryFn } from \"./utils.js\";\nfunction infiniteQueryBehavior(pages) {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options;\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n      const oldPages = context.state.data?.pages || [];\n      const oldPageParams = context.state.data?.pageParams || [];\n      let result = { pages: [], pageParams: [] };\n      let currentPage = 0;\n      const fetchFn = async () => {\n        let cancelled = false;\n        const addSignalProperty = (object) => {\n          Object.defineProperty(object, \"signal\", {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true;\n              } else {\n                context.signal.addEventListener(\"abort\", () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        };\n        const queryFn = ensureQueryFn(context.options, context.fetchOptions);\n        const fetchPage = async (data, param, previous) => {\n          if (cancelled) {\n            return Promise.reject();\n          }\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data);\n          }\n          const createQueryFnContext = () => {\n            const queryFnContext2 = {\n              client: context.client,\n              queryKey: context.queryKey,\n              pageParam: param,\n              direction: previous ? \"backward\" : \"forward\",\n              meta: context.options.meta\n            };\n            addSignalProperty(queryFnContext2);\n            return queryFnContext2;\n          };\n          const queryFnContext = createQueryFnContext();\n          const page = await queryFn(queryFnContext);\n          const { maxPages } = context.options;\n          const addTo = previous ? addToStart : addToEnd;\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          };\n        };\n        if (direction && oldPages.length) {\n          const previous = direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams\n          };\n          const param = pageParamFn(options, oldData);\n          result = await fetchPage(oldData, param, previous);\n        } else {\n          const remainingPages = pages ?? oldPages.length;\n          do {\n            const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n            if (currentPage > 0 && param == null) {\n              break;\n            }\n            result = await fetchPage(result, param);\n            currentPage++;\n          } while (currentPage < remainingPages);\n        }\n        return result;\n      };\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn,\n            {\n              client: context.client,\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal\n            },\n            query\n          );\n        };\n      } else {\n        context.fetchFn = fetchFn;\n      }\n    }\n  };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n  const lastIndex = pages.length - 1;\n  return pages.length > 0 ? options.getNextPageParam(\n    pages[lastIndex],\n    pages,\n    pageParams[lastIndex],\n    pageParams\n  ) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n  return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n  if (!data) return false;\n  return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n  if (!data || !options.getPreviousPageParam) return false;\n  return getPreviousPageParam(options, data) != null;\n}\nexport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior\n};\n//# sourceMappingURL=infiniteQueryBehavior.js.map", "// src/queryClient.ts\nimport {\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  noop,\n  partialMatchKey,\n  resolveStaleTime,\n  skipToken\n} from \"./utils.js\";\nimport { QueryCache } from \"./queryCache.js\";\nimport { MutationCache } from \"./mutationCache.js\";\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { infiniteQueryBehavior } from \"./infiniteQueryBehavior.js\";\nvar QueryClient = class {\n  #queryCache;\n  #mutationCache;\n  #defaultOptions;\n  #queryDefaults;\n  #mutationDefaults;\n  #mountCount;\n  #unsubscribeFocus;\n  #unsubscribeOnline;\n  constructor(config = {}) {\n    this.#queryCache = config.queryCache || new QueryCache();\n    this.#mutationCache = config.mutationCache || new MutationCache();\n    this.#defaultOptions = config.defaultOptions || {};\n    this.#queryDefaults = /* @__PURE__ */ new Map();\n    this.#mutationDefaults = /* @__PURE__ */ new Map();\n    this.#mountCount = 0;\n  }\n  mount() {\n    this.#mountCount++;\n    if (this.#mountCount !== 1) return;\n    this.#unsubscribeFocus = focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations();\n        this.#queryCache.onFocus();\n      }\n    });\n    this.#unsubscribeOnline = onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations();\n        this.#queryCache.onOnline();\n      }\n    });\n  }\n  unmount() {\n    this.#mountCount--;\n    if (this.#mountCount !== 0) return;\n    this.#unsubscribeFocus?.();\n    this.#unsubscribeFocus = void 0;\n    this.#unsubscribeOnline?.();\n    this.#unsubscribeOnline = void 0;\n  }\n  isFetching(filters) {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: \"fetching\" }).length;\n  }\n  isMutating(filters) {\n    return this.#mutationCache.findAll({ ...filters, status: \"pending\" }).length;\n  }\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state.data;\n  }\n  ensureQueryData(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    const query = this.#queryCache.build(this, defaultedOptions);\n    const cachedData = query.state.data;\n    if (cachedData === void 0) {\n      return this.fetchQuery(options);\n    }\n    if (options.revalidateIfStale && query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query))) {\n      void this.prefetchQuery(defaultedOptions);\n    }\n    return Promise.resolve(cachedData);\n  }\n  getQueriesData(filters) {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n  setQueryData(queryKey, updater, options) {\n    const defaultedOptions = this.defaultQueryOptions({ queryKey });\n    const query = this.#queryCache.get(\n      defaultedOptions.queryHash\n    );\n    const prevData = query?.state.data;\n    const data = functionalUpdate(updater, prevData);\n    if (data === void 0) {\n      return void 0;\n    }\n    return this.#queryCache.build(this, defaultedOptions).setData(data, { ...options, manual: true });\n  }\n  setQueriesData(filters, updater, options) {\n    return notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map(({ queryKey }) => [\n        queryKey,\n        this.setQueryData(queryKey, updater, options)\n      ])\n    );\n  }\n  getQueryState(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(\n      options.queryHash\n    )?.state;\n  }\n  removeQueries(filters) {\n    const queryCache = this.#queryCache;\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query);\n      });\n    });\n  }\n  resetQueries(filters, options) {\n    const queryCache = this.#queryCache;\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset();\n      });\n      return this.refetchQueries(\n        {\n          type: \"active\",\n          ...filters\n        },\n        options\n      );\n    });\n  }\n  cancelQueries(filters, cancelOptions = {}) {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions };\n    const promises = notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map((query) => query.cancel(defaultedCancelOptions))\n    );\n    return Promise.all(promises).then(noop).catch(noop);\n  }\n  invalidateQueries(filters, options = {}) {\n    return notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate();\n      });\n      if (filters?.refetchType === \"none\") {\n        return Promise.resolve();\n      }\n      return this.refetchQueries(\n        {\n          ...filters,\n          type: filters?.refetchType ?? filters?.type ?? \"active\"\n        },\n        options\n      );\n    });\n  }\n  refetchQueries(filters, options = {}) {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true\n    };\n    const promises = notifyManager.batch(\n      () => this.#queryCache.findAll(filters).filter((query) => !query.isDisabled() && !query.isStatic()).map((query) => {\n        let promise = query.fetch(void 0, fetchOptions);\n        if (!fetchOptions.throwOnError) {\n          promise = promise.catch(noop);\n        }\n        return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n      })\n    );\n    return Promise.all(promises).then(noop);\n  }\n  fetchQuery(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    if (defaultedOptions.retry === void 0) {\n      defaultedOptions.retry = false;\n    }\n    const query = this.#queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(\n      resolveStaleTime(defaultedOptions.staleTime, query)\n    ) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n  prefetchQuery(options) {\n    return this.fetchQuery(options).then(noop).catch(noop);\n  }\n  fetchInfiniteQuery(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.fetchQuery(options);\n  }\n  prefetchInfiniteQuery(options) {\n    return this.fetchInfiniteQuery(options).then(noop).catch(noop);\n  }\n  ensureInfiniteQueryData(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.ensureQueryData(options);\n  }\n  resumePausedMutations() {\n    if (onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations();\n    }\n    return Promise.resolve();\n  }\n  getQueryCache() {\n    return this.#queryCache;\n  }\n  getMutationCache() {\n    return this.#mutationCache;\n  }\n  getDefaultOptions() {\n    return this.#defaultOptions;\n  }\n  setDefaultOptions(options) {\n    this.#defaultOptions = options;\n  }\n  setQueryDefaults(queryKey, options) {\n    this.#queryDefaults.set(hashKey(queryKey), {\n      queryKey,\n      defaultOptions: options\n    });\n  }\n  getQueryDefaults(queryKey) {\n    const defaults = [...this.#queryDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  setMutationDefaults(mutationKey, options) {\n    this.#mutationDefaults.set(hashKey(mutationKey), {\n      mutationKey,\n      defaultOptions: options\n    });\n  }\n  getMutationDefaults(mutationKey) {\n    const defaults = [...this.#mutationDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  defaultQueryOptions(options) {\n    if (options._defaulted) {\n      return options;\n    }\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions\n      );\n    }\n    if (defaultedOptions.refetchOnReconnect === void 0) {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n    }\n    if (defaultedOptions.throwOnError === void 0) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n    }\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = \"offlineFirst\";\n    }\n    if (defaultedOptions.queryFn === skipToken) {\n      defaultedOptions.enabled = false;\n    }\n    return defaultedOptions;\n  }\n  defaultMutationOptions(options) {\n    if (options?._defaulted) {\n      return options;\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n  clear() {\n    this.#queryCache.clear();\n    this.#mutationCache.clear();\n  }\n};\nexport {\n  QueryClient\n};\n//# sourceMappingURL=queryClient.js.map", "// src/queryObserver.ts\nimport { focusManager } from \"./focusManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { fetchState } from \"./query.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { pendingThenable } from \"./thenable.js\";\nimport {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  timeUntilStale\n} from \"./utils.js\";\nvar QueryObserver = class extends Subscribable {\n  constructor(client, options) {\n    super();\n    this.options = options;\n    this.#client = client;\n    this.#selectError = null;\n    this.#currentThenable = pendingThenable();\n    if (!this.options.experimental_prefetchInRender) {\n      this.#currentThenable.reject(\n        new Error(\"experimental_prefetchInRender feature flag is not enabled\")\n      );\n    }\n    this.bindMethods();\n    this.setOptions(options);\n  }\n  #client;\n  #currentQuery = void 0;\n  #currentQueryInitialState = void 0;\n  #currentResult = void 0;\n  #currentResultState;\n  #currentResultOptions;\n  #currentThenable;\n  #selectError;\n  #selectFn;\n  #selectResult;\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData;\n  #staleTimeoutId;\n  #refetchIntervalId;\n  #currentRefetchInterval;\n  #trackedProps = /* @__PURE__ */ new Set();\n  bindMethods() {\n    this.refetch = this.refetch.bind(this);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this);\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch();\n      } else {\n        this.updateResult();\n      }\n      this.#updateTimers();\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnReconnect\n    );\n  }\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus\n    );\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.#clearStaleTimeout();\n    this.#clearRefetchInterval();\n    this.#currentQuery.removeObserver(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    const prevQuery = this.#currentQuery;\n    this.options = this.#client.defaultQueryOptions(options);\n    if (this.options.enabled !== void 0 && typeof this.options.enabled !== \"boolean\" && typeof this.options.enabled !== \"function\" && typeof resolveEnabled(this.options.enabled, this.#currentQuery) !== \"boolean\") {\n      throw new Error(\n        \"Expected enabled to be a boolean or a callback that returns a boolean\"\n      );\n    }\n    this.#updateQuery();\n    this.#currentQuery.setOptions(this.options);\n    if (prevOptions._defaulted && !shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getQueryCache().notify({\n        type: \"observerOptionsUpdated\",\n        query: this.#currentQuery,\n        observer: this\n      });\n    }\n    const mounted = this.hasListeners();\n    if (mounted && shouldFetchOptionally(\n      this.#currentQuery,\n      prevQuery,\n      this.options,\n      prevOptions\n    )) {\n      this.#executeFetch();\n    }\n    this.updateResult();\n    if (mounted && (this.#currentQuery !== prevQuery || resolveEnabled(this.options.enabled, this.#currentQuery) !== resolveEnabled(prevOptions.enabled, this.#currentQuery) || resolveStaleTime(this.options.staleTime, this.#currentQuery) !== resolveStaleTime(prevOptions.staleTime, this.#currentQuery))) {\n      this.#updateStaleTimeout();\n    }\n    const nextRefetchInterval = this.#computeRefetchInterval();\n    if (mounted && (this.#currentQuery !== prevQuery || resolveEnabled(this.options.enabled, this.#currentQuery) !== resolveEnabled(prevOptions.enabled, this.#currentQuery) || nextRefetchInterval !== this.#currentRefetchInterval)) {\n      this.#updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n  getOptimisticResult(options) {\n    const query = this.#client.getQueryCache().build(this.#client, options);\n    const result = this.createResult(query, options);\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      this.#currentResult = result;\n      this.#currentResultOptions = this.options;\n      this.#currentResultState = this.#currentQuery.state;\n    }\n    return result;\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  trackResult(result, onPropTracked) {\n    return new Proxy(result, {\n      get: (target, key) => {\n        this.trackProp(key);\n        onPropTracked?.(key);\n        return Reflect.get(target, key);\n      }\n    });\n  }\n  trackProp(key) {\n    this.#trackedProps.add(key);\n  }\n  getCurrentQuery() {\n    return this.#currentQuery;\n  }\n  refetch({ ...options } = {}) {\n    return this.fetch({\n      ...options\n    });\n  }\n  fetchOptimistic(options) {\n    const defaultedOptions = this.#client.defaultQueryOptions(options);\n    const query = this.#client.getQueryCache().build(this.#client, defaultedOptions);\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n  fetch(fetchOptions) {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true\n    }).then(() => {\n      this.updateResult();\n      return this.#currentResult;\n    });\n  }\n  #executeFetch(fetchOptions) {\n    this.#updateQuery();\n    let promise = this.#currentQuery.fetch(\n      this.options,\n      fetchOptions\n    );\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop);\n    }\n    return promise;\n  }\n  #updateStaleTimeout() {\n    this.#clearStaleTimeout();\n    const staleTime = resolveStaleTime(\n      this.options.staleTime,\n      this.#currentQuery\n    );\n    if (isServer || this.#currentResult.isStale || !isValidTimeout(staleTime)) {\n      return;\n    }\n    const time = timeUntilStale(this.#currentResult.dataUpdatedAt, staleTime);\n    const timeout = time + 1;\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n  #computeRefetchInterval() {\n    return (typeof this.options.refetchInterval === \"function\" ? this.options.refetchInterval(this.#currentQuery) : this.options.refetchInterval) ?? false;\n  }\n  #updateRefetchInterval(nextInterval) {\n    this.#clearRefetchInterval();\n    this.#currentRefetchInterval = nextInterval;\n    if (isServer || resolveEnabled(this.options.enabled, this.#currentQuery) === false || !isValidTimeout(this.#currentRefetchInterval) || this.#currentRefetchInterval === 0) {\n      return;\n    }\n    this.#refetchIntervalId = setInterval(() => {\n      if (this.options.refetchIntervalInBackground || focusManager.isFocused()) {\n        this.#executeFetch();\n      }\n    }, this.#currentRefetchInterval);\n  }\n  #updateTimers() {\n    this.#updateStaleTimeout();\n    this.#updateRefetchInterval(this.#computeRefetchInterval());\n  }\n  #clearStaleTimeout() {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId);\n      this.#staleTimeoutId = void 0;\n    }\n  }\n  #clearRefetchInterval() {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId);\n      this.#refetchIntervalId = void 0;\n    }\n  }\n  createResult(query, options) {\n    const prevQuery = this.#currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.#currentResult;\n    const prevResultState = this.#currentResultState;\n    const prevResultOptions = this.#currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.#currentQueryInitialState;\n    const { state } = query;\n    let newState = { ...state };\n    let isPlaceholderData = false;\n    let data;\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...fetchState(state.data, query.options)\n        };\n      }\n      if (options._optimisticResults === \"isRestoring\") {\n        newState.fetchStatus = \"idle\";\n      }\n    }\n    let { error, errorUpdatedAt, status } = newState;\n    data = newState.data;\n    let skipSelect = false;\n    if (options.placeholderData !== void 0 && data === void 0 && status === \"pending\") {\n      let placeholderData;\n      if (prevResult?.isPlaceholderData && options.placeholderData === prevResultOptions?.placeholderData) {\n        placeholderData = prevResult.data;\n        skipSelect = true;\n      } else {\n        placeholderData = typeof options.placeholderData === \"function\" ? options.placeholderData(\n          this.#lastQueryWithDefinedData?.state.data,\n          this.#lastQueryWithDefinedData\n        ) : options.placeholderData;\n      }\n      if (placeholderData !== void 0) {\n        status = \"success\";\n        data = replaceData(\n          prevResult?.data,\n          placeholderData,\n          options\n        );\n        isPlaceholderData = true;\n      }\n    }\n    if (options.select && data !== void 0 && !skipSelect) {\n      if (prevResult && data === prevResultState?.data && options.select === this.#selectFn) {\n        data = this.#selectResult;\n      } else {\n        try {\n          this.#selectFn = options.select;\n          data = options.select(data);\n          data = replaceData(prevResult?.data, data, options);\n          this.#selectResult = data;\n          this.#selectError = null;\n        } catch (selectError) {\n          this.#selectError = selectError;\n        }\n      }\n    }\n    if (this.#selectError) {\n      error = this.#selectError;\n      data = this.#selectResult;\n      errorUpdatedAt = Date.now();\n      status = \"error\";\n    }\n    const isFetching = newState.fetchStatus === \"fetching\";\n    const isPending = status === \"pending\";\n    const isError = status === \"error\";\n    const isLoading = isPending && isFetching;\n    const hasData = data !== void 0;\n    const result = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === \"success\",\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount: newState.dataUpdateCount > queryInitialState.dataUpdateCount || newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === \"paused\",\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable,\n      isEnabled: resolveEnabled(options.enabled, query) !== false\n    };\n    const nextResult = result;\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = (thenable) => {\n        if (nextResult.status === \"error\") {\n          thenable.reject(nextResult.error);\n        } else if (nextResult.data !== void 0) {\n          thenable.resolve(nextResult.data);\n        }\n      };\n      const recreateThenable = () => {\n        const pending = this.#currentThenable = nextResult.promise = pendingThenable();\n        finalizeThenableIfPossible(pending);\n      };\n      const prevThenable = this.#currentThenable;\n      switch (prevThenable.status) {\n        case \"pending\":\n          if (query.queryHash === prevQuery.queryHash) {\n            finalizeThenableIfPossible(prevThenable);\n          }\n          break;\n        case \"fulfilled\":\n          if (nextResult.status === \"error\" || nextResult.data !== prevThenable.value) {\n            recreateThenable();\n          }\n          break;\n        case \"rejected\":\n          if (nextResult.status !== \"error\" || nextResult.error !== prevThenable.reason) {\n            recreateThenable();\n          }\n          break;\n      }\n    }\n    return nextResult;\n  }\n  updateResult() {\n    const prevResult = this.#currentResult;\n    const nextResult = this.createResult(this.#currentQuery, this.options);\n    this.#currentResultState = this.#currentQuery.state;\n    this.#currentResultOptions = this.options;\n    if (this.#currentResultState.data !== void 0) {\n      this.#lastQueryWithDefinedData = this.#currentQuery;\n    }\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return;\n    }\n    this.#currentResult = nextResult;\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n      const { notifyOnChangeProps } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === \"function\" ? notifyOnChangeProps() : notifyOnChangeProps;\n      if (notifyOnChangePropsValue === \"all\" || !notifyOnChangePropsValue && !this.#trackedProps.size) {\n        return true;\n      }\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.#trackedProps\n      );\n      if (this.options.throwOnError) {\n        includedProps.add(\"error\");\n      }\n      return Object.keys(this.#currentResult).some((key) => {\n        const typedKey = key;\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n    this.#notify({ listeners: shouldNotifyListeners() });\n  }\n  #updateQuery() {\n    const query = this.#client.getQueryCache().build(this.#client, this.options);\n    if (query === this.#currentQuery) {\n      return;\n    }\n    const prevQuery = this.#currentQuery;\n    this.#currentQuery = query;\n    this.#currentQueryInitialState = query.state;\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n  onQueryUpdate() {\n    this.updateResult();\n    if (this.hasListeners()) {\n      this.#updateTimers();\n    }\n  }\n  #notify(notifyOptions) {\n    notifyManager.batch(() => {\n      if (notifyOptions.listeners) {\n        this.listeners.forEach((listener) => {\n          listener(this.#currentResult);\n        });\n      }\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: \"observerResultsUpdated\"\n      });\n    });\n  }\n};\nfunction shouldLoadOnMount(query, options) {\n  return resolveEnabled(options.enabled, query) !== false && query.state.data === void 0 && !(query.state.status === \"error\" && options.retryOnMount === false);\n}\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.data !== void 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\nfunction shouldFetchOn(query, options, field) {\n  if (resolveEnabled(options.enabled, query) !== false && resolveStaleTime(options.staleTime, query) !== \"static\") {\n    const value = typeof field === \"function\" ? field(query) : field;\n    return value === \"always\" || value !== false && isStale(query, options);\n  }\n  return false;\n}\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return (query !== prevQuery || resolveEnabled(prevOptions.enabled, query) === false) && (!options.suspense || query.state.status !== \"error\") && isStale(query, options);\n}\nfunction isStale(query, options) {\n  return resolveEnabled(options.enabled, query) !== false && query.isStaleByTime(resolveStaleTime(options.staleTime, query));\n}\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult) {\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  }\n  return false;\n}\nexport {\n  QueryObserver\n};\n//# sourceMappingURL=queryObserver.js.map", "// src/mutationObserver.ts\nimport { getDefaultState } from \"./mutation.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { hashKey, shallowEqualObjects } from \"./utils.js\";\nvar MutationObserver = class extends Subscribable {\n  #client;\n  #currentResult = void 0;\n  #currentMutation;\n  #mutateOptions;\n  constructor(client, options) {\n    super();\n    this.#client = client;\n    this.setOptions(options);\n    this.bindMethods();\n    this.#updateResult();\n  }\n  bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    this.options = this.#client.defaultMutationOptions(options);\n    if (!shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: \"observerOptionsUpdated\",\n        mutation: this.#currentMutation,\n        observer: this\n      });\n    }\n    if (prevOptions?.mutationKey && this.options.mutationKey && hashKey(prevOptions.mutationKey) !== hashKey(this.options.mutationKey)) {\n      this.reset();\n    } else if (this.#currentMutation?.state.status === \"pending\") {\n      this.#currentMutation.setOptions(this.options);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this);\n    }\n  }\n  onMutationUpdate(action) {\n    this.#updateResult();\n    this.#notify(action);\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  reset() {\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = void 0;\n    this.#updateResult();\n    this.#notify();\n  }\n  mutate(variables, options) {\n    this.#mutateOptions = options;\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = this.#client.getMutationCache().build(this.#client, this.options);\n    this.#currentMutation.addObserver(this);\n    return this.#currentMutation.execute(variables);\n  }\n  #updateResult() {\n    const state = this.#currentMutation?.state ?? getDefaultState();\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === \"pending\",\n      isSuccess: state.status === \"success\",\n      isError: state.status === \"error\",\n      isIdle: state.status === \"idle\",\n      mutate: this.mutate,\n      reset: this.reset\n    };\n  }\n  #notify(action) {\n    notifyManager.batch(() => {\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables;\n        const context = this.#currentResult.context;\n        if (action?.type === \"success\") {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context);\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context);\n        } else if (action?.type === \"error\") {\n          this.#mutateOptions.onError?.(action.error, variables, context);\n          this.#mutateOptions.onSettled?.(\n            void 0,\n            action.error,\n            variables,\n            context\n          );\n        }\n      }\n      this.listeners.forEach((listener) => {\n        listener(this.#currentResult);\n      });\n    });\n  }\n};\nexport {\n  MutationObserver\n};\n//# sourceMappingURL=mutationObserver.js.map", "\"use client\";\n\n// src/QueryClientProvider.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar QueryClientContext = React.createContext(\n  void 0\n);\nvar useQueryClient = (queryClient) => {\n  const client = React.useContext(QueryClientContext);\n  if (queryClient) {\n    return queryClient;\n  }\n  if (!client) {\n    throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n  }\n  return client;\n};\nvar QueryClientProvider = ({\n  client,\n  children\n}) => {\n  React.useEffect(() => {\n    client.mount();\n    return () => {\n      client.unmount();\n    };\n  }, [client]);\n  return /* @__PURE__ */ jsx(QueryClientContext.Provider, { value: client, children });\n};\nexport {\n  QueryClientContext,\n  QueryClientProvider,\n  useQueryClient\n};\n//# sourceMappingURL=QueryClientProvider.js.map", "\"use client\";\n\n// src/IsRestoringProvider.ts\nimport * as React from \"react\";\nvar IsRestoringContext = React.createContext(false);\nvar useIsRestoring = () => React.useContext(IsRestoringContext);\nvar IsRestoringProvider = IsRestoringContext.Provider;\nexport {\n  IsRestoringProvider,\n  useIsRestoring\n};\n//# sourceMappingURL=IsRestoringProvider.js.map", "\"use client\";\n\n// src/QueryErrorResetBoundary.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createValue() {\n  let isReset = false;\n  return {\n    clearReset: () => {\n      isReset = false;\n    },\n    reset: () => {\n      isReset = true;\n    },\n    isReset: () => {\n      return isReset;\n    }\n  };\n}\nvar QueryErrorResetBoundaryContext = React.createContext(createValue());\nvar useQueryErrorResetBoundary = () => React.useContext(QueryErrorResetBoundaryContext);\nvar QueryErrorResetBoundary = ({\n  children\n}) => {\n  const [value] = React.useState(() => createValue());\n  return /* @__PURE__ */ jsx(QueryErrorResetBoundaryContext.Provider, { value, children: typeof children === \"function\" ? children(value) : children });\n};\nexport {\n  QueryErrorResetBoundary,\n  useQueryErrorResetBoundary\n};\n//# sourceMappingURL=QueryErrorResetBoundary.js.map", "\"use client\";\n\n// src/errorBoundaryUtils.ts\nimport * as React from \"react\";\nimport { shouldThrowError } from \"@tanstack/query-core\";\nvar ensurePreventErrorBoundaryRetry = (options, errorResetBoundary) => {\n  if (options.suspense || options.throwOnError || options.experimental_prefetchInRender) {\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false;\n    }\n  }\n};\nvar useClearResetErrorBoundary = (errorResetBoundary) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset();\n  }, [errorResetBoundary]);\n};\nvar getHasError = ({\n  result,\n  errorResetBoundary,\n  throwOnError,\n  query,\n  suspense\n}) => {\n  return result.isError && !errorResetBoundary.isReset() && !result.isFetching && query && (suspense && result.data === void 0 || shouldThrowError(throwOnError, [result.error, query]));\n};\nexport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary\n};\n//# sourceMappingURL=errorBoundaryUtils.js.map", "// src/suspense.ts\nvar defaultThrowOnError = (_error, query) => query.state.data === void 0;\nvar ensureSuspenseTimers = (defaultedOptions) => {\n  if (defaultedOptions.suspense) {\n    const clamp = (value) => value === \"static\" ? value : Math.max(value ?? 1e3, 1e3);\n    const originalStaleTime = defaultedOptions.staleTime;\n    defaultedOptions.staleTime = typeof originalStaleTime === \"function\" ? (...args) => clamp(originalStaleTime(...args)) : clamp(originalStaleTime);\n    if (typeof defaultedOptions.gcTime === \"number\") {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1e3);\n    }\n  }\n};\nvar willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;\nvar shouldSuspend = (defaultedOptions, result) => defaultedOptions?.suspense && result.isPending;\nvar fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).catch(() => {\n  errorResetBoundary.clearReset();\n});\nexport {\n  defaultThrowOnError,\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch\n};\n//# sourceMappingURL=suspense.js.map", "\"use client\";\n\n// src/useBaseQuery.ts\nimport * as React from \"react\";\nimport { isServer, noop, notify<PERSON>anager } from \"@tanstack/query-core\";\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nimport { useQueryErrorResetBoundary } from \"./QueryErrorResetBoundary.js\";\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary\n} from \"./errorBoundaryUtils.js\";\nimport { useIsRestoring } from \"./IsRestoringProvider.js\";\nimport {\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch\n} from \"./suspense.js\";\nfunction useBaseQuery(options, Observer, queryClient) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof options !== \"object\" || Array.isArray(options)) {\n      throw new Error(\n        'Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object'\n      );\n    }\n  }\n  const isRestoring = useIsRestoring();\n  const errorResetBoundary = useQueryErrorResetBoundary();\n  const client = useQueryClient(queryClient);\n  const defaultedOptions = client.defaultQueryOptions(options);\n  client.getDefaultOptions().queries?._experimental_beforeQuery?.(\n    defaultedOptions\n  );\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!defaultedOptions.queryFn) {\n      console.error(\n        `[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`\n      );\n    }\n  }\n  defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n  ensureSuspenseTimers(defaultedOptions);\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary);\n  useClearResetErrorBoundary(errorResetBoundary);\n  const isNewCacheEntry = !client.getQueryCache().get(defaultedOptions.queryHash);\n  const [observer] = React.useState(\n    () => new Observer(\n      client,\n      defaultedOptions\n    )\n  );\n  const result = observer.getOptimisticResult(defaultedOptions);\n  const shouldSubscribe = !isRestoring && options.subscribed !== false;\n  React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = shouldSubscribe ? observer.subscribe(notifyManager.batchCalls(onStoreChange)) : noop;\n        observer.updateResult();\n        return unsubscribe;\n      },\n      [observer, shouldSubscribe]\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult()\n  );\n  React.useEffect(() => {\n    observer.setOptions(defaultedOptions);\n  }, [defaultedOptions, observer]);\n  if (shouldSuspend(defaultedOptions, result)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary);\n  }\n  if (getHasError({\n    result,\n    errorResetBoundary,\n    throwOnError: defaultedOptions.throwOnError,\n    query: client.getQueryCache().get(defaultedOptions.queryHash),\n    suspense: defaultedOptions.suspense\n  })) {\n    throw result.error;\n  }\n  ;\n  client.getDefaultOptions().queries?._experimental_afterQuery?.(\n    defaultedOptions,\n    result\n  );\n  if (defaultedOptions.experimental_prefetchInRender && !isServer && willFetch(result, isRestoring)) {\n    const promise = isNewCacheEntry ? (\n      // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n      fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n    ) : (\n      // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n      client.getQueryCache().get(defaultedOptions.queryHash)?.promise\n    );\n    promise?.catch(noop).finally(() => {\n      observer.updateResult();\n    });\n  }\n  return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\nexport {\n  useBaseQuery\n};\n//# sourceMappingURL=useBaseQuery.js.map", "\"use client\";\n\n// src/useQuery.ts\nimport { QueryObserver } from \"@tanstack/query-core\";\nimport { useBaseQuery } from \"./useBaseQuery.js\";\nfunction useQuery(options, queryClient) {\n  return useBaseQuery(options, QueryObserver, queryClient);\n}\nexport {\n  useQuery\n};\n//# sourceMappingURL=useQuery.js.map", "\"use client\";\n\n// src/useMutation.ts\nimport * as React from \"react\";\nimport {\n  MutationObserver,\n  noop,\n  notify<PERSON><PERSON><PERSON>,\n  shouldThrowError\n} from \"@tanstack/query-core\";\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nfunction useMutation(options, queryClient) {\n  const client = useQueryClient(queryClient);\n  const [observer] = React.useState(\n    () => new MutationObserver(\n      client,\n      options\n    )\n  );\n  React.useEffect(() => {\n    observer.setOptions(options);\n  }, [observer, options]);\n  const result = React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer]\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult()\n  );\n  const mutate = React.useCallback(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop);\n    },\n    [observer]\n  );\n  if (result.error && shouldThrowError(observer.options.throwOnError, [result.error])) {\n    throw result.error;\n  }\n  return { ...result, mutate, mutateAsync: result.mutate };\n}\nexport {\n  useMutation\n};\n//# sourceMappingURL=useMutation.js.map"], "names": ["f", "require$$0", "k", "l", "m", "n", "p", "q", "c", "a", "g", "b", "d", "e", "h", "reactJsxRuntime_production_min", "jsxRuntimeModule", "Subscribable", "listener", "isServer", "noop", "functionalUpdate", "updater", "input", "isValidTimeout", "value", "timeUntilStale", "updatedAt", "staleTime", "resolveStaleTime", "query", "resolveEnabled", "enabled", "matchQuery", "filters", "type", "exact", "fetchStatus", "predicate", "query<PERSON><PERSON>", "stale", "hashQueryKeyByOptions", "partialMatchKey", "isActive", "matchMutation", "mutation", "status", "<PERSON><PERSON><PERSON>", "hash<PERSON><PERSON>", "options", "_", "val", "isPlainObject", "result", "key", "replaceEqualDeep", "array", "is<PERSON><PERSON>A<PERSON>y", "aItems", "aSize", "bItems", "bSize", "copy", "aItemsSet", "equalItems", "i", "shallowEqualObjects", "o", "hasObjectPrototype", "ctor", "prot", "sleep", "timeout", "resolve", "replaceData", "prevData", "data", "addToEnd", "items", "item", "max", "newItems", "addToStart", "skipToken", "ensureQueryFn", "fetchOptions", "shouldThrowError", "throwOnError", "params", "FocusManager", "_a", "__privateAdd", "_focused", "_cleanup", "_setup", "__privateSet", "onFocus", "__privateGet", "setup", "focused", "isFocused", "focusManager", "OnlineManager", "_online", "onOnline", "onlineListener", "offlineListener", "online", "onlineManager", "pendingThenable", "reject", "thenable", "_resolve", "_reject", "finalize", "reason", "defaultRetryDelay", "failureCount", "canFetch", "networkMode", "CancelledError", "createRetryer", "config", "isRetryCancelled", "continueFn", "isResolved", "cancel", "cancelOptions", "cancelRetry", "continueRetry", "canContinue", "canStart", "pause", "continueResolve", "run", "promiseOrValue", "initialPromise", "error", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "defaultScheduler", "cb", "createNotifyManager", "queue", "transactions", "notifyFn", "callback", "batchNotifyFn", "scheduleFn", "schedule", "flush", "originalQueue", "args", "fn", "notify<PERSON><PERSON>ger", "Removable", "_gcTimeout", "newGcTime", "Query", "_Query_instances", "_initialState", "_revertState", "_cache", "_client", "_retryer", "_defaultOptions", "_abortSignalConsumed", "getDefaultState", "newData", "__privateMethod", "dispatch_fn", "state", "setStateOptions", "promise", "_b", "observer", "x", "abortController", "addSignalProperty", "object", "fetchFn", "queryFn", "queryFnContext", "queryFnContext2", "context", "context2", "_c", "_d", "_f", "_e", "_h", "_g", "_j", "_i", "_l", "_k", "action", "reducer", "fetchState", "newState", "hasData", "initialDataUpdatedAt", "Query<PERSON>ache", "_queries", "client", "queryHash", "queryInMap", "defaultedFilters", "queries", "event", "Mutation", "_Mutation_instances", "_observers", "_mutationCache", "variables", "onContinue", "restored", "isPaused", "_n", "_m", "_p", "_o", "_r", "_q", "_t", "_s", "MutationCache", "_mutations", "_scopes", "_mutationId", "__privateWrapper", "scope", "scopeFor", "scopedMutations", "index", "mutationsWithSameScope", "firstPendingMutation", "foundMutation", "pausedMutations", "infiniteQueryBehavior", "pages", "direction", "oldPages", "oldPageParams", "currentPage", "cancelled", "fetchPage", "param", "previous", "page", "maxPages", "addTo", "pageParamFn", "getPreviousPageParam", "getNextPageParam", "oldData", "remainingPages", "pageParams", "lastIndex", "QueryClient", "_queryCache", "_queryDefaults", "_mutationDefaults", "_mountCount", "_unsubscribeFocus", "_unsubscribeOnline", "defaultedOptions", "cachedData", "queryCache", "defaultedCancelOptions", "promises", "defaults", "query<PERSON><PERSON><PERSON>", "QueryObserver", "_QueryObserver_instances", "_current<PERSON><PERSON>y", "_currentQueryInitialState", "_currentResult", "_currentResultState", "_currentResultOptions", "_currentThenable", "_selectError", "_selectFn", "_selectResult", "_lastQueryWithDefinedData", "_staleTimeoutId", "_refetchIntervalId", "_currentRefetchInterval", "_trackedProps", "shouldFetchOnMount", "executeFetch_fn", "updateTimers_fn", "shouldFetchOn", "clearStaleTimeout_fn", "clearRefetchInterval_fn", "prevOptions", "prev<PERSON><PERSON><PERSON>", "updateQuery_fn", "mounted", "shouldFetchOptionally", "updateStaleTimeout_fn", "nextRefetchInterval", "computeRefetchInterval_fn", "updateRefetchInterval_fn", "shouldAssignObserverCurrentProperties", "onPropTracked", "target", "prevResult", "prevResultState", "prevResultOptions", "queryInitialState", "isPlaceholderData", "fetchOnMount", "fetchOptionally", "errorUpdatedAt", "skipSelect", "placeholderData", "selectError", "isFetching", "isPending", "isError", "isLoading", "nextResult", "isStale", "finalizeThenableIfPossible", "recreateThenable", "pending", "prevThenable", "shouldNotifyListeners", "notifyOnChangeProps", "notifyOnChangePropsValue", "includedProps", "<PERSON><PERSON><PERSON>", "notify_fn", "nextInterval", "notifyOptions", "shouldLoadOnMount", "field", "optimisticResult", "MutationObserver", "_MutationObserver_instances", "_currentMutation", "_mutateOptions", "updateResult_fn", "QueryClientContext", "React.createContext", "useQueryClient", "queryClient", "React.useContext", "QueryClientProvider", "children", "React.useEffect", "jsx", "IsRestoringContext", "useIsRestoring", "createValue", "isReset", "QueryErrorResetBoundaryContext", "useQueryErrorResetBoundary", "ensurePreventErrorBoundaryRetry", "errorResetBoundary", "useClearResetErrorBoundary", "getHasError", "suspense", "ensureSuspenseTimers", "clamp", "originalStaleTime", "<PERSON><PERSON><PERSON><PERSON>", "isRestoring", "shouldSuspend", "fetchOptimistic", "useBaseQuery", "Observer", "isNewCacheEntry", "React.useState", "shouldSubscribe", "React.useSyncExternalStore", "React.useCallback", "onStoreChange", "unsubscribe", "useQuery", "useMutation", "mutate", "mutateOptions"], "mappings": ";;;;;;;;GASa,IAAIA,GAAEC,EAAiBC,GAAE,OAAO,IAAI,eAAe,EAAEC,GAAE,OAAO,IAAI,gBAAgB,EAAEC,GAAE,OAAO,UAAU,eAAeC,GAAEL,GAAE,mDAAmD,kBAAkBM,GAAE,CAAC,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,SAAS,EAAE,EAClP,SAASC,GAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAIC,EAAEC,EAAE,GAAGC,EAAE,KAAKC,EAAE,KAAcJ,IAAT,SAAaG,EAAE,GAAGH,GAAYD,EAAE,MAAX,SAAiBI,EAAE,GAAGJ,EAAE,KAAcA,EAAE,MAAX,SAAiBK,EAAEL,EAAE,KAAK,IAAIE,KAAKF,EAAEL,GAAE,KAAKK,EAAEE,CAAC,GAAG,CAACL,GAAE,eAAeK,CAAC,IAAIC,EAAED,CAAC,EAAEF,EAAEE,CAAC,GAAG,GAAGH,GAAGA,EAAE,aAAa,IAAIG,KAAKF,EAAED,EAAE,aAAaC,EAAWG,EAAED,CAAC,aAAIC,EAAED,CAAC,EAAEF,EAAEE,CAAC,GAAG,MAAM,CAAC,SAAST,GAAE,KAAKM,EAAE,IAAIK,EAAE,IAAIC,EAAE,MAAMF,EAAE,OAAOP,GAAE,OAAO,CAAC,aAAkBF,GAAEY,GAAA,IAAYR,GAAEQ,GAAA,KAAaR,GCPxWS,GAAA,QAAiBf,qBCFfgB,GAAe,KAAM,CACvB,aAAc,CACZ,KAAK,UAA4B,IAAI,IACrC,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,CAC3C,CACA,UAAUC,EAAU,CAClB,YAAK,UAAU,IAAIA,CAAQ,EAC3B,KAAK,YAAW,EACT,IAAM,CACX,KAAK,UAAU,OAAOA,CAAQ,EAC9B,KAAK,cAAa,CACpB,CACF,CACA,cAAe,CACb,OAAO,KAAK,UAAU,KAAO,CAC/B,CACA,aAAc,CACd,CACA,eAAgB,CAChB,CACF,ECpBIC,GAAW,OAAO,OAAW,KAAe,SAAU,WAC1D,SAASC,GAAO,CAChB,CACA,SAASC,GAAiBC,EAASC,EAAO,CACxC,OAAO,OAAOD,GAAY,WAAaA,EAAQC,CAAK,EAAID,CAC1D,CACA,SAASE,GAAeC,EAAO,CAC7B,OAAO,OAAOA,GAAU,UAAYA,GAAS,GAAKA,IAAU,GAC9D,CACA,SAASC,GAAeC,EAAWC,EAAW,CAC5C,OAAO,KAAK,IAAID,GAAaC,GAAa,GAAK,KAAK,IAAA,EAAO,CAAC,CAC9D,CACA,SAASC,GAAiBD,EAAWE,EAAO,CAC1C,OAAO,OAAOF,GAAc,WAAaA,EAAUE,CAAK,EAAIF,CAC9D,CACA,SAASG,EAAeC,EAASF,EAAO,CACtC,OAAO,OAAOE,GAAY,WAAaA,EAAQF,CAAK,EAAIE,CAC1D,CACA,SAASC,GAAWC,EAASJ,EAAO,CAClC,KAAM,CACJ,KAAAK,EAAO,MACP,MAAAC,EACA,YAAAC,EACA,UAAAC,EACA,SAAAC,EACA,MAAAC,CAAA,EACEN,EACJ,GAAIK,GACF,GAAIH,GACF,GAAIN,EAAM,YAAcW,GAAsBF,EAAUT,EAAM,OAAO,EACnE,MAAO,WAEA,CAACY,GAAgBZ,EAAM,SAAUS,CAAQ,EAClD,MAAO,GAGX,GAAIJ,IAAS,MAAO,CAClB,MAAMQ,EAAWb,EAAM,SAAA,EAIvB,GAHIK,IAAS,UAAY,CAACQ,GAGtBR,IAAS,YAAcQ,EACzB,MAAO,EAEX,CAOA,MANI,SAAOH,GAAU,WAAaV,EAAM,QAAA,IAAcU,GAGlDH,GAAeA,IAAgBP,EAAM,MAAM,aAG3CQ,GAAa,CAACA,EAAUR,CAAK,EAInC,CACA,SAASc,GAAcV,EAASW,EAAU,CACxC,KAAM,CAAE,MAAAT,EAAO,OAAAU,EAAQ,UAAAR,EAAW,YAAAS,GAAgBb,EAClD,GAAIa,EAAa,CACf,GAAI,CAACF,EAAS,QAAQ,YACpB,MAAO,GAET,GAAIT,GACF,GAAIY,GAAQH,EAAS,QAAQ,WAAW,IAAMG,GAAQD,CAAW,EAC/D,MAAO,WAEA,CAACL,GAAgBG,EAAS,QAAQ,YAAaE,CAAW,EACnE,MAAO,EAEX,CAIA,MAHI,EAAAD,GAAUD,EAAS,MAAM,SAAWC,GAGpCR,GAAa,CAACA,EAAUO,CAAQ,EAItC,CACA,SAASJ,GAAsBF,EAAUU,EAAS,CAEhD,QADeA,GAAA,YAAAA,EAAS,iBAAkBD,IAC5BT,CAAQ,CACxB,CACA,SAASS,GAAQT,EAAU,CACzB,OAAO,KAAK,UACVA,EACA,CAACW,EAAGC,IAAQC,GAAcD,CAAG,EAAI,OAAO,KAAKA,CAAG,EAAE,KAAA,EAAO,OAAO,CAACE,EAAQC,KACvED,EAAOC,CAAG,EAAIH,EAAIG,CAAG,EACdD,GACN,CAAA,CAAE,EAAIF,CAAA,CAEb,CACA,SAAST,GAAgBjC,EAAGE,EAAG,CAC7B,OAAIF,IAAME,EACD,GAEL,OAAOF,GAAM,OAAOE,EACf,GAELF,GAAKE,GAAK,OAAOF,GAAM,UAAY,OAAOE,GAAM,SAC3C,OAAO,KAAKA,CAAC,EAAE,MAAO2C,GAAQZ,GAAgBjC,EAAE6C,CAAG,EAAG3C,EAAE2C,CAAG,CAAC,CAAC,EAE/D,EACT,CACA,SAASC,GAAiB9C,EAAGE,EAAG,CAC9B,GAAIF,IAAME,EACR,OAAOF,EAET,MAAM+C,EAAQC,GAAahD,CAAC,GAAKgD,GAAa9C,CAAC,EAC/C,GAAI6C,GAASJ,GAAc3C,CAAC,GAAK2C,GAAczC,CAAC,EAAG,CACjD,MAAM+C,EAASF,EAAQ/C,EAAI,OAAO,KAAKA,CAAC,EAClCkD,EAAQD,EAAO,OACfE,EAASJ,EAAQ7C,EAAI,OAAO,KAAKA,CAAC,EAClCkD,EAAQD,EAAO,OACfE,EAAON,EAAQ,CAAA,EAAK,CAAA,EACpBO,EAAY,IAAI,IAAIL,CAAM,EAChC,IAAIM,EAAa,EACjB,QAASC,EAAI,EAAGA,EAAIJ,EAAOI,IAAK,CAC9B,MAAMX,EAAME,EAAQS,EAAIL,EAAOK,CAAC,GAC3B,CAACT,GAASO,EAAU,IAAIT,CAAG,GAAKE,IAAU/C,EAAE6C,CAAG,IAAM,QAAU3C,EAAE2C,CAAG,IAAM,QAC7EQ,EAAKR,CAAG,EAAI,OACZU,MAEAF,EAAKR,CAAG,EAAIC,GAAiB9C,EAAE6C,CAAG,EAAG3C,EAAE2C,CAAG,CAAC,EACvCQ,EAAKR,CAAG,IAAM7C,EAAE6C,CAAG,GAAK7C,EAAE6C,CAAG,IAAM,QACrCU,IAGN,CACA,OAAOL,IAAUE,GAASG,IAAeL,EAAQlD,EAAIqD,CACvD,CACA,OAAOnD,CACT,CACA,SAASuD,GAAoBzD,EAAGE,EAAG,CACjC,GAAI,CAACA,GAAK,OAAO,KAAKF,CAAC,EAAE,SAAW,OAAO,KAAKE,CAAC,EAAE,OACjD,MAAO,GAET,UAAW2C,KAAO7C,EAChB,GAAIA,EAAE6C,CAAG,IAAM3C,EAAE2C,CAAG,EAClB,MAAO,GAGX,MAAO,EACT,CACA,SAASG,GAAahC,EAAO,CAC3B,OAAO,MAAM,QAAQA,CAAK,GAAKA,EAAM,SAAW,OAAO,KAAKA,CAAK,EAAE,MACrE,CACA,SAAS2B,GAAce,EAAG,CACxB,GAAI,CAACC,GAAmBD,CAAC,EACvB,MAAO,GAET,MAAME,EAAOF,EAAE,YACf,GAAIE,IAAS,OACX,MAAO,GAET,MAAMC,EAAOD,EAAK,UAOlB,MANI,GAACD,GAAmBE,CAAI,GAGxB,CAACA,EAAK,eAAe,eAAe,GAGpC,OAAO,eAAeH,CAAC,IAAM,OAAO,UAI1C,CACA,SAASC,GAAmBD,EAAG,CAC7B,OAAO,OAAO,UAAU,SAAS,KAAKA,CAAC,IAAM,iBAC/C,CACA,SAASI,GAAMC,EAAS,CACtB,OAAO,IAAI,QAASC,GAAY,CAC9B,WAAWA,EAASD,CAAO,CAC7B,CAAC,CACH,CACA,SAASE,GAAYC,EAAUC,EAAM3B,EAAS,CAC5C,OAAI,OAAOA,EAAQ,mBAAsB,WAChCA,EAAQ,kBAAkB0B,EAAUC,CAAI,EACtC3B,EAAQ,oBAAsB,GAWhCM,GAAiBoB,EAAUC,CAAI,EAEjCA,CACT,CAIA,SAASC,GAASC,EAAOC,EAAMC,EAAM,EAAG,CACtC,MAAMC,EAAW,CAAC,GAAGH,EAAOC,CAAI,EAChC,OAAOC,GAAOC,EAAS,OAASD,EAAMC,EAAS,MAAM,CAAC,EAAIA,CAC5D,CACA,SAASC,GAAWJ,EAAOC,EAAMC,EAAM,EAAG,CACxC,MAAMC,EAAW,CAACF,EAAM,GAAGD,CAAK,EAChC,OAAOE,GAAOC,EAAS,OAASD,EAAMC,EAAS,MAAM,EAAG,EAAE,EAAIA,CAChE,CACA,IAAIE,GAAY,OAAA,EAChB,SAASC,GAAcnC,EAASoC,EAAc,CAQ5C,MAAI,CAACpC,EAAQ,UAAWoC,GAAA,MAAAA,EAAc,gBAC7B,IAAMA,EAAa,eAExB,CAACpC,EAAQ,SAAWA,EAAQ,UAAYkC,GACnC,IAAM,QAAQ,OAAO,IAAI,MAAM,qBAAqBlC,EAAQ,SAAS,GAAG,CAAC,EAE3EA,EAAQ,OACjB,CACA,SAASqC,GAAiBC,EAAcC,EAAQ,CAC9C,OAAI,OAAOD,GAAiB,WACnBA,EAAa,GAAGC,CAAM,EAExB,CAAC,CAACD,CACX,iBC/NIE,IAAeC,GAAA,cAAczE,EAAa,CAI5C,aAAc,CACZ,MAAK,EAJP0E,EAAA,KAAAC,IACAD,EAAA,KAAAE,IACAF,EAAA,KAAAG,IAGEC,EAAA,KAAKD,GAAUE,GAAY,CACzB,GAAI,CAAC7E,IAAY,OAAO,iBAAkB,CACxC,MAAMD,EAAW,IAAM8E,EAAO,EAC9B,cAAO,iBAAiB,mBAAoB9E,EAAU,EAAK,EACpD,IAAM,CACX,OAAO,oBAAoB,mBAAoBA,CAAQ,CACzD,CACF,CAEF,EACF,CACA,aAAc,CACP+E,EAAA,KAAKJ,KACR,KAAK,iBAAiBI,EAAA,KAAKH,GAAM,CAErC,CACA,eAAgB,OACT,KAAK,kBACRJ,EAAAO,EAAA,KAAKJ,MAAL,MAAAH,EAAA,WACAK,EAAA,KAAKF,GAAW,QAEpB,CACA,iBAAiBK,EAAO,OACtBH,EAAA,KAAKD,GAASI,IACdR,EAAAO,EAAA,KAAKJ,MAAL,MAAAH,EAAA,WACAK,EAAA,KAAKF,GAAWK,EAAOC,GAAY,CAC7B,OAAOA,GAAY,UACrB,KAAK,WAAWA,CAAO,EAEvB,KAAK,QAAO,CAEhB,CAAC,EACH,CACA,WAAWA,EAAS,CACFF,EAAA,KAAKL,MAAaO,IAEhCJ,EAAA,KAAKH,GAAWO,GAChB,KAAK,QAAO,EAEhB,CACA,SAAU,CACR,MAAMC,EAAY,KAAK,UAAS,EAChC,KAAK,UAAU,QAASlF,GAAa,CACnCA,EAASkF,CAAS,CACpB,CAAC,CACH,CACA,WAAY,OACV,OAAI,OAAOH,EAAA,KAAKL,KAAa,UACpBK,EAAA,KAAKL,MAEPF,EAAA,WAAW,WAAX,YAAAA,EAAqB,mBAAoB,QAClD,CACF,EAzDEE,GAAA,YACAC,GAAA,YACAC,GAAA,YAHiBJ,IA2DfW,GAAe,IAAIZ,eC3DnBa,IAAgBZ,GAAA,cAAczE,EAAa,CAI7C,aAAc,CACZ,MAAK,EAJP0E,EAAA,KAAAY,GAAU,IACVZ,EAAA,KAAAE,IACAF,EAAA,KAAAG,IAGEC,EAAA,KAAKD,GAAUU,GAAa,CAC1B,GAAI,CAACrF,IAAY,OAAO,iBAAkB,CACxC,MAAMsF,EAAiB,IAAMD,EAAS,EAAI,EACpCE,EAAkB,IAAMF,EAAS,EAAK,EAC5C,cAAO,iBAAiB,SAAUC,EAAgB,EAAK,EACvD,OAAO,iBAAiB,UAAWC,EAAiB,EAAK,EAClD,IAAM,CACX,OAAO,oBAAoB,SAAUD,CAAc,EACnD,OAAO,oBAAoB,UAAWC,CAAe,CACvD,CACF,CAEF,EACF,CACA,aAAc,CACPT,EAAA,KAAKJ,KACR,KAAK,iBAAiBI,EAAA,KAAKH,GAAM,CAErC,CACA,eAAgB,OACT,KAAK,kBACRJ,EAAAO,EAAA,KAAKJ,MAAL,MAAAH,EAAA,WACAK,EAAA,KAAKF,GAAW,QAEpB,CACA,iBAAiBK,EAAO,OACtBH,EAAA,KAAKD,GAASI,IACdR,EAAAO,EAAA,KAAKJ,MAAL,MAAAH,EAAA,WACAK,EAAA,KAAKF,GAAWK,EAAM,KAAK,UAAU,KAAK,IAAI,CAAC,EACjD,CACA,UAAUS,EAAQ,CACAV,EAAA,KAAKM,MAAYI,IAE/BZ,EAAA,KAAKQ,GAAUI,GACf,KAAK,UAAU,QAASzF,GAAa,CACnCA,EAASyF,CAAM,CACjB,CAAC,EAEL,CACA,UAAW,CACT,OAAOV,EAAA,KAAKM,GACd,CACF,EA/CEA,GAAA,YACAV,GAAA,YACAC,GAAA,YAHkBJ,IAiDhBkB,GAAgB,IAAIN,GClDxB,SAASO,IAAkB,CACzB,IAAIpC,EACAqC,EACJ,MAAMC,EAAW,IAAI,QAAQ,CAACC,EAAUC,IAAY,CAClDxC,EAAUuC,EACVF,EAASG,CACX,CAAC,EACDF,EAAS,OAAS,UAClBA,EAAS,MAAM,IAAM,CACrB,CAAC,EACD,SAASG,EAAStC,EAAM,CACtB,OAAO,OAAOmC,EAAUnC,CAAI,EAC5B,OAAOmC,EAAS,QAChB,OAAOA,EAAS,MAClB,CACA,OAAAA,EAAS,QAAWtF,GAAU,CAC5ByF,EAAS,CACP,OAAQ,YACR,MAAAzF,CACN,CAAK,EACDgD,EAAQhD,CAAK,CACf,EACAsF,EAAS,OAAUI,GAAW,CAC5BD,EAAS,CACP,OAAQ,WACR,OAAAC,CACN,CAAK,EACDL,EAAOK,CAAM,CACf,EACOJ,CACT,CC3BA,SAASK,GAAkBC,EAAc,CACvC,OAAO,KAAK,IAAI,IAAM,GAAKA,EAAc,GAAG,CAC9C,CACA,SAASC,GAASC,EAAa,CAC7B,OAAQA,GAAe,YAAc,SAAWX,GAAc,SAAQ,EAAK,EAC7E,CACA,IAAIY,GAAiB,cAAc,KAAM,CACvC,YAAYvE,EAAS,CACnB,MAAM,gBAAgB,EACtB,KAAK,OAASA,GAAA,YAAAA,EAAS,OACvB,KAAK,OAASA,GAAA,YAAAA,EAAS,MACzB,CACF,EAIA,SAASwE,GAAcC,EAAQ,CAC7B,IAAIC,EAAmB,GACnBN,EAAe,EACfO,EACJ,MAAMb,EAAWF,GAAe,EAC1BgB,EAAa,IAAMd,EAAS,SAAW,UACvCe,EAAUC,GAAkB,OAC3BF,EAAU,IACbf,EAAO,IAAIU,GAAeO,CAAa,CAAC,GACxCrC,EAAAgC,EAAO,QAAP,MAAAhC,EAAA,KAAAgC,GAEJ,EACMM,EAAc,IAAM,CACxBL,EAAmB,EACrB,EACMM,EAAgB,IAAM,CAC1BN,EAAmB,EACrB,EACMO,EAAc,IAAM7B,GAAa,UAAS,IAAOqB,EAAO,cAAgB,UAAYd,GAAc,SAAQ,IAAOc,EAAO,OAAM,EAC9HS,EAAW,IAAMb,GAASI,EAAO,WAAW,GAAKA,EAAO,OAAM,EAC9DjD,EAAWhD,GAAU,CACpBoG,EAAU,IACbD,GAAA,MAAAA,IACAb,EAAS,QAAQtF,CAAK,EAE1B,EACMqF,EAAUrF,GAAU,CACnBoG,EAAU,IACbD,GAAA,MAAAA,IACAb,EAAS,OAAOtF,CAAK,EAEzB,EACM2G,EAAQ,IACL,IAAI,QAASC,GAAoB,OACtCT,EAAcnG,GAAU,EAClBoG,EAAU,GAAMK,MAClBG,EAAgB5G,CAAK,CAEzB,GACAiE,EAAAgC,EAAO,UAAP,MAAAhC,EAAA,KAAAgC,EACF,CAAC,EAAE,KAAK,IAAM,OACZE,EAAa,OACRC,EAAU,IACbnC,EAAAgC,EAAO,aAAP,MAAAhC,EAAA,KAAAgC,EAEJ,CAAC,EAEGY,EAAM,IAAM,CAChB,GAAIT,EAAU,EACZ,OAEF,IAAIU,EACJ,MAAMC,EAAiBnB,IAAiB,EAAIK,EAAO,eAAiB,OACpE,GAAI,CACFa,EAAiBC,GAAkBd,EAAO,GAAE,CAC9C,OAASe,EAAO,CACdF,EAAiB,QAAQ,OAAOE,CAAK,CACvC,CACA,QAAQ,QAAQF,CAAc,EAAE,KAAK9D,CAAO,EAAE,MAAOgE,GAAU,OAC7D,GAAIZ,EAAU,EACZ,OAEF,MAAMa,EAAQhB,EAAO,QAAUvG,GAAW,EAAI,GACxCwH,EAAajB,EAAO,YAAcN,GAClCwB,EAAQ,OAAOD,GAAe,WAAaA,EAAWtB,EAAcoB,CAAK,EAAIE,EAC7EE,EAAcH,IAAU,IAAQ,OAAOA,GAAU,UAAYrB,EAAeqB,GAAS,OAAOA,GAAU,YAAcA,EAAMrB,EAAcoB,CAAK,EACnJ,GAAId,GAAoB,CAACkB,EAAa,CACpC/B,EAAO2B,CAAK,EACZ,MACF,CACApB,KACA3B,EAAAgC,EAAO,SAAP,MAAAhC,EAAA,KAAAgC,EAAgBL,EAAcoB,GAC9BlE,GAAMqE,CAAK,EAAE,KAAK,IACTV,EAAW,EAAK,OAASE,EAAK,CACtC,EAAE,KAAK,IAAM,CACRT,EACFb,EAAO2B,CAAK,EAEZH,EAAG,CAEP,CAAC,CACH,CAAC,CACH,EACA,MAAO,CACL,QAASvB,EACT,OAAQ,IAAMA,EAAS,OACvB,OAAAe,EACA,SAAU,KACRF,GAAA,MAAAA,IACOb,GAET,YAAAiB,EACA,cAAAC,EACA,SAAAE,EACA,MAAO,KACDA,EAAQ,EACVG,EAAG,EAEHF,EAAK,EAAG,KAAKE,CAAG,EAEXvB,EAEb,CACA,CC3HA,IAAI+B,GAAoBC,GAAO,WAAWA,EAAI,CAAC,EAC/C,SAASC,IAAsB,CAC7B,IAAIC,EAAQ,CAAA,EACRC,EAAe,EACfC,EAAYC,GAAa,CAC3BA,EAAQ,CACV,EACIC,EAAiBD,GAAa,CAChCA,EAAQ,CACV,EACIE,EAAaR,GACjB,MAAMS,EAAYH,GAAa,CACzBF,EACFD,EAAM,KAAKG,CAAQ,EAEnBE,EAAW,IAAM,CACfH,EAASC,CAAQ,CACnB,CAAC,CAEL,EACMI,EAAQ,IAAM,CAClB,MAAMC,EAAgBR,EACtBA,EAAQ,CAAA,EACJQ,EAAc,QAChBH,EAAW,IAAM,CACfD,EAAc,IAAM,CAClBI,EAAc,QAASL,GAAa,CAClCD,EAASC,CAAQ,CACnB,CAAC,CACH,CAAC,CACH,CAAC,CAEL,EACA,MAAO,CACL,MAAQA,GAAa,CACnB,IAAI/F,EACJ6F,IACA,GAAI,CACF7F,EAAS+F,EAAQ,CACnB,QAAC,CACCF,IACKA,GACHM,EAAK,CAET,CACA,OAAOnG,CACT,EAIA,WAAa+F,GACJ,IAAIM,IAAS,CAClBH,EAAS,IAAM,CACbH,EAAS,GAAGM,CAAI,CAClB,CAAC,CACH,EAEF,SAAAH,EAKA,kBAAoBI,GAAO,CACzBR,EAAWQ,CACb,EAKA,uBAAyBA,GAAO,CAC9BN,EAAgBM,CAClB,EACA,aAAeA,GAAO,CACpBL,EAAaK,CACf,CACJ,CACA,CACA,IAAIC,EAAgBZ,GAAmB,QC5EnCa,IAAYnE,GAAA,KAAM,CAAN,cACdC,EAAA,KAAAmE,IACA,SAAU,CACR,KAAK,eAAc,CACrB,CACA,YAAa,CACX,KAAK,eAAc,EACftI,GAAe,KAAK,MAAM,GAC5BuE,EAAA,KAAK+D,GAAa,WAAW,IAAM,CACjC,KAAK,eAAc,CACrB,EAAG,KAAK,MAAM,EAElB,CACA,aAAaC,EAAW,CACtB,KAAK,OAAS,KAAK,IACjB,KAAK,QAAU,EACfA,IAAc5I,GAAW,IAAW,EAAI,GAAK,IACnD,CACE,CACA,gBAAiB,CACX8E,EAAA,KAAK6D,MACP,aAAa7D,EAAA,KAAK6D,GAAU,EAC5B/D,EAAA,KAAK+D,GAAa,QAEtB,CACF,EAxBEA,GAAA,YADcpE,8BCWZsE,IAAQtE,GAAA,cAAcmE,EAAU,CAQlC,YAAYnC,EAAQ,CAClB,MAAA,EATQ/B,EAAA,KAAAsE,GACVtE,EAAA,KAAAuE,IACAvE,EAAA,KAAAwE,IACAxE,EAAA,KAAAyE,GACAzE,EAAA,KAAA0E,IACA1E,EAAA,KAAA2E,GACA3E,EAAA,KAAA4E,IACA5E,EAAA,KAAA6E,IAGEzE,EAAA,KAAKyE,GAAuB,IAC5BzE,EAAA,KAAKwE,GAAkB7C,EAAO,gBAC9B,KAAK,WAAWA,EAAO,OAAO,EAC9B,KAAK,UAAY,CAAA,EACjB3B,EAAA,KAAKsE,GAAU3C,EAAO,QACtB3B,EAAA,KAAKqE,EAASnE,EAAA,KAAKoE,IAAQ,cAAA,GAC3B,KAAK,SAAW3C,EAAO,SACvB,KAAK,UAAYA,EAAO,UACxB3B,EAAA,KAAKmE,GAAgBO,GAAgB,KAAK,OAAO,GACjD,KAAK,MAAQ/C,EAAO,OAASzB,EAAA,KAAKiE,IAClC,KAAK,WAAA,CACP,CACA,IAAI,MAAO,CACT,OAAO,KAAK,QAAQ,IACtB,CACA,IAAI,SAAU,OACZ,OAAOxE,EAAAO,EAAA,KAAKqE,KAAL,YAAA5E,EAAe,OACxB,CACA,WAAWzC,EAAS,CAClB,KAAK,QAAU,CAAE,GAAGgD,EAAA,KAAKsE,IAAiB,GAAGtH,CAAA,EAC7C,KAAK,aAAa,KAAK,QAAQ,MAAM,CACvC,CACA,gBAAiB,CACX,CAAC,KAAK,UAAU,QAAU,KAAK,MAAM,cAAgB,QACvDgD,EAAA,KAAKmE,GAAO,OAAO,IAAI,CAE3B,CACA,QAAQM,EAASzH,EAAS,CACxB,MAAM2B,EAAOF,GAAY,KAAK,MAAM,KAAMgG,EAAS,KAAK,OAAO,EAC/D,OAAAC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CACb,KAAAhG,EACA,KAAM,UACN,cAAe3B,GAAA,YAAAA,EAAS,UACxB,OAAQA,GAAA,YAAAA,EAAS,MAAA,GAEZ2B,CACT,CACA,SAASiG,EAAOC,EAAiB,CAC/BH,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,WAAY,MAAAC,EAAO,gBAAAC,GAC5C,CACA,OAAO7H,EAAS,SACd,MAAM8H,GAAUrF,EAAAO,EAAA,KAAKqE,KAAL,YAAA5E,EAAe,QAC/B,OAAAsF,EAAA/E,EAAA,KAAKqE,KAAL,MAAAU,EAAe,OAAO/H,GACf8H,EAAUA,EAAQ,KAAK3J,CAAI,EAAE,MAAMA,CAAI,EAAI,QAAQ,QAAA,CAC5D,CACA,SAAU,CACR,MAAM,QAAA,EACN,KAAK,OAAO,CAAE,OAAQ,EAAA,CAAM,CAC9B,CACA,OAAQ,CACN,KAAK,QAAA,EACL,KAAK,SAAS6E,EAAA,KAAKiE,GAAa,CAClC,CACA,UAAW,CACT,OAAO,KAAK,UAAU,KACnBe,GAAalJ,EAAekJ,EAAS,QAAQ,QAAS,IAAI,IAAM,EAAA,CAErE,CACA,YAAa,CACX,OAAI,KAAK,kBAAA,EAAsB,EACtB,CAAC,KAAK,SAAA,EAER,KAAK,QAAQ,UAAY9F,IAAa,KAAK,MAAM,gBAAkB,KAAK,MAAM,mBAAqB,CAC5G,CACA,UAAW,CACT,OAAI,KAAK,kBAAA,EAAsB,EACtB,KAAK,UAAU,KACnB8F,GAAapJ,GAAiBoJ,EAAS,QAAQ,UAAW,IAAI,IAAM,QAAA,EAGlE,EACT,CACA,SAAU,CACR,OAAI,KAAK,kBAAA,EAAsB,EACtB,KAAK,UAAU,KACnBA,GAAaA,EAAS,mBAAmB,OAAA,EAGvC,KAAK,MAAM,OAAS,QAAU,KAAK,MAAM,aAClD,CACA,cAAcrJ,EAAY,EAAG,CAC3B,OAAI,KAAK,MAAM,OAAS,OACf,GAELA,IAAc,SACT,GAEL,KAAK,MAAM,cACN,GAEF,CAACF,GAAe,KAAK,MAAM,cAAeE,CAAS,CAC5D,CACA,SAAU,OACR,MAAMqJ,EAAW,KAAK,UAAU,KAAMC,GAAMA,EAAE,0BAA0B,EACxED,GAAA,MAAAA,EAAU,QAAQ,CAAE,cAAe,EAAA,IACnCvF,EAAAO,EAAA,KAAKqE,KAAL,MAAA5E,EAAe,UACjB,CACA,UAAW,OACT,MAAMuF,EAAW,KAAK,UAAU,KAAMC,GAAMA,EAAE,wBAAwB,EACtED,GAAA,MAAAA,EAAU,QAAQ,CAAE,cAAe,EAAA,IACnCvF,EAAAO,EAAA,KAAKqE,KAAL,MAAA5E,EAAe,UACjB,CACA,YAAYuF,EAAU,CACf,KAAK,UAAU,SAASA,CAAQ,IACnC,KAAK,UAAU,KAAKA,CAAQ,EAC5B,KAAK,eAAA,EACLhF,EAAA,KAAKmE,GAAO,OAAO,CAAE,KAAM,gBAAiB,MAAO,KAAM,SAAAa,EAAU,EAEvE,CACA,eAAeA,EAAU,CACnB,KAAK,UAAU,SAASA,CAAQ,IAClC,KAAK,UAAY,KAAK,UAAU,OAAQC,GAAMA,IAAMD,CAAQ,EACvD,KAAK,UAAU,SACdhF,EAAA,KAAKqE,KACHrE,EAAA,KAAKuE,IACPvE,EAAA,KAAKqE,GAAS,OAAO,CAAE,OAAQ,GAAM,EAErCrE,EAAA,KAAKqE,GAAS,YAAA,GAGlB,KAAK,WAAA,GAEPrE,EAAA,KAAKmE,GAAO,OAAO,CAAE,KAAM,kBAAmB,MAAO,KAAM,SAAAa,EAAU,EAEzE,CACA,mBAAoB,CAClB,OAAO,KAAK,UAAU,MACxB,CACA,YAAa,CACN,KAAK,MAAM,eACdN,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,YAAA,EAE3B,CACA,MAAM,MAAM3H,EAASoC,EAAc,6BACjC,GAAI,KAAK,MAAM,cAAgB,UAG/BK,EAAAO,EAAA,KAAKqE,KAAL,YAAA5E,EAAe,YAAa,YAC1B,GAAI,KAAK,MAAM,OAAS,SAAUL,GAAA,MAAAA,EAAc,eAC9C,KAAK,OAAO,CAAE,OAAQ,EAAA,CAAM,UACnBY,EAAA,KAAKqE,GACd,OAAArE,EAAA,KAAKqE,GAAS,cAAA,EACPrE,EAAA,KAAKqE,GAAS,QAMzB,GAHIrH,GACF,KAAK,WAAWA,CAAO,EAErB,CAAC,KAAK,QAAQ,QAAS,CACzB,MAAMgI,EAAW,KAAK,UAAU,KAAMC,GAAMA,EAAE,QAAQ,OAAO,EACzDD,GACF,KAAK,WAAWA,EAAS,OAAO,CAEpC,CAQA,MAAME,EAAkB,IAAI,gBACtBC,EAAqBC,GAAW,CACpC,OAAO,eAAeA,EAAQ,SAAU,CACtC,WAAY,GACZ,IAAK,KACHtF,EAAA,KAAKyE,GAAuB,IACrBW,EAAgB,OACzB,CACD,CACH,EACMG,EAAU,IAAM,CACpB,MAAMC,EAAUnG,GAAc,KAAK,QAASC,CAAY,EAUlDmG,GATuB,IAAM,CACjC,MAAMC,EAAkB,CACtB,OAAQxF,EAAA,KAAKoE,IACb,SAAU,KAAK,SACf,KAAM,KAAK,IAAA,EAEb,OAAAe,EAAkBK,CAAe,EAC1BA,CACT,GACuB,EAEvB,OADA1F,EAAA,KAAKyE,GAAuB,IACxB,KAAK,QAAQ,UACR,KAAK,QAAQ,UAClBe,EACAC,EACA,IAAA,EAGGD,EAAQC,CAAc,CAC/B,EAaME,GAZqB,IAAM,CAC/B,MAAMC,EAAW,CACf,aAAAtG,EACA,QAAS,KAAK,QACd,SAAU,KAAK,SACf,OAAQY,EAAA,KAAKoE,IACb,MAAO,KAAK,MACZ,QAAAiB,CAAA,EAEF,OAAAF,EAAkBO,CAAQ,EACnBA,CACT,GACgB,GAChBX,EAAA,KAAK,QAAQ,WAAb,MAAAA,EAAuB,QAAQU,EAAS,MACxC3F,EAAA,KAAKoE,GAAe,KAAK,QACrB,KAAK,MAAM,cAAgB,QAAU,KAAK,MAAM,cAAcyB,EAAAF,EAAQ,eAAR,YAAAE,EAAsB,QACtFjB,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,QAAS,MAAMiB,EAAAH,EAAQ,eAAR,YAAAG,EAAsB,OAE9D9F,EAAA,KAAKuE,EAAW7C,GAAc,CAC5B,eAAgBpC,GAAA,YAAAA,EAAc,eAC9B,GAAIqG,EAAQ,QACZ,MAAOP,EAAgB,MAAM,KAAKA,CAAe,EACjD,OAAQ,CAAC9D,EAAcoB,IAAU,CAC/BkC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,SAAU,aAAAvD,EAAc,MAAAoB,GACjD,EACA,QAAS,IAAM,CACbkC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,OAAA,EACzB,EACA,WAAY,IAAM,CAChBD,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,UAAA,EACzB,EACA,MAAOc,EAAQ,QAAQ,MACvB,WAAYA,EAAQ,QAAQ,WAC5B,YAAaA,EAAQ,QAAQ,YAC7B,OAAQ,IAAM,EAAA,CACf,GACD,GAAI,CACF,MAAM9G,EAAO,MAAMqB,EAAA,KAAKqE,GAAS,MAAA,EACjC,GAAI1F,IAAS,OAMX,MAAM,IAAI,MAAM,GAAG,KAAK,SAAS,oBAAoB,EAEvD,YAAK,QAAQA,CAAI,GACjBkH,GAAAC,EAAA9F,EAAA,KAAKmE,GAAO,QAAO,YAAnB,MAAA0B,EAAA,KAAAC,EAA+BnH,EAAM,OACrCoH,GAAAC,EAAAhG,EAAA,KAAKmE,GAAO,QAAO,YAAnB,MAAA4B,EAAA,KAAAC,EACErH,EACA,KAAK,MAAM,MACX,MAEKA,CACT,OAAS6D,EAAO,CACd,GAAIA,aAAiBjB,GAAgB,CACnC,GAAIiB,EAAM,OACR,OAAOxC,EAAA,KAAKqE,GAAS,QACvB,GAAW7B,EAAM,OACf,YAAK,SAAS,CACZ,GAAGxC,EAAA,KAAKkE,IACR,YAAa,MAAA,CACd,EACM,KAAK,MAAM,IAEtB,CACA,MAAAQ,EAAA,KAAKV,EAAAW,GAAL,UAAe,CACb,KAAM,QACN,MAAAnC,CAAA,IAEFyD,GAAAC,EAAAlG,EAAA,KAAKmE,GAAO,QAAO,UAAnB,MAAA8B,EAAA,KAAAC,EACE1D,EACA,OAEF2D,GAAAC,EAAApG,EAAA,KAAKmE,GAAO,QAAO,YAAnB,MAAAgC,EAAA,KAAAC,EACE,KAAK,MAAM,KACX5D,EACA,MAEIA,CACR,QAAA,CACE,KAAK,WAAA,CACP,CACF,CA2EF,EAxWEyB,GAAA,YACAC,GAAA,YACAC,EAAA,YACAC,GAAA,YACAC,EAAA,YACAC,GAAA,YACAC,GAAA,YAPUP,EAAA,YA+RVW,WAAU0B,EAAQ,CAChB,MAAMC,EAAW1B,GAAU,CACzB,OAAQyB,EAAO,KAAA,CACb,IAAK,SACH,MAAO,CACL,GAAGzB,EACH,kBAAmByB,EAAO,aAC1B,mBAAoBA,EAAO,KAAA,EAE/B,IAAK,QACH,MAAO,CACL,GAAGzB,EACH,YAAa,QAAA,EAEjB,IAAK,WACH,MAAO,CACL,GAAGA,EACH,YAAa,UAAA,EAEjB,IAAK,QACH,MAAO,CACL,GAAGA,EACH,GAAG2B,GAAW3B,EAAM,KAAM,KAAK,OAAO,EACtC,UAAWyB,EAAO,MAAQ,IAAA,EAE9B,IAAK,UACH,MAAMG,EAAW,CACf,GAAG5B,EACH,KAAMyB,EAAO,KACb,gBAAiBzB,EAAM,gBAAkB,EACzC,cAAeyB,EAAO,eAAiB,KAAK,IAAA,EAC5C,MAAO,KACP,cAAe,GACf,OAAQ,UACR,GAAG,CAACA,EAAO,QAAU,CACnB,YAAa,OACb,kBAAmB,EACnB,mBAAoB,IAAA,CACtB,EAEF,OAAAvG,EAAA,KAAKoE,GAAemC,EAAO,OAASG,EAAW,QACxCA,EACT,IAAK,QACH,MAAMhE,EAAQ6D,EAAO,MACrB,MAAO,CACL,GAAGzB,EACH,MAAApC,EACA,iBAAkBoC,EAAM,iBAAmB,EAC3C,eAAgB,KAAK,IAAA,EACrB,kBAAmBA,EAAM,kBAAoB,EAC7C,mBAAoBpC,EACpB,YAAa,OACb,OAAQ,OAAA,EAEZ,IAAK,aACH,MAAO,CACL,GAAGoC,EACH,cAAe,EAAA,EAEnB,IAAK,WACH,MAAO,CACL,GAAGA,EACH,GAAGyB,EAAO,KAAA,CACZ,CAEN,EACA,KAAK,MAAQC,EAAQ,KAAK,KAAK,EAC/B3C,EAAc,MAAM,IAAM,CACxB,KAAK,UAAU,QAASqB,GAAa,CACnCA,EAAS,cAAA,CACX,CAAC,EACDhF,EAAA,KAAKmE,GAAO,OAAO,CAAE,MAAO,KAAM,KAAM,UAAW,OAAAkC,EAAQ,CAC7D,CAAC,CACH,EAxWU5G,IA0WZ,SAAS8G,GAAW5H,EAAM3B,EAAS,CACjC,MAAO,CACL,kBAAmB,EACnB,mBAAoB,KACpB,YAAaqE,GAASrE,EAAQ,WAAW,EAAI,WAAa,SAC1D,GAAG2B,IAAS,QAAU,CACpB,MAAO,KACP,OAAQ,SAAA,CACV,CAEJ,CACA,SAAS6F,GAAgBxH,EAAS,CAChC,MAAM2B,EAAO,OAAO3B,EAAQ,aAAgB,WAAaA,EAAQ,cAAgBA,EAAQ,YACnFyJ,EAAU9H,IAAS,OACnB+H,EAAuBD,EAAU,OAAOzJ,EAAQ,sBAAyB,WAAaA,EAAQ,qBAAA,EAAyBA,EAAQ,qBAAuB,EAC5J,MAAO,CACL,KAAA2B,EACA,gBAAiB,EACjB,cAAe8H,EAAUC,GAAwB,KAAK,MAAQ,EAC9D,MAAO,KACP,iBAAkB,EAClB,eAAgB,EAChB,kBAAmB,EACnB,mBAAoB,KACpB,UAAW,KACX,cAAe,GACf,OAAQD,EAAU,UAAY,UAC9B,YAAa,MAAA,CAEjB,UC/YIE,IAAalH,GAAA,cAAczE,EAAa,CAC1C,YAAYyG,EAAS,GAAI,CACvB,MAAK,EAIP/B,EAAA,KAAAkH,GAHE,KAAK,OAASnF,EACd3B,EAAA,KAAK8G,EAA2B,IAAI,IACtC,CAEA,MAAMC,EAAQ7J,EAAS4H,EAAO,CAC5B,MAAMtI,EAAWU,EAAQ,SACnB8J,EAAY9J,EAAQ,WAAaR,GAAsBF,EAAUU,CAAO,EAC9E,IAAInB,EAAQ,KAAK,IAAIiL,CAAS,EAC9B,OAAKjL,IACHA,EAAQ,IAAIkI,GAAM,CAChB,OAAA8C,EACA,SAAAvK,EACA,UAAAwK,EACA,QAASD,EAAO,oBAAoB7J,CAAO,EAC3C,MAAA4H,EACA,eAAgBiC,EAAO,iBAAiBvK,CAAQ,CACxD,CAAO,EACD,KAAK,IAAIT,CAAK,GAETA,CACT,CACA,IAAIA,EAAO,CACJmE,EAAA,KAAK4G,GAAS,IAAI/K,EAAM,SAAS,IACpCmE,EAAA,KAAK4G,GAAS,IAAI/K,EAAM,UAAWA,CAAK,EACxC,KAAK,OAAO,CACV,KAAM,QACN,MAAAA,CACR,CAAO,EAEL,CACA,OAAOA,EAAO,CACZ,MAAMkL,EAAa/G,EAAA,KAAK4G,GAAS,IAAI/K,EAAM,SAAS,EAChDkL,IACFlL,EAAM,QAAO,EACTkL,IAAelL,GACjBmE,EAAA,KAAK4G,GAAS,OAAO/K,EAAM,SAAS,EAEtC,KAAK,OAAO,CAAE,KAAM,UAAW,MAAAA,CAAK,CAAE,EAE1C,CACA,OAAQ,CACN8H,EAAc,MAAM,IAAM,CACxB,KAAK,OAAM,EAAG,QAAS9H,GAAU,CAC/B,KAAK,OAAOA,CAAK,CACnB,CAAC,CACH,CAAC,CACH,CACA,IAAIiL,EAAW,CACb,OAAO9G,EAAA,KAAK4G,GAAS,IAAIE,CAAS,CACpC,CACA,QAAS,CACP,MAAO,CAAC,GAAG9G,EAAA,KAAK4G,GAAS,OAAM,CAAE,CACnC,CACA,KAAK3K,EAAS,CACZ,MAAM+K,EAAmB,CAAE,MAAO,GAAM,GAAG/K,CAAO,EAClD,OAAO,KAAK,OAAM,EAAG,KAClBJ,GAAUG,GAAWgL,EAAkBnL,CAAK,CACnD,CACE,CACA,QAAQI,EAAU,GAAI,CACpB,MAAMgL,EAAU,KAAK,OAAM,EAC3B,OAAO,OAAO,KAAKhL,CAAO,EAAE,OAAS,EAAIgL,EAAQ,OAAQpL,GAAUG,GAAWC,EAASJ,CAAK,CAAC,EAAIoL,CACnG,CACA,OAAOC,EAAO,CACZvD,EAAc,MAAM,IAAM,CACxB,KAAK,UAAU,QAAS1I,GAAa,CACnCA,EAASiM,CAAK,CAChB,CAAC,CACH,CAAC,CACH,CACA,SAAU,CACRvD,EAAc,MAAM,IAAM,CACxB,KAAK,OAAM,EAAG,QAAS9H,GAAU,CAC/BA,EAAM,QAAO,CACf,CAAC,CACH,CAAC,CACH,CACA,UAAW,CACT8H,EAAc,MAAM,IAAM,CACxB,KAAK,OAAM,EAAG,QAAS9H,GAAU,CAC/BA,EAAM,SAAQ,CAChB,CAAC,CACH,CAAC,CACH,CACF,EAjFE+K,EAAA,YANenH,kBCDb0H,IAAW1H,GAAA,cAAcmE,EAAU,CAIrC,YAAYnC,EAAQ,CAClB,MAAK,EALM/B,EAAA,KAAA0H,GACb1H,EAAA,KAAA2H,GACA3H,EAAA,KAAA4H,GACA5H,EAAA,KAAA2E,IAGE,KAAK,WAAa5C,EAAO,WACzB3B,EAAA,KAAKwH,EAAiB7F,EAAO,eAC7B3B,EAAA,KAAKuH,EAAa,CAAA,GAClB,KAAK,MAAQ5F,EAAO,OAAS+C,GAAe,EAC5C,KAAK,WAAW/C,EAAO,OAAO,EAC9B,KAAK,WAAU,CACjB,CACA,WAAWzE,EAAS,CAClB,KAAK,QAAUA,EACf,KAAK,aAAa,KAAK,QAAQ,MAAM,CACvC,CACA,IAAI,MAAO,CACT,OAAO,KAAK,QAAQ,IACtB,CACA,YAAYgI,EAAU,CACfhF,EAAA,KAAKqH,GAAW,SAASrC,CAAQ,IACpChF,EAAA,KAAKqH,GAAW,KAAKrC,CAAQ,EAC7B,KAAK,eAAc,EACnBhF,EAAA,KAAKsH,GAAe,OAAO,CACzB,KAAM,gBACN,SAAU,KACV,SAAAtC,CACR,CAAO,EAEL,CACA,eAAeA,EAAU,CACvBlF,EAAA,KAAKuH,EAAarH,EAAA,KAAKqH,GAAW,OAAQpC,GAAMA,IAAMD,CAAQ,GAC9D,KAAK,WAAU,EACfhF,EAAA,KAAKsH,GAAe,OAAO,CACzB,KAAM,kBACN,SAAU,KACV,SAAAtC,CACN,CAAK,CACH,CACA,gBAAiB,CACVhF,EAAA,KAAKqH,GAAW,SACf,KAAK,MAAM,SAAW,UACxB,KAAK,WAAU,EAEfrH,EAAA,KAAKsH,GAAe,OAAO,IAAI,EAGrC,CACA,UAAW,OACT,QAAO7H,EAAAO,EAAA,KAAKqE,MAAL,YAAA5E,EAAe,aACtB,KAAK,QAAQ,KAAK,MAAM,SAAS,CACnC,CACA,MAAM,QAAQ8H,EAAW,6CACvB,MAAMC,EAAa,IAAM,CACvB9C,EAAA,KAAK0C,EAAAzC,GAAL,UAAe,CAAE,KAAM,UAAU,EACnC,EACA7E,EAAA,KAAKuE,GAAW7C,GAAc,CAC5B,GAAI,IACG,KAAK,QAAQ,WAGX,KAAK,QAAQ,WAAW+F,CAAS,EAF/B,QAAQ,OAAO,IAAI,MAAM,qBAAqB,CAAC,EAI1D,OAAQ,CAACnG,EAAcoB,IAAU,CAC/BkC,EAAA,KAAK0C,EAAAzC,GAAL,UAAe,CAAE,KAAM,SAAU,aAAAvD,EAAc,MAAAoB,GACjD,EACA,QAAS,IAAM,CACbkC,EAAA,KAAK0C,EAAAzC,GAAL,UAAe,CAAE,KAAM,OAAO,EAChC,EACA,WAAA6C,EACA,MAAO,KAAK,QAAQ,OAAS,EAC7B,WAAY,KAAK,QAAQ,WACzB,YAAa,KAAK,QAAQ,YAC1B,OAAQ,IAAMxH,EAAA,KAAKsH,GAAe,OAAO,IAAI,CACnD,CAAK,GACD,MAAMG,EAAW,KAAK,MAAM,SAAW,UACjCC,EAAW,CAAC1H,EAAA,KAAKqE,IAAS,SAAQ,EACxC,GAAI,CACF,GAAIoD,EACFD,EAAU,MACL,CACL9C,EAAA,KAAK0C,EAAAzC,GAAL,UAAe,CAAE,KAAM,UAAW,UAAA4C,EAAW,SAAAG,IAC7C,OAAM3C,GAAAtF,EAAAO,EAAA,KAAKsH,GAAe,QAAO,WAA3B,YAAAvC,EAAA,KAAAtF,EACJ8H,EACA,OAEF,MAAM9B,EAAU,OAAMG,GAAAD,EAAA,KAAK,SAAQ,WAAb,YAAAC,EAAA,KAAAD,EAAwB4B,IAC1C9B,IAAY,KAAK,MAAM,SACzBf,EAAA,KAAK0C,EAAAzC,GAAL,UAAe,CACb,KAAM,UACN,QAAAc,EACA,UAAA8B,EACA,SAAAG,CACZ,EAEM,CACA,MAAM/I,EAAO,MAAMqB,EAAA,KAAKqE,IAAS,MAAK,EACtC,cAAMwB,GAAAC,EAAA9F,EAAA,KAAKsH,GAAe,QAAO,YAA3B,YAAAzB,EAAA,KAAAC,EACJnH,EACA4I,EACA,KAAK,MAAM,QACX,OAEF,OAAMxB,GAAAC,EAAA,KAAK,SAAQ,YAAb,YAAAD,EAAA,KAAAC,EAAyBrH,EAAM4I,EAAW,KAAK,MAAM,UAC3D,OAAMtB,GAAAC,EAAAlG,EAAA,KAAKsH,GAAe,QAAO,YAA3B,YAAArB,EAAA,KAAAC,EACJvH,EACA,KACA,KAAK,MAAM,UACX,KAAK,MAAM,QACX,OAEF,OAAMwH,GAAAC,EAAA,KAAK,SAAQ,YAAb,YAAAD,EAAA,KAAAC,EAAyBzH,EAAM,KAAM4I,EAAW,KAAK,MAAM,UACjE7C,EAAA,KAAK0C,EAAAzC,GAAL,UAAe,CAAE,KAAM,UAAW,KAAAhG,CAAI,GAC/BA,CACT,OAAS6D,EAAO,CACd,GAAI,CACF,aAAMmF,GAAAC,EAAA5H,EAAA,KAAKsH,GAAe,QAAO,UAA3B,YAAAK,EAAA,KAAAC,EACJpF,EACA+E,EACA,KAAK,MAAM,QACX,OAEF,OAAMM,GAAAC,EAAA,KAAK,SAAQ,UAAb,YAAAD,EAAA,KAAAC,EACJtF,EACA+E,EACA,KAAK,MAAM,UAEb,OAAMQ,GAAAC,EAAAhI,EAAA,KAAKsH,GAAe,QAAO,YAA3B,YAAAS,EAAA,KAAAC,EACJ,OACAxF,EACA,KAAK,MAAM,UACX,KAAK,MAAM,QACX,OAEF,OAAMyF,GAAAC,EAAA,KAAK,SAAQ,YAAb,YAAAD,EAAA,KAAAC,EACJ,OACA1F,EACA+E,EACA,KAAK,MAAM,UAEP/E,CACR,QAAC,CACCkC,EAAA,KAAK0C,EAAAzC,GAAL,UAAe,CAAE,KAAM,QAAS,MAAAnC,CAAK,EACvC,CACF,QAAC,CACCxC,EAAA,KAAKsH,GAAe,QAAQ,IAAI,CAClC,CACF,CAmEF,EAtNED,EAAA,YACAC,EAAA,YACAjD,GAAA,YAHa+C,EAAA,YAqJbzC,EAAS,SAAC0B,EAAQ,CAChB,MAAMC,EAAW1B,GAAU,CACzB,OAAQyB,EAAO,KAAI,CACjB,IAAK,SACH,MAAO,CACL,GAAGzB,EACH,aAAcyB,EAAO,aACrB,cAAeA,EAAO,KAClC,EACQ,IAAK,QACH,MAAO,CACL,GAAGzB,EACH,SAAU,EACtB,EACQ,IAAK,WACH,MAAO,CACL,GAAGA,EACH,SAAU,EACtB,EACQ,IAAK,UACH,MAAO,CACL,GAAGA,EACH,QAASyB,EAAO,QAChB,KAAM,OACN,aAAc,EACd,cAAe,KACf,MAAO,KACP,SAAUA,EAAO,SACjB,OAAQ,UACR,UAAWA,EAAO,UAClB,YAAa,KAAK,IAAG,CACjC,EACQ,IAAK,UACH,MAAO,CACL,GAAGzB,EACH,KAAMyB,EAAO,KACb,aAAc,EACd,cAAe,KACf,MAAO,KACP,OAAQ,UACR,SAAU,EACtB,EACQ,IAAK,QACH,MAAO,CACL,GAAGzB,EACH,KAAM,OACN,MAAOyB,EAAO,MACd,aAAczB,EAAM,aAAe,EACnC,cAAeyB,EAAO,MACtB,SAAU,GACV,OAAQ,OACpB,CACA,CACI,EACA,KAAK,MAAQC,EAAQ,KAAK,KAAK,EAC/B3C,EAAc,MAAM,IAAM,CACxB3D,EAAA,KAAKqH,GAAW,QAASrC,GAAa,CACpCA,EAAS,iBAAiBqB,CAAM,CAClC,CAAC,EACDrG,EAAA,KAAKsH,GAAe,OAAO,CACzB,SAAU,KACV,KAAM,UACN,OAAAjB,CACR,CAAO,CACH,CAAC,CACH,EAtNa5G,IAwNf,SAAS+E,IAAkB,CACzB,MAAO,CACL,QAAS,OACT,KAAM,OACN,MAAO,KACP,aAAc,EACd,cAAe,KACf,SAAU,GACV,OAAQ,OACR,UAAW,OACX,YAAa,CACjB,CACA,eCnOI2D,IAAgB1I,GAAA,cAAczE,EAAa,CAC7C,YAAYyG,EAAS,GAAI,CACvB,MAAK,EAMP/B,EAAA,KAAA0I,GACA1I,EAAA,KAAA2I,GACA3I,EAAA,KAAA4I,IAPE,KAAK,OAAS7G,EACd3B,EAAA,KAAKsI,EAA6B,IAAI,KACtCtI,EAAA,KAAKuI,EAA0B,IAAI,KACnCvI,EAAA,KAAKwI,GAAc,EACrB,CAIA,MAAMzB,EAAQ7J,EAAS4H,EAAO,CAC5B,MAAMhI,EAAW,IAAIuK,GAAS,CAC5B,cAAe,KACf,WAAmB,EAALoB,GAAA,KAAKD,IAAL,EACd,QAASzB,EAAO,uBAAuB7J,CAAO,EAC9C,MAAA4H,CACN,CAAK,EACD,YAAK,IAAIhI,CAAQ,EACVA,CACT,CACA,IAAIA,EAAU,CACZoD,EAAA,KAAKoI,GAAW,IAAIxL,CAAQ,EAC5B,MAAM4L,EAAQC,GAAS7L,CAAQ,EAC/B,GAAI,OAAO4L,GAAU,SAAU,CAC7B,MAAME,EAAkB1I,EAAA,KAAKqI,GAAQ,IAAIG,CAAK,EAC1CE,EACFA,EAAgB,KAAK9L,CAAQ,EAE7BoD,EAAA,KAAKqI,GAAQ,IAAIG,EAAO,CAAC5L,CAAQ,CAAC,CAEtC,CACA,KAAK,OAAO,CAAE,KAAM,QAAS,SAAAA,CAAQ,CAAE,CACzC,CACA,OAAOA,EAAU,CACf,GAAIoD,EAAA,KAAKoI,GAAW,OAAOxL,CAAQ,EAAG,CACpC,MAAM4L,EAAQC,GAAS7L,CAAQ,EAC/B,GAAI,OAAO4L,GAAU,SAAU,CAC7B,MAAME,EAAkB1I,EAAA,KAAKqI,GAAQ,IAAIG,CAAK,EAC9C,GAAIE,EACF,GAAIA,EAAgB,OAAS,EAAG,CAC9B,MAAMC,EAAQD,EAAgB,QAAQ9L,CAAQ,EAC1C+L,IAAU,IACZD,EAAgB,OAAOC,EAAO,CAAC,CAEnC,MAAWD,EAAgB,CAAC,IAAM9L,GAChCoD,EAAA,KAAKqI,GAAQ,OAAOG,CAAK,CAG/B,CACF,CACA,KAAK,OAAO,CAAE,KAAM,UAAW,SAAA5L,CAAQ,CAAE,CAC3C,CACA,OAAOA,EAAU,CACf,MAAM4L,EAAQC,GAAS7L,CAAQ,EAC/B,GAAI,OAAO4L,GAAU,SAAU,CAC7B,MAAMI,EAAyB5I,EAAA,KAAKqI,GAAQ,IAAIG,CAAK,EAC/CK,EAAuBD,GAAA,YAAAA,EAAwB,KAClDzO,GAAMA,EAAE,MAAM,SAAW,WAE5B,MAAO,CAAC0O,GAAwBA,IAAyBjM,CAC3D,KACE,OAAO,EAEX,CACA,QAAQA,EAAU,OAChB,MAAM4L,EAAQC,GAAS7L,CAAQ,EAC/B,GAAI,OAAO4L,GAAU,SAAU,CAC7B,MAAMM,GAAgBrJ,EAAAO,EAAA,KAAKqI,GAAQ,IAAIG,CAAK,IAAtB,YAAA/I,EAAyB,KAAMtF,GAAMA,IAAMyC,GAAYzC,EAAE,MAAM,UACrF,OAAO2O,GAAA,YAAAA,EAAe,aAAc,QAAQ,QAAO,CACrD,KACE,QAAO,QAAQ,QAAO,CAE1B,CACA,OAAQ,CACNnF,EAAc,MAAM,IAAM,CACxB3D,EAAA,KAAKoI,GAAW,QAASxL,GAAa,CACpC,KAAK,OAAO,CAAE,KAAM,UAAW,SAAAA,CAAQ,CAAE,CAC3C,CAAC,EACDoD,EAAA,KAAKoI,GAAW,MAAK,EACrBpI,EAAA,KAAKqI,GAAQ,MAAK,CACpB,CAAC,CACH,CACA,QAAS,CACP,OAAO,MAAM,KAAKrI,EAAA,KAAKoI,EAAU,CACnC,CACA,KAAKnM,EAAS,CACZ,MAAM+K,EAAmB,CAAE,MAAO,GAAM,GAAG/K,CAAO,EAClD,OAAO,KAAK,OAAM,EAAG,KAClBW,GAAaD,GAAcqK,EAAkBpK,CAAQ,CAC5D,CACE,CACA,QAAQX,EAAU,GAAI,CACpB,OAAO,KAAK,OAAM,EAAG,OAAQW,GAAaD,GAAcV,EAASW,CAAQ,CAAC,CAC5E,CACA,OAAOsK,EAAO,CACZvD,EAAc,MAAM,IAAM,CACxB,KAAK,UAAU,QAAS1I,GAAa,CACnCA,EAASiM,CAAK,CAChB,CAAC,CACH,CAAC,CACH,CACA,uBAAwB,CACtB,MAAM6B,EAAkB,KAAK,SAAS,OAAQ9D,GAAMA,EAAE,MAAM,QAAQ,EACpE,OAAOtB,EAAc,MACnB,IAAM,QAAQ,IACZoF,EAAgB,IAAKnM,GAAaA,EAAS,SAAQ,EAAG,MAAMzB,CAAI,CAAC,CACzE,CACA,CACE,CACF,EAtGEiN,EAAA,YACAC,EAAA,YACAC,GAAA,YAVkB7I,IA+GpB,SAASgJ,GAAS7L,EAAU,OAC1B,OAAO6C,EAAA7C,EAAS,QAAQ,QAAjB,YAAA6C,EAAwB,EACjC,CCpHA,SAASuJ,GAAsBC,EAAO,CACpC,MAAO,CACL,QAAS,CAACxD,EAAS5J,IAAU,eAC3B,MAAMmB,EAAUyI,EAAQ,QAClByD,GAAYvD,GAAAZ,GAAAtF,EAAAgG,EAAQ,eAAR,YAAAhG,EAAsB,OAAtB,YAAAsF,EAA4B,YAA5B,YAAAY,EAAuC,UACnDwD,IAAWvD,EAAAH,EAAQ,MAAM,OAAd,YAAAG,EAAoB,QAAS,CAAA,EACxCwD,IAAgBtD,EAAAL,EAAQ,MAAM,OAAd,YAAAK,EAAoB,aAAc,CAAA,EACxD,IAAI1I,EAAS,CAAE,MAAO,CAAA,EAAI,WAAY,CAAA,CAAE,EACpCiM,EAAc,EAClB,MAAMhE,EAAU,SAAY,CAC1B,IAAIiE,EAAY,GAChB,MAAMnE,EAAqBC,GAAW,CACpC,OAAO,eAAeA,EAAQ,SAAU,CACtC,WAAY,GACZ,IAAK,KACCK,EAAQ,OAAO,QACjB6D,EAAY,GAEZ7D,EAAQ,OAAO,iBAAiB,QAAS,IAAM,CAC7C6D,EAAY,EACd,CAAC,EAEI7D,EAAQ,OAE7B,CAAW,CACH,EACMH,EAAUnG,GAAcsG,EAAQ,QAASA,EAAQ,YAAY,EAC7D8D,EAAY,MAAO5K,EAAM6K,EAAOC,IAAa,CACjD,GAAIH,EACF,OAAO,QAAQ,OAAM,EAEvB,GAAIE,GAAS,MAAQ7K,EAAK,MAAM,OAC9B,OAAO,QAAQ,QAAQA,CAAI,EAa7B,MAAM4G,GAXuB,IAAM,CACjC,MAAMC,GAAkB,CACtB,OAAQC,EAAQ,OAChB,SAAUA,EAAQ,SAClB,UAAW+D,EACX,UAAWC,EAAW,WAAa,UACnC,KAAMhE,EAAQ,QAAQ,IACpC,EACY,OAAAN,EAAkBK,EAAe,EAC1BA,EACT,GAC2C,EACrCkE,EAAO,MAAMpE,EAAQC,CAAc,EACnC,CAAE,SAAAoE,GAAalE,EAAQ,QACvBmE,EAAQH,EAAWxK,GAAaL,GACtC,MAAO,CACL,MAAOgL,EAAMjL,EAAK,MAAO+K,EAAMC,CAAQ,EACvC,WAAYC,EAAMjL,EAAK,WAAY6K,EAAOG,CAAQ,CAC9D,CACQ,EACA,GAAIT,GAAaC,EAAS,OAAQ,CAChC,MAAMM,EAAWP,IAAc,WACzBW,EAAcJ,EAAWK,GAAuBC,GAChDC,EAAU,CACd,MAAOb,EACP,WAAYC,CACxB,EACgBI,EAAQK,EAAY7M,EAASgN,CAAO,EAC1C5M,EAAS,MAAMmM,EAAUS,EAASR,EAAOC,CAAQ,CACnD,KAAO,CACL,MAAMQ,EAAiBhB,GAASE,EAAS,OACzC,EAAG,CACD,MAAMK,EAAQH,IAAgB,EAAID,EAAc,CAAC,GAAKpM,EAAQ,iBAAmB+M,GAAiB/M,EAASI,CAAM,EACjH,GAAIiM,EAAc,GAAKG,GAAS,KAC9B,MAEFpM,EAAS,MAAMmM,EAAUnM,EAAQoM,CAAK,EACtCH,GACF,OAASA,EAAcY,EACzB,CACA,OAAO7M,CACT,EACIqI,EAAQ,QAAQ,UAClBA,EAAQ,QAAU,IAAM,SACtB,OAAOV,GAAAtF,EAAAgG,EAAQ,SAAQ,YAAhB,YAAAV,EAAA,KAAAtF,EACL4F,EACA,CACE,OAAQI,EAAQ,OAChB,SAAUA,EAAQ,SAClB,KAAMA,EAAQ,QAAQ,KACtB,OAAQA,EAAQ,MAC9B,EACY5J,EAEJ,EAEA4J,EAAQ,QAAUJ,CAEtB,CACJ,CACA,CACA,SAAS0E,GAAiB/M,EAAS,CAAE,MAAAiM,EAAO,WAAAiB,CAAU,EAAI,CACxD,MAAMC,EAAYlB,EAAM,OAAS,EACjC,OAAOA,EAAM,OAAS,EAAIjM,EAAQ,iBAChCiM,EAAMkB,CAAS,EACflB,EACAiB,EAAWC,CAAS,EACpBD,CACJ,EAAM,MACN,CACA,SAASJ,GAAqB9M,EAAS,CAAE,MAAAiM,EAAO,WAAAiB,CAAU,EAAI,OAC5D,OAAOjB,EAAM,OAAS,GAAIxJ,EAAAzC,EAAQ,uBAAR,YAAAyC,EAAA,KAAAzC,EAA+BiM,EAAM,CAAC,EAAGA,EAAOiB,EAAW,CAAC,EAAGA,GAAc,MACzG,+BC5FIE,IAAc3K,GAAA,KAAM,CAStB,YAAYgC,EAAS,GAAI,CARzB/B,EAAA,KAAA2K,GACA3K,EAAA,KAAA4H,IACA5H,EAAA,KAAA4E,IACA5E,EAAA,KAAA4K,IACA5K,EAAA,KAAA6K,IACA7K,EAAA,KAAA8K,IACA9K,EAAA,KAAA+K,IACA/K,EAAA,KAAAgL,IAEE5K,EAAA,KAAKuK,EAAc5I,EAAO,YAAc,IAAIkF,IAC5C7G,EAAA,KAAKwH,GAAiB7F,EAAO,eAAiB,IAAI0G,IAClDrI,EAAA,KAAKwE,GAAkB7C,EAAO,gBAAkB,CAAA,GAChD3B,EAAA,KAAKwK,GAAiC,IAAI,KAC1CxK,EAAA,KAAKyK,GAAoC,IAAI,KAC7CzK,EAAA,KAAK0K,GAAc,EACrB,CACA,OAAQ,CACNjC,GAAA,KAAKiC,IAAL,IACIxK,EAAA,KAAKwK,MAAgB,IACzB1K,EAAA,KAAK2K,GAAoBrK,GAAa,UAAU,MAAOF,GAAY,CAC7DA,IACF,MAAM,KAAK,sBAAqB,EAChCF,EAAA,KAAKqK,GAAY,QAAO,EAE5B,CAAC,GACDvK,EAAA,KAAK4K,GAAqB/J,GAAc,UAAU,MAAOD,GAAW,CAC9DA,IACF,MAAM,KAAK,sBAAqB,EAChCV,EAAA,KAAKqK,GAAY,SAAQ,EAE7B,CAAC,GACH,CACA,SAAU,SACR9B,GAAA,KAAKiC,IAAL,IACIxK,EAAA,KAAKwK,MAAgB,KACzB/K,EAAAO,EAAA,KAAKyK,MAAL,MAAAhL,EAAA,WACAK,EAAA,KAAK2K,GAAoB,SACzB1F,EAAA/E,EAAA,KAAK0K,MAAL,MAAA3F,EAAA,WACAjF,EAAA,KAAK4K,GAAqB,QAC5B,CACA,WAAWzO,EAAS,CAClB,OAAO+D,EAAA,KAAKqK,GAAY,QAAQ,CAAE,GAAGpO,EAAS,YAAa,UAAU,CAAE,EAAE,MAC3E,CACA,WAAWA,EAAS,CAClB,OAAO+D,EAAA,KAAKsH,IAAe,QAAQ,CAAE,GAAGrL,EAAS,OAAQ,SAAS,CAAE,EAAE,MACxE,CAQA,aAAaK,EAAU,OACrB,MAAMU,EAAU,KAAK,oBAAoB,CAAE,SAAAV,CAAQ,CAAE,EACrD,OAAOmD,EAAAO,EAAA,KAAKqK,GAAY,IAAIrN,EAAQ,SAAS,IAAtC,YAAAyC,EAAyC,MAAM,IACxD,CACA,gBAAgBzC,EAAS,CACvB,MAAM2N,EAAmB,KAAK,oBAAoB3N,CAAO,EACnDnB,EAAQmE,EAAA,KAAKqK,GAAY,MAAM,KAAMM,CAAgB,EACrDC,EAAa/O,EAAM,MAAM,KAC/B,OAAI+O,IAAe,OACV,KAAK,WAAW5N,CAAO,GAE5BA,EAAQ,mBAAqBnB,EAAM,cAAcD,GAAiB+O,EAAiB,UAAW9O,CAAK,CAAC,GACjG,KAAK,cAAc8O,CAAgB,EAEnC,QAAQ,QAAQC,CAAU,EACnC,CACA,eAAe3O,EAAS,CACtB,OAAO+D,EAAA,KAAKqK,GAAY,QAAQpO,CAAO,EAAE,IAAI,CAAC,CAAE,SAAAK,EAAU,MAAAsI,KAAY,CACpE,MAAMjG,EAAOiG,EAAM,KACnB,MAAO,CAACtI,EAAUqC,CAAI,CACxB,CAAC,CACH,CACA,aAAarC,EAAUjB,EAAS2B,EAAS,CACvC,MAAM2N,EAAmB,KAAK,oBAAoB,CAAE,SAAArO,CAAQ,CAAE,EACxDT,EAAQmE,EAAA,KAAKqK,GAAY,IAC7BM,EAAiB,SACvB,EACUjM,EAAW7C,GAAA,YAAAA,EAAO,MAAM,KACxB8C,EAAOvD,GAAiBC,EAASqD,CAAQ,EAC/C,GAAIC,IAAS,OAGb,OAAOqB,EAAA,KAAKqK,GAAY,MAAM,KAAMM,CAAgB,EAAE,QAAQhM,EAAM,CAAE,GAAG3B,EAAS,OAAQ,EAAI,CAAE,CAClG,CACA,eAAef,EAASZ,EAAS2B,EAAS,CACxC,OAAO2G,EAAc,MACnB,IAAM3D,EAAA,KAAKqK,GAAY,QAAQpO,CAAO,EAAE,IAAI,CAAC,CAAE,SAAAK,KAAe,CAC5DA,EACA,KAAK,aAAaA,EAAUjB,EAAS2B,CAAO,CACpD,CAAO,CACP,CACE,CACA,cAAcV,EAAU,OACtB,MAAMU,EAAU,KAAK,oBAAoB,CAAE,SAAAV,CAAQ,CAAE,EACrD,OAAOmD,EAAAO,EAAA,KAAKqK,GAAY,IACtBrN,EAAQ,SACd,IAFW,YAAAyC,EAEJ,KACL,CACA,cAAcxD,EAAS,CACrB,MAAM4O,EAAa7K,EAAA,KAAKqK,GACxB1G,EAAc,MAAM,IAAM,CACxBkH,EAAW,QAAQ5O,CAAO,EAAE,QAASJ,GAAU,CAC7CgP,EAAW,OAAOhP,CAAK,CACzB,CAAC,CACH,CAAC,CACH,CACA,aAAaI,EAASe,EAAS,CAC7B,MAAM6N,EAAa7K,EAAA,KAAKqK,GACxB,OAAO1G,EAAc,MAAM,KACzBkH,EAAW,QAAQ5O,CAAO,EAAE,QAASJ,GAAU,CAC7CA,EAAM,MAAK,CACb,CAAC,EACM,KAAK,eACV,CACE,KAAM,SACN,GAAGI,CACb,EACQe,CACR,EACK,CACH,CACA,cAAcf,EAAS6F,EAAgB,GAAI,CACzC,MAAMgJ,EAAyB,CAAE,OAAQ,GAAM,GAAGhJ,CAAa,EACzDiJ,EAAWpH,EAAc,MAC7B,IAAM3D,EAAA,KAAKqK,GAAY,QAAQpO,CAAO,EAAE,IAAKJ,GAAUA,EAAM,OAAOiP,CAAsB,CAAC,CACjG,EACI,OAAO,QAAQ,IAAIC,CAAQ,EAAE,KAAK5P,CAAI,EAAE,MAAMA,CAAI,CACpD,CACA,kBAAkBc,EAASe,EAAU,GAAI,CACvC,OAAO2G,EAAc,MAAM,KACzB3D,EAAA,KAAKqK,GAAY,QAAQpO,CAAO,EAAE,QAASJ,GAAU,CACnDA,EAAM,WAAU,CAClB,CAAC,GACGI,GAAA,YAAAA,EAAS,eAAgB,OACpB,QAAQ,QAAO,EAEjB,KAAK,eACV,CACE,GAAGA,EACH,MAAMA,GAAA,YAAAA,EAAS,eAAeA,GAAA,YAAAA,EAAS,OAAQ,QACzD,EACQe,CACR,EACK,CACH,CACA,eAAef,EAASe,EAAU,GAAI,CACpC,MAAMoC,EAAe,CACnB,GAAGpC,EACH,cAAeA,EAAQ,eAAiB,EAC9C,EACU+N,EAAWpH,EAAc,MAC7B,IAAM3D,EAAA,KAAKqK,GAAY,QAAQpO,CAAO,EAAE,OAAQJ,GAAU,CAACA,EAAM,WAAU,GAAM,CAACA,EAAM,SAAQ,CAAE,EAAE,IAAKA,GAAU,CACjH,IAAIiJ,EAAUjJ,EAAM,MAAM,OAAQuD,CAAY,EAC9C,OAAKA,EAAa,eAChB0F,EAAUA,EAAQ,MAAM3J,CAAI,GAEvBU,EAAM,MAAM,cAAgB,SAAW,QAAQ,QAAO,EAAKiJ,CACpE,CAAC,CACP,EACI,OAAO,QAAQ,IAAIiG,CAAQ,EAAE,KAAK5P,CAAI,CACxC,CACA,WAAW6B,EAAS,CAClB,MAAM2N,EAAmB,KAAK,oBAAoB3N,CAAO,EACrD2N,EAAiB,QAAU,SAC7BA,EAAiB,MAAQ,IAE3B,MAAM9O,EAAQmE,EAAA,KAAKqK,GAAY,MAAM,KAAMM,CAAgB,EAC3D,OAAO9O,EAAM,cACXD,GAAiB+O,EAAiB,UAAW9O,CAAK,CACxD,EAAQA,EAAM,MAAM8O,CAAgB,EAAI,QAAQ,QAAQ9O,EAAM,MAAM,IAAI,CACtE,CACA,cAAcmB,EAAS,CACrB,OAAO,KAAK,WAAWA,CAAO,EAAE,KAAK7B,CAAI,EAAE,MAAMA,CAAI,CACvD,CACA,mBAAmB6B,EAAS,CAC1B,OAAAA,EAAQ,SAAWgM,GAAsBhM,EAAQ,KAAK,EAC/C,KAAK,WAAWA,CAAO,CAChC,CACA,sBAAsBA,EAAS,CAC7B,OAAO,KAAK,mBAAmBA,CAAO,EAAE,KAAK7B,CAAI,EAAE,MAAMA,CAAI,CAC/D,CACA,wBAAwB6B,EAAS,CAC/B,OAAAA,EAAQ,SAAWgM,GAAsBhM,EAAQ,KAAK,EAC/C,KAAK,gBAAgBA,CAAO,CACrC,CACA,uBAAwB,CACtB,OAAI2D,GAAc,WACTX,EAAA,KAAKsH,IAAe,sBAAqB,EAE3C,QAAQ,QAAO,CACxB,CACA,eAAgB,CACd,OAAOtH,EAAA,KAAKqK,EACd,CACA,kBAAmB,CACjB,OAAOrK,EAAA,KAAKsH,GACd,CACA,mBAAoB,CAClB,OAAOtH,EAAA,KAAKsE,GACd,CACA,kBAAkBtH,EAAS,CACzB8C,EAAA,KAAKwE,GAAkBtH,EACzB,CACA,iBAAiBV,EAAUU,EAAS,CAClCgD,EAAA,KAAKsK,IAAe,IAAIvN,GAAQT,CAAQ,EAAG,CACzC,SAAAA,EACA,eAAgBU,CACtB,CAAK,CACH,CACA,iBAAiBV,EAAU,CACzB,MAAM0O,EAAW,CAAC,GAAGhL,EAAA,KAAKsK,IAAe,OAAM,CAAE,EAC3ClN,EAAS,CAAA,EACf,OAAA4N,EAAS,QAASC,GAAiB,CAC7BxO,GAAgBH,EAAU2O,EAAa,QAAQ,GACjD,OAAO,OAAO7N,EAAQ6N,EAAa,cAAc,CAErD,CAAC,EACM7N,CACT,CACA,oBAAoBN,EAAaE,EAAS,CACxCgD,EAAA,KAAKuK,IAAkB,IAAIxN,GAAQD,CAAW,EAAG,CAC/C,YAAAA,EACA,eAAgBE,CACtB,CAAK,CACH,CACA,oBAAoBF,EAAa,CAC/B,MAAMkO,EAAW,CAAC,GAAGhL,EAAA,KAAKuK,IAAkB,OAAM,CAAE,EAC9CnN,EAAS,CAAA,EACf,OAAA4N,EAAS,QAASC,GAAiB,CAC7BxO,GAAgBK,EAAamO,EAAa,WAAW,GACvD,OAAO,OAAO7N,EAAQ6N,EAAa,cAAc,CAErD,CAAC,EACM7N,CACT,CACA,oBAAoBJ,EAAS,CAC3B,GAAIA,EAAQ,WACV,OAAOA,EAET,MAAM2N,EAAmB,CACvB,GAAG3K,EAAA,KAAKsE,IAAgB,QACxB,GAAG,KAAK,iBAAiBtH,EAAQ,QAAQ,EACzC,GAAGA,EACH,WAAY,EAClB,EACI,OAAK2N,EAAiB,YACpBA,EAAiB,UAAYnO,GAC3BmO,EAAiB,SACjBA,CACR,GAEQA,EAAiB,qBAAuB,SAC1CA,EAAiB,mBAAqBA,EAAiB,cAAgB,UAErEA,EAAiB,eAAiB,SACpCA,EAAiB,aAAe,CAAC,CAACA,EAAiB,UAEjD,CAACA,EAAiB,aAAeA,EAAiB,YACpDA,EAAiB,YAAc,gBAE7BA,EAAiB,UAAYzL,KAC/ByL,EAAiB,QAAU,IAEtBA,CACT,CACA,uBAAuB3N,EAAS,CAC9B,OAAIA,GAAA,MAAAA,EAAS,WACJA,EAEF,CACL,GAAGgD,EAAA,KAAKsE,IAAgB,UACxB,IAAGtH,GAAA,YAAAA,EAAS,cAAe,KAAK,oBAAoBA,EAAQ,WAAW,EACvE,GAAGA,EACH,WAAY,EAClB,CACE,CACA,OAAQ,CACNgD,EAAA,KAAKqK,GAAY,MAAK,EACtBrK,EAAA,KAAKsH,IAAe,MAAK,CAC3B,CACF,EA3RE+C,EAAA,YACA/C,GAAA,YACAhD,GAAA,YACAgG,GAAA,YACAC,GAAA,YACAC,GAAA,YACAC,GAAA,YACAC,GAAA,YARgBjL,8ECAdyL,IAAgBzL,GAAA,cAAczE,EAAa,CAC7C,YAAY6L,EAAQ7J,EAAS,CAC3B,MAAK,EAFW0C,EAAA,KAAAyL,GAelBzL,EAAA,KAAA0E,GACA1E,EAAA,KAAA0L,GACA1L,EAAA,KAAA2L,IACA3L,EAAA,KAAA4L,GACA5L,EAAA,KAAA6L,IACA7L,EAAA,KAAA8L,IACA9L,EAAA,KAAA+L,IACA/L,EAAA,KAAAgM,IACAhM,EAAA,KAAAiM,IACAjM,EAAA,KAAAkM,IAGAlM,EAAA,KAAAmM,IACAnM,EAAA,KAAAoM,IACApM,EAAA,KAAAqM,IACArM,EAAA,KAAAsM,IACAtM,EAAA,KAAAuM,GAAgC,IAAI,KA5BlC,KAAK,QAAUjP,EACf8C,EAAA,KAAKsE,EAAUyC,GACf/G,EAAA,KAAK4L,GAAe,MACpB5L,EAAA,KAAK2L,GAAmB7K,GAAe,GAClC,KAAK,QAAQ,+BAChBZ,EAAA,KAAKyL,IAAiB,OACpB,IAAI,MAAM,2DAA2D,CAC7E,EAEI,KAAK,YAAW,EAChB,KAAK,WAAWzO,CAAO,CACzB,CAkBA,aAAc,CACZ,KAAK,QAAU,KAAK,QAAQ,KAAK,IAAI,CACvC,CACA,aAAc,CACR,KAAK,UAAU,OAAS,IAC1BgD,EAAA,KAAKoL,GAAc,YAAY,IAAI,EAC/Bc,GAAmBlM,EAAA,KAAKoL,GAAe,KAAK,OAAO,EACrD1G,EAAA,KAAKyG,EAAAgB,IAAL,WAEA,KAAK,aAAY,EAEnBzH,EAAA,KAAKyG,EAAAiB,IAAL,WAEJ,CACA,eAAgB,CACT,KAAK,gBACR,KAAK,QAAO,CAEhB,CACA,wBAAyB,CACvB,OAAOC,GACLrM,EAAA,KAAKoL,GACL,KAAK,QACL,KAAK,QAAQ,kBACnB,CACE,CACA,0BAA2B,CACzB,OAAOiB,GACLrM,EAAA,KAAKoL,GACL,KAAK,QACL,KAAK,QAAQ,oBACnB,CACE,CACA,SAAU,CACR,KAAK,UAA4B,IAAI,IACrC1G,EAAA,KAAKyG,EAAAmB,IAAL,WACA5H,EAAA,KAAKyG,EAAAoB,IAAL,WACAvM,EAAA,KAAKoL,GAAc,eAAe,IAAI,CACxC,CACA,WAAWpO,EAAS,CAClB,MAAMwP,EAAc,KAAK,QACnBC,EAAYzM,EAAA,KAAKoL,GAEvB,GADA,KAAK,QAAUpL,EAAA,KAAKoE,GAAQ,oBAAoBpH,CAAO,EACnD,KAAK,QAAQ,UAAY,QAAU,OAAO,KAAK,QAAQ,SAAY,WAAa,OAAO,KAAK,QAAQ,SAAY,YAAc,OAAOlB,EAAe,KAAK,QAAQ,QAASkE,EAAA,KAAKoL,EAAa,GAAM,UACpM,MAAM,IAAI,MACR,uEACR,EAEI1G,EAAA,KAAKyG,EAAAuB,IAAL,WACA1M,EAAA,KAAKoL,GAAc,WAAW,KAAK,OAAO,EACtCoB,EAAY,YAAc,CAACvO,GAAoB,KAAK,QAASuO,CAAW,GAC1ExM,EAAA,KAAKoE,GAAQ,cAAa,EAAG,OAAO,CAClC,KAAM,yBACN,MAAOpE,EAAA,KAAKoL,GACZ,SAAU,IAClB,CAAO,EAEH,MAAMuB,EAAU,KAAK,aAAY,EAC7BA,GAAWC,GACb5M,EAAA,KAAKoL,GACLqB,EACA,KAAK,QACLD,CACN,GACM9H,EAAA,KAAKyG,EAAAgB,IAAL,WAEF,KAAK,aAAY,EACbQ,IAAY3M,EAAA,KAAKoL,KAAkBqB,GAAa3Q,EAAe,KAAK,QAAQ,QAASkE,EAAA,KAAKoL,EAAa,IAAMtP,EAAe0Q,EAAY,QAASxM,EAAA,KAAKoL,EAAa,GAAKxP,GAAiB,KAAK,QAAQ,UAAWoE,EAAA,KAAKoL,EAAa,IAAMxP,GAAiB4Q,EAAY,UAAWxM,EAAA,KAAKoL,EAAa,IACrS1G,EAAA,KAAKyG,EAAA0B,IAAL,WAEF,MAAMC,EAAsBpI,EAAA,KAAKyG,EAAA4B,IAAL,WACxBJ,IAAY3M,EAAA,KAAKoL,KAAkBqB,GAAa3Q,EAAe,KAAK,QAAQ,QAASkE,EAAA,KAAKoL,EAAa,IAAMtP,EAAe0Q,EAAY,QAASxM,EAAA,KAAKoL,EAAa,GAAK0B,IAAwB9M,EAAA,KAAKgM,MACvMtH,EAAA,KAAKyG,EAAA6B,IAAL,UAA4BF,EAEhC,CACA,oBAAoB9P,EAAS,CAC3B,MAAMnB,EAAQmE,EAAA,KAAKoE,GAAQ,cAAa,EAAG,MAAMpE,EAAA,KAAKoE,GAASpH,CAAO,EAChEI,EAAS,KAAK,aAAavB,EAAOmB,CAAO,EAC/C,OAAIiQ,GAAsC,KAAM7P,CAAM,IACpD0C,EAAA,KAAKwL,EAAiBlO,GACtB0C,EAAA,KAAK0L,GAAwB,KAAK,SAClC1L,EAAA,KAAKyL,GAAsBvL,EAAA,KAAKoL,GAAc,QAEzChO,CACT,CACA,kBAAmB,CACjB,OAAO4C,EAAA,KAAKsL,EACd,CACA,YAAYlO,EAAQ8P,EAAe,CACjC,OAAO,IAAI,MAAM9P,EAAQ,CACvB,IAAK,CAAC+P,EAAQ9P,KACZ,KAAK,UAAUA,CAAG,EAClB6P,GAAA,MAAAA,EAAgB7P,GACT,QAAQ,IAAI8P,EAAQ9P,CAAG,EAEtC,CAAK,CACH,CACA,UAAUA,EAAK,CACb2C,EAAA,KAAKiM,IAAc,IAAI5O,CAAG,CAC5B,CACA,iBAAkB,CAChB,OAAO2C,EAAA,KAAKoL,EACd,CACA,QAAQ,CAAE,GAAGpO,CAAO,EAAK,GAAI,CAC3B,OAAO,KAAK,MAAM,CAChB,GAAGA,CACT,CAAK,CACH,CACA,gBAAgBA,EAAS,CACvB,MAAM2N,EAAmB3K,EAAA,KAAKoE,GAAQ,oBAAoBpH,CAAO,EAC3DnB,EAAQmE,EAAA,KAAKoE,GAAQ,cAAa,EAAG,MAAMpE,EAAA,KAAKoE,GAASuG,CAAgB,EAC/E,OAAO9O,EAAM,MAAK,EAAG,KAAK,IAAM,KAAK,aAAaA,EAAO8O,CAAgB,CAAC,CAC5E,CACA,MAAMvL,EAAc,CAClB,OAAOsF,EAAA,KAAKyG,EAAAgB,IAAL,UAAmB,CACxB,GAAG/M,EACH,cAAeA,EAAa,eAAiB,EACnD,GAAO,KAAK,KACN,KAAK,aAAY,EACVY,EAAA,KAAKsL,GACb,CACH,CA4DA,aAAazP,EAAOmB,EAAS,OAC3B,MAAMyP,EAAYzM,EAAA,KAAKoL,GACjBoB,EAAc,KAAK,QACnBY,EAAapN,EAAA,KAAKsL,GAClB+B,EAAkBrN,EAAA,KAAKuL,IACvB+B,EAAoBtN,EAAA,KAAKwL,IAEzB+B,EADc1R,IAAU4Q,EACU5Q,EAAM,MAAQmE,EAAA,KAAKqL,IACrD,CAAE,MAAAzG,CAAK,EAAK/I,EAClB,IAAI2K,EAAW,CAAE,GAAG5B,CAAK,EACrB4I,EAAoB,GACpB7O,EACJ,GAAI3B,EAAQ,mBAAoB,CAC9B,MAAM2P,EAAU,KAAK,aAAY,EAC3Bc,GAAe,CAACd,GAAWT,GAAmBrQ,EAAOmB,CAAO,EAC5D0Q,GAAkBf,GAAWC,GAAsB/Q,EAAO4Q,EAAWzP,EAASwP,CAAW,GAC3FiB,IAAgBC,MAClBlH,EAAW,CACT,GAAGA,EACH,GAAGD,GAAW3B,EAAM,KAAM/I,EAAM,OAAO,CACjD,GAEUmB,EAAQ,qBAAuB,gBACjCwJ,EAAS,YAAc,OAE3B,CACA,GAAI,CAAE,MAAAhE,EAAO,eAAAmL,EAAgB,OAAA9Q,CAAM,EAAK2J,EACxC7H,EAAO6H,EAAS,KAChB,IAAIoH,EAAa,GACjB,GAAI5Q,EAAQ,kBAAoB,QAAU2B,IAAS,QAAU9B,IAAW,UAAW,CACjF,IAAIgR,EACAT,GAAA,MAAAA,EAAY,mBAAqBpQ,EAAQ,mBAAoBsQ,GAAA,YAAAA,EAAmB,kBAClFO,EAAkBT,EAAW,KAC7BQ,EAAa,IAEbC,EAAkB,OAAO7Q,EAAQ,iBAAoB,WAAaA,EAAQ,iBACxEyC,EAAAO,EAAA,KAAK6L,MAAL,YAAApM,EAAgC,MAAM,KACtCO,EAAA,KAAK6L,GACf,EAAY7O,EAAQ,gBAEV6Q,IAAoB,SACtBhR,EAAS,UACT8B,EAAOF,GACL2O,GAAA,YAAAA,EAAY,KACZS,EACA7Q,CACV,EACQwQ,EAAoB,GAExB,CACA,GAAIxQ,EAAQ,QAAU2B,IAAS,QAAU,CAACiP,EACxC,GAAIR,GAAczO,KAAS0O,GAAA,YAAAA,EAAiB,OAAQrQ,EAAQ,SAAWgD,EAAA,KAAK2L,IAC1EhN,EAAOqB,EAAA,KAAK4L,QAEZ,IAAI,CACF9L,EAAA,KAAK6L,GAAY3O,EAAQ,QACzB2B,EAAO3B,EAAQ,OAAO2B,CAAI,EAC1BA,EAAOF,GAAY2O,GAAA,YAAAA,EAAY,KAAMzO,EAAM3B,CAAO,EAClD8C,EAAA,KAAK8L,GAAgBjN,GACrBmB,EAAA,KAAK4L,GAAe,KACtB,OAASoC,EAAa,CACpBhO,EAAA,KAAK4L,GAAeoC,EACtB,CAGA9N,EAAA,KAAK0L,MACPlJ,EAAQxC,EAAA,KAAK0L,IACb/M,EAAOqB,EAAA,KAAK4L,IACZ+B,EAAiB,KAAK,IAAG,EACzB9Q,EAAS,SAEX,MAAMkR,EAAavH,EAAS,cAAgB,WACtCwH,EAAYnR,IAAW,UACvBoR,EAAUpR,IAAW,QACrBqR,EAAYF,GAAaD,EACzBtH,EAAU9H,IAAS,OA6BnBwP,EA5BS,CACb,OAAAtR,EACA,YAAa2J,EAAS,YACtB,UAAAwH,EACA,UAAWnR,IAAW,UACtB,QAAAoR,EACA,iBAAkBC,EAClB,UAAAA,EACA,KAAAvP,EACA,cAAe6H,EAAS,cACxB,MAAAhE,EACA,eAAAmL,EACA,aAAcnH,EAAS,kBACvB,cAAeA,EAAS,mBACxB,iBAAkBA,EAAS,iBAC3B,UAAWA,EAAS,gBAAkB,GAAKA,EAAS,iBAAmB,EACvE,oBAAqBA,EAAS,gBAAkB+G,EAAkB,iBAAmB/G,EAAS,iBAAmB+G,EAAkB,iBACnI,WAAAQ,EACA,aAAcA,GAAc,CAACC,EAC7B,eAAgBC,GAAW,CAACxH,EAC5B,SAAUD,EAAS,cAAgB,SACnC,kBAAAgH,EACA,eAAgBS,GAAWxH,EAC3B,QAAS2H,GAAQvS,EAAOmB,CAAO,EAC/B,QAAS,KAAK,QACd,QAASgD,EAAA,KAAKyL,IACd,UAAW3P,EAAekB,EAAQ,QAASnB,CAAK,IAAM,EAC5D,EAEI,GAAI,KAAK,QAAQ,8BAA+B,CAC9C,MAAMwS,EAA8BvN,IAAa,CAC3CqN,EAAW,SAAW,QACxBrN,GAAS,OAAOqN,EAAW,KAAK,EACvBA,EAAW,OAAS,QAC7BrN,GAAS,QAAQqN,EAAW,IAAI,CAEpC,EACMG,GAAmB,IAAM,CAC7B,MAAMC,GAAUzO,EAAA,KAAK2L,GAAmB0C,EAAW,QAAUvN,GAAe,GAC5EyN,EAA2BE,EAAO,CACpC,EACMC,GAAexO,EAAA,KAAKyL,IAC1B,OAAQ+C,GAAa,OAAM,CACzB,IAAK,UACC3S,EAAM,YAAc4Q,EAAU,WAChC4B,EAA2BG,EAAY,EAEzC,MACF,IAAK,aACCL,EAAW,SAAW,SAAWA,EAAW,OAASK,GAAa,QACpEF,GAAgB,EAElB,MACF,IAAK,YACCH,EAAW,SAAW,SAAWA,EAAW,QAAUK,GAAa,SACrEF,GAAgB,EAElB,KACV,CACI,CACA,OAAOH,CACT,CACA,cAAe,CACb,MAAMf,EAAapN,EAAA,KAAKsL,GAClB6C,EAAa,KAAK,aAAanO,EAAA,KAAKoL,GAAe,KAAK,OAAO,EAMrE,GALAtL,EAAA,KAAKyL,GAAsBvL,EAAA,KAAKoL,GAAc,OAC9CtL,EAAA,KAAK0L,GAAwB,KAAK,SAC9BxL,EAAA,KAAKuL,IAAoB,OAAS,QACpCzL,EAAA,KAAK+L,GAA4B7L,EAAA,KAAKoL,IAEpCnN,GAAoBkQ,EAAYf,CAAU,EAC5C,OAEFtN,EAAA,KAAKwL,EAAiB6C,GACtB,MAAMM,EAAwB,IAAM,CAClC,GAAI,CAACrB,EACH,MAAO,GAET,KAAM,CAAE,oBAAAsB,GAAwB,KAAK,QAC/BC,EAA2B,OAAOD,GAAwB,WAAaA,EAAmB,EAAKA,EACrG,GAAIC,IAA6B,OAAS,CAACA,GAA4B,CAAC3O,EAAA,KAAKiM,IAAc,KACzF,MAAO,GAET,MAAM2C,EAAgB,IAAI,IACxBD,GAA4B3O,EAAA,KAAKiM,GACzC,EACM,OAAI,KAAK,QAAQ,cACf2C,EAAc,IAAI,OAAO,EAEpB,OAAO,KAAK5O,EAAA,KAAKsL,EAAc,EAAE,KAAMjO,GAAQ,CACpD,MAAMwR,EAAWxR,EAEjB,OADgB2C,EAAA,KAAKsL,GAAeuD,CAAQ,IAAMzB,EAAWyB,CAAQ,GACnDD,EAAc,IAAIC,CAAQ,CAC9C,CAAC,CACH,EACAnK,EAAA,KAAKyG,EAAA2D,IAAL,UAAa,CAAE,UAAWL,EAAqB,CAAE,EACnD,CAcA,eAAgB,CACd,KAAK,aAAY,EACb,KAAK,gBACP/J,EAAA,KAAKyG,EAAAiB,IAAL,UAEJ,CAcF,EAnZEhI,EAAA,YACAgH,EAAA,YACAC,GAAA,YACAC,EAAA,YACAC,GAAA,YACAC,GAAA,YACAC,GAAA,YACAC,GAAA,YACAC,GAAA,YACAC,GAAA,YAGAC,GAAA,YACAC,GAAA,YACAC,GAAA,YACAC,GAAA,YACAC,GAAA,YA/BkBd,EAAA,YA0JlBgB,GAAa,SAAC/M,EAAc,CAC1BsF,EAAA,KAAKyG,EAAAuB,IAAL,WACA,IAAI5H,EAAU9E,EAAA,KAAKoL,GAAc,MAC/B,KAAK,QACLhM,CACN,EACI,OAAKA,GAAA,MAAAA,EAAc,eACjB0F,EAAUA,EAAQ,MAAM3J,CAAI,GAEvB2J,CACT,EACA+H,GAAmB,UAAG,CACpBnI,EAAA,KAAKyG,EAAAmB,IAAL,WACA,MAAM3Q,EAAYC,GAChB,KAAK,QAAQ,UACboE,EAAA,KAAKoL,EACX,EACI,GAAIlQ,IAAY8E,EAAA,KAAKsL,GAAe,SAAW,CAAC/P,GAAeI,CAAS,EACtE,OAGF,MAAM4C,EADO9C,GAAeuE,EAAA,KAAKsL,GAAe,cAAe3P,CAAS,EACjD,EACvBmE,EAAA,KAAKgM,GAAkB,WAAW,IAAM,CACjC9L,EAAA,KAAKsL,GAAe,SACvB,KAAK,aAAY,CAErB,EAAG/M,CAAO,EACZ,EACAwO,GAAuB,UAAG,CACxB,OAAQ,OAAO,KAAK,QAAQ,iBAAoB,WAAa,KAAK,QAAQ,gBAAgB/M,EAAA,KAAKoL,EAAa,EAAI,KAAK,QAAQ,kBAAoB,EACnJ,EACA4B,GAAsB,SAAC+B,EAAc,CACnCrK,EAAA,KAAKyG,EAAAoB,IAAL,WACAzM,EAAA,KAAKkM,GAA0B+C,GAC3B,EAAA7T,IAAYY,EAAe,KAAK,QAAQ,QAASkE,EAAA,KAAKoL,EAAa,IAAM,IAAS,CAAC7P,GAAeyE,EAAA,KAAKgM,GAAuB,GAAKhM,EAAA,KAAKgM,MAA4B,IAGxKlM,EAAA,KAAKiM,GAAqB,YAAY,IAAM,EACtC,KAAK,QAAQ,6BAA+B3L,GAAa,UAAS,IACpEsE,EAAA,KAAKyG,EAAAgB,IAAL,UAEJ,EAAGnM,EAAA,KAAKgM,GAAuB,EACjC,EACAI,GAAa,UAAG,CACd1H,EAAA,KAAKyG,EAAA0B,IAAL,WACAnI,EAAA,KAAKyG,EAAA6B,IAAL,UAA4BtI,EAAA,KAAKyG,EAAA4B,IAAL,WAC9B,EACAT,GAAkB,UAAG,CACftM,EAAA,KAAK8L,MACP,aAAa9L,EAAA,KAAK8L,GAAe,EACjChM,EAAA,KAAKgM,GAAkB,QAE3B,EACAS,GAAqB,UAAG,CAClBvM,EAAA,KAAK+L,MACP,cAAc/L,EAAA,KAAK+L,GAAkB,EACrCjM,EAAA,KAAKiM,GAAqB,QAE9B,EA8KAW,GAAY,UAAG,CACb,MAAM7Q,EAAQmE,EAAA,KAAKoE,GAAQ,cAAa,EAAG,MAAMpE,EAAA,KAAKoE,GAAS,KAAK,OAAO,EAC3E,GAAIvI,IAAUmE,EAAA,KAAKoL,GACjB,OAEF,MAAMqB,EAAYzM,EAAA,KAAKoL,GACvBtL,EAAA,KAAKsL,EAAgBvP,GACrBiE,EAAA,KAAKuL,GAA4BxP,EAAM,OACnC,KAAK,iBACP4Q,GAAA,MAAAA,EAAW,eAAe,MAC1B5Q,EAAM,YAAY,IAAI,EAE1B,EAOAiT,GAAO,SAACE,EAAe,CACrBrL,EAAc,MAAM,IAAM,CACpBqL,EAAc,WAChB,KAAK,UAAU,QAAS/T,GAAa,CACnCA,EAAS+E,EAAA,KAAKsL,EAAc,CAC9B,CAAC,EAEHtL,EAAA,KAAKoE,GAAQ,cAAa,EAAG,OAAO,CAClC,MAAOpE,EAAA,KAAKoL,GACZ,KAAM,wBACd,CAAO,CACH,CAAC,CACH,EAjakB3L,IAmapB,SAASwP,GAAkBpT,EAAOmB,EAAS,CACzC,OAAOlB,EAAekB,EAAQ,QAASnB,CAAK,IAAM,IAASA,EAAM,MAAM,OAAS,QAAU,EAAEA,EAAM,MAAM,SAAW,SAAWmB,EAAQ,eAAiB,GACzJ,CACA,SAASkP,GAAmBrQ,EAAOmB,EAAS,CAC1C,OAAOiS,GAAkBpT,EAAOmB,CAAO,GAAKnB,EAAM,MAAM,OAAS,QAAUwQ,GAAcxQ,EAAOmB,EAASA,EAAQ,cAAc,CACjI,CACA,SAASqP,GAAcxQ,EAAOmB,EAASkS,EAAO,CAC5C,GAAIpT,EAAekB,EAAQ,QAASnB,CAAK,IAAM,IAASD,GAAiBoB,EAAQ,UAAWnB,CAAK,IAAM,SAAU,CAC/G,MAAML,EAAQ,OAAO0T,GAAU,WAAaA,EAAMrT,CAAK,EAAIqT,EAC3D,OAAO1T,IAAU,UAAYA,IAAU,IAAS4S,GAAQvS,EAAOmB,CAAO,CACxE,CACA,MAAO,EACT,CACA,SAAS4P,GAAsB/Q,EAAO4Q,EAAWzP,EAASwP,EAAa,CACrE,OAAQ3Q,IAAU4Q,GAAa3Q,EAAe0Q,EAAY,QAAS3Q,CAAK,IAAM,MAAW,CAACmB,EAAQ,UAAYnB,EAAM,MAAM,SAAW,UAAYuS,GAAQvS,EAAOmB,CAAO,CACzK,CACA,SAASoR,GAAQvS,EAAOmB,EAAS,CAC/B,OAAOlB,EAAekB,EAAQ,QAASnB,CAAK,IAAM,IAASA,EAAM,cAAcD,GAAiBoB,EAAQ,UAAWnB,CAAK,CAAC,CAC3H,CACA,SAASoR,GAAsCjI,EAAUmK,EAAkB,CACzE,MAAK,CAAAlR,GAAoB+G,EAAS,iBAAgB,EAAImK,CAAgB,CAIxE,0BCtcIC,IAAmB3P,GAAA,cAAczE,EAAa,CAKhD,YAAY6L,EAAQ7J,EAAS,CAC3B,MAAK,EANc0C,EAAA,KAAA2P,GACrB3P,EAAA,KAAA0E,IACA1E,EAAA,KAAA4L,IACA5L,EAAA,KAAA4P,GACA5P,EAAA,KAAA6P,GAGEzP,EAAA,KAAKsE,GAAUyC,GACf,KAAK,WAAW7J,CAAO,EACvB,KAAK,YAAW,EAChB0H,EAAA,KAAK2K,EAAAG,IAAL,UACF,CACA,aAAc,CACZ,KAAK,OAAS,KAAK,OAAO,KAAK,IAAI,EACnC,KAAK,MAAQ,KAAK,MAAM,KAAK,IAAI,CACnC,CACA,WAAWxS,EAAS,OAClB,MAAMwP,EAAc,KAAK,QACzB,KAAK,QAAUxM,EAAA,KAAKoE,IAAQ,uBAAuBpH,CAAO,EACrDiB,GAAoB,KAAK,QAASuO,CAAW,GAChDxM,EAAA,KAAKoE,IAAQ,iBAAgB,EAAG,OAAO,CACrC,KAAM,yBACN,SAAUpE,EAAA,KAAKsP,GACf,SAAU,IAClB,CAAO,EAEC9C,GAAA,MAAAA,EAAa,aAAe,KAAK,QAAQ,aAAezP,GAAQyP,EAAY,WAAW,IAAMzP,GAAQ,KAAK,QAAQ,WAAW,EAC/H,KAAK,MAAK,IACD0C,EAAAO,EAAA,KAAKsP,KAAL,YAAA7P,EAAuB,MAAM,UAAW,WACjDO,EAAA,KAAKsP,GAAiB,WAAW,KAAK,OAAO,CAEjD,CACA,eAAgB,OACT,KAAK,iBACR7P,EAAAO,EAAA,KAAKsP,KAAL,MAAA7P,EAAuB,eAAe,KAE1C,CACA,iBAAiB4G,EAAQ,CACvB3B,EAAA,KAAK2K,EAAAG,IAAL,WACA9K,EAAA,KAAK2K,EAAAP,IAAL,UAAazI,EACf,CACA,kBAAmB,CACjB,OAAOrG,EAAA,KAAKsL,GACd,CACA,OAAQ,QACN7L,EAAAO,EAAA,KAAKsP,KAAL,MAAA7P,EAAuB,eAAe,MACtCK,EAAA,KAAKwP,EAAmB,QACxB5K,EAAA,KAAK2K,EAAAG,IAAL,WACA9K,EAAA,KAAK2K,EAAAP,IAAL,UACF,CACA,OAAOvH,EAAWvK,EAAS,OACzB,OAAA8C,EAAA,KAAKyP,EAAiBvS,IACtByC,EAAAO,EAAA,KAAKsP,KAAL,MAAA7P,EAAuB,eAAe,MACtCK,EAAA,KAAKwP,EAAmBtP,EAAA,KAAKoE,IAAQ,iBAAgB,EAAG,MAAMpE,EAAA,KAAKoE,IAAS,KAAK,OAAO,GACxFpE,EAAA,KAAKsP,GAAiB,YAAY,IAAI,EAC/BtP,EAAA,KAAKsP,GAAiB,QAAQ/H,CAAS,CAChD,CAoCF,EA3FEnD,GAAA,YACAkH,GAAA,YACAgE,EAAA,YACAC,EAAA,YAJqBF,EAAA,YAyDrBG,GAAa,UAAG,OACd,MAAM5K,IAAQnF,EAAAO,EAAA,KAAKsP,KAAL,YAAA7P,EAAuB,QAAS+E,GAAe,EAC7D1E,EAAA,KAAKwL,GAAiB,CACpB,GAAG1G,EACH,UAAWA,EAAM,SAAW,UAC5B,UAAWA,EAAM,SAAW,UAC5B,QAASA,EAAM,SAAW,QAC1B,OAAQA,EAAM,SAAW,OACzB,OAAQ,KAAK,OACb,MAAO,KAAK,KAClB,EACE,EACAkK,GAAO,SAACzI,EAAQ,CACd1C,EAAc,MAAM,IAAM,qBACxB,GAAI3D,EAAA,KAAKuP,IAAkB,KAAK,aAAY,EAAI,CAC9C,MAAMhI,EAAYvH,EAAA,KAAKsL,IAAe,UAChC7F,EAAUzF,EAAA,KAAKsL,IAAe,SAChCjF,GAAA,YAAAA,EAAQ,QAAS,YACnBtB,GAAAtF,EAAAO,EAAA,KAAKuP,IAAe,YAApB,MAAAxK,EAAA,KAAAtF,EAAgC4G,EAAO,KAAMkB,EAAW9B,IACxDG,GAAAD,EAAA3F,EAAA,KAAKuP,IAAe,YAApB,MAAA3J,EAAA,KAAAD,EAAgCU,EAAO,KAAM,KAAMkB,EAAW9B,KACrDY,GAAA,YAAAA,EAAQ,QAAS,WAC1BR,GAAAC,EAAA9F,EAAA,KAAKuP,IAAe,UAApB,MAAA1J,EAAA,KAAAC,EAA8BO,EAAO,MAAOkB,EAAW9B,IACvDM,GAAAC,EAAAhG,EAAA,KAAKuP,IAAe,YAApB,MAAAxJ,EAAA,KAAAC,EACE,OACAK,EAAO,MACPkB,EACA9B,GAGN,CACA,KAAK,UAAU,QAASxK,GAAa,CACnCA,EAAS+E,EAAA,KAAKsL,GAAc,CAC9B,CAAC,CACH,CAAC,CACH,EA3FqB7L,ICAnBgQ,GAAqBC,EAAAA,cACvB,MACF,EACIC,GAAkBC,GAAgB,CACpC,MAAM/I,EAASgJ,EAAAA,WAAiBJ,EAAkB,EAIlD,GAAI,CAAC5I,EACH,MAAM,IAAI,MAAM,wDAAwD,EAE1E,OAAOA,CACT,EACIiJ,GAAsB,CAAC,CACzB,OAAAjJ,EACA,SAAAkJ,CACF,KACEC,EAAAA,UAAgB,KACdnJ,EAAO,MAAK,EACL,IAAM,CACXA,EAAO,QAAO,CAChB,GACC,CAACA,CAAM,CAAC,EACYoJ,GAAAA,IAAIR,GAAmB,SAAU,CAAE,MAAO5I,EAAQ,SAAAkJ,EAAU,GCxBjFG,GAAqBR,EAAAA,cAAoB,EAAK,EAC9CS,GAAiB,IAAMN,EAAAA,WAAiBK,EAAkB,EACpCA,GAAmB,SCD7C,SAASE,IAAc,CACrB,IAAIC,EAAU,GACd,MAAO,CACL,WAAY,IAAM,CAChBA,EAAU,EACZ,EACA,MAAO,IAAM,CACXA,EAAU,EACZ,EACA,QAAS,IACAA,CAEb,CACA,CACA,IAAIC,GAAiCZ,EAAAA,cAAoBU,IAAa,EAClEG,GAA6B,IAAMV,EAAAA,WAAiBS,EAA8B,ECflFE,GAAkC,CAACxT,EAASyT,IAAuB,EACjEzT,EAAQ,UAAYA,EAAQ,cAAgBA,EAAQ,iCACjDyT,EAAmB,YACtBzT,EAAQ,aAAe,IAG7B,EACI0T,GAA8BD,GAAuB,CACvDT,EAAAA,UAAgB,IAAM,CACpBS,EAAmB,WAAU,CAC/B,EAAG,CAACA,CAAkB,CAAC,CACzB,EACIE,GAAc,CAAC,CACjB,OAAAvT,EACA,mBAAAqT,EACA,aAAAnR,EACA,MAAAzD,EACA,SAAA+U,CACF,IACSxT,EAAO,SAAW,CAACqT,EAAmB,QAAO,GAAM,CAACrT,EAAO,YAAcvB,IAAU+U,GAAYxT,EAAO,OAAS,QAAUiC,GAAiBC,EAAc,CAAClC,EAAO,MAAOvB,CAAK,CAAC,GCtBlLgV,GAAwBlG,GAAqB,CAC/C,GAAIA,EAAiB,SAAU,CAC7B,MAAMmG,EAAStV,GAAUA,IAAU,SAAWA,EAAQ,KAAK,IAAIA,GAAS,IAAK,GAAG,EAC1EuV,EAAoBpG,EAAiB,UAC3CA,EAAiB,UAAY,OAAOoG,GAAsB,WAAa,IAAItN,IAASqN,EAAMC,EAAkB,GAAGtN,CAAI,CAAC,EAAIqN,EAAMC,CAAiB,EAC3I,OAAOpG,EAAiB,QAAW,WACrCA,EAAiB,OAAS,KAAK,IAAIA,EAAiB,OAAQ,GAAG,EAEnE,CACF,EACIqG,GAAY,CAAC5T,EAAQ6T,IAAgB7T,EAAO,WAAaA,EAAO,YAAc,CAAC6T,EAC/EC,GAAgB,CAACvG,EAAkBvN,KAAWuN,GAAA,YAAAA,EAAkB,WAAYvN,EAAO,UACnF+T,GAAkB,CAACxG,EAAkB3F,EAAUyL,IAAuBzL,EAAS,gBAAgB2F,CAAgB,EAAE,MAAM,IAAM,CAC/H8F,EAAmB,WAAU,CAC/B,CAAC,ECGD,SAASW,GAAapU,EAASqU,EAAUzB,EAAa,eAQpD,MAAMqB,EAAcd,GAAA,EACdM,EAAqBF,GAAA,EACrB1J,EAAS8I,GAA0B,EACnChF,EAAmB9D,EAAO,oBAAoB7J,CAAO,GAC3D+H,GAAAtF,EAAAoH,EAAO,oBAAoB,UAA3B,YAAApH,EAAoC,4BAApC,MAAAsF,EAAA,KAAAtF,EACEkL,GASFA,EAAiB,mBAAqBsG,EAAc,cAAgB,aACpEJ,GAAqBlG,CAAgB,EACrC6F,GAAgC7F,EAAkB8F,CAAkB,EACpEC,GAA2BD,CAAkB,EAC7C,MAAMa,EAAkB,CAACzK,EAAO,gBAAgB,IAAI8D,EAAiB,SAAS,EACxE,CAAC3F,CAAQ,EAAIuM,EAAAA,SACjB,IAAM,IAAIF,EACRxK,EACA8D,CAAA,CACF,EAEIvN,EAAS4H,EAAS,oBAAoB2F,CAAgB,EACtD6G,EAAkB,CAACP,GAAejU,EAAQ,aAAe,GAgB/D,GAfAyU,EAAAA,qBACEC,EAAAA,YACGC,GAAkB,CACjB,MAAMC,EAAcJ,EAAkBxM,EAAS,UAAUrB,EAAc,WAAWgO,CAAa,CAAC,EAAIxW,EACpG,OAAA6J,EAAS,aAAA,EACF4M,CACT,EACA,CAAC5M,EAAUwM,CAAe,CAAA,EAE5B,IAAMxM,EAAS,iBAAA,EACf,IAAMA,EAAS,iBAAA,CAAiB,EAElCgL,EAAAA,UAAgB,IAAM,CACpBhL,EAAS,WAAW2F,CAAgB,CACtC,EAAG,CAACA,EAAkB3F,CAAQ,CAAC,EAC3BkM,GAAcvG,EAAkBvN,CAAM,EACxC,MAAM+T,GAAgBxG,EAAkB3F,EAAUyL,CAAkB,EAEtE,GAAIE,GAAY,CACd,OAAAvT,EACA,mBAAAqT,EACA,aAAc9F,EAAiB,aAC/B,MAAO9D,EAAO,cAAA,EAAgB,IAAI8D,EAAiB,SAAS,EAC5D,SAAUA,EAAiB,QAAA,CAC5B,EACC,MAAMvN,EAAO,MAOf,IAJAwI,GAAAD,EAAAkB,EAAO,oBAAoB,UAA3B,YAAAlB,EAAoC,2BAApC,MAAAC,EAAA,KAAAD,EACEgF,EACAvN,GAEEuN,EAAiB,+BAAiC,CAACzP,IAAY8V,GAAU5T,EAAQ6T,CAAW,EAAG,CACjG,MAAMnM,EAAUwM,EAEdH,GAAgBxG,EAAkB3F,EAAUyL,CAAkB,GAG9D3K,EAAAe,EAAO,cAAA,EAAgB,IAAI8D,EAAiB,SAAS,IAArD,YAAA7E,EAAwD,QAE1DhB,GAAA,MAAAA,EAAS,MAAM3J,GAAM,QAAQ,IAAM,CACjC6J,EAAS,aAAA,CACX,EACF,CACA,OAAQ2F,EAAiB,oBAAqDvN,EAA/B4H,EAAS,YAAY5H,CAAM,CAC5E,CC9FA,SAASyU,GAAS7U,EAAS4S,EAAa,CACtC,OAAOwB,GAAapU,EAASkO,EAA0B,CACzD,CCIA,SAAS4G,GAAY9U,EAAS4S,EAAa,CACzC,MAAM/I,EAAS8I,GAA0B,EACnC,CAAC3K,CAAQ,EAAIuM,EAAAA,SACjB,IAAM,IAAInC,GACRvI,EACA7J,CACN,CACA,EACEgT,EAAAA,UAAgB,IAAM,CACpBhL,EAAS,WAAWhI,CAAO,CAC7B,EAAG,CAACgI,EAAUhI,CAAO,CAAC,EACtB,MAAMI,EAASqU,EAAAA,qBACbC,EAAAA,YACGC,GAAkB3M,EAAS,UAAUrB,EAAc,WAAWgO,CAAa,CAAC,EAC7E,CAAC3M,CAAQ,CACf,EACI,IAAMA,EAAS,iBAAgB,EAC/B,IAAMA,EAAS,iBAAgB,CACnC,EACQ+M,EAASL,EAAAA,YACb,CAACnK,EAAWyK,IAAkB,CAC5BhN,EAAS,OAAOuC,EAAWyK,CAAa,EAAE,MAAM7W,CAAI,CACtD,EACA,CAAC6J,CAAQ,CACb,EACE,GAAI5H,EAAO,OAASiC,GAAiB2F,EAAS,QAAQ,aAAc,CAAC5H,EAAO,KAAK,CAAC,EAChF,MAAMA,EAAO,MAEf,MAAO,CAAE,GAAGA,EAAQ,OAAA2U,EAAQ,YAAa3U,EAAO,MAAM,CACxD", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]}