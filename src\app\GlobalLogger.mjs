import fs from 'fs';
import * as util from 'util';

export default new (class {
    constructor() {
        if (!fs.existsSync('./src/logs')) {
            fs.mkdirSync('./src/logs');
        }

        const today = new Date();
        const logFileName = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(
            2,
            '0'
        )}-${String(today.getDate()).padStart(2, '0')}_app.log`;

        this.logStream = fs.createWriteStream(`./src/logs/${logFileName}`, {
            flags: 'a',
        });

        this.captureConsole();
        this.captureUnhandledRejections();
    }

    log(...args) {
        const message = util.format(...args);
        const timestamp = new Date().toISOString();
        const formattedLog = `[${timestamp}] ${message}\n`;

        this.logStream.write(formattedLog);

        process.stdout.write(formattedLog);
    }

    captureConsole() {
        console.log = this.log.bind(this);
        console.error = this.log.bind(this);
    }

    captureUnhandledRejections() {
        process.on('unhandledRejection', (reason, promise) => {
            this.log('Unhandled Rejection at:', promise, 'reason:', reason);
        });

        process.on('uncaughtException', (error) => {
            this.log('Uncaught Exception:', error);
            process.exit(1);
        });
    }
})();
