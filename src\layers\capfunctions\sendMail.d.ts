/**
 * Type definitions for the sendMail Lambda function
 */

// Email recipient types
type EmailRecipient = string | string[];

// Email parameters interface
interface EmailParams {
  /** Primary recipient(s) - required */
  to: EmailRecipient;
  /** Email subject - required */
  subject: string;
  /** Email body content - required */
  body: string;
  /** CC recipient(s) - optional */
  cc?: EmailRecipient;
  /** BCC recipient(s) - optional */
  bcc?: EmailRecipient;
  /** Reply-to address - optional */
  replyTo?: string;
}

// Success response interface
interface SuccessResponse {
  /** HTTP status code */
  statusCode: 200;
  /** Response body */
  body: string;
}

// Error response interface
interface ErrorResponse {
  /** HTTP status code */
  statusCode: 400 | 500;
  /** Response body with error details */
  body: string;
}

// Union type for all possible responses
type LambdaResponse = SuccessResponse | ErrorResponse;

/**
 * AWS Lambda handler function for sending emails
 * @param event - Email parameters
 * @param context - Lambda execution context
 * @returns Promise resolving to Lambda response
 */
export declare const handler: (event: EmailParams, context: any) => Promise<LambdaResponse>;
