const path = require('path');
const { getParams, execTerminal } = require('./functions');

const updateLayer = async () => {
    try {
        const { profile, region, layerName } = await getParams(['profile', 'layerName']);

        const layerDIR = path.resolve(
            __dirname,
            '..',
            'src',
            'layers',
            layerName,
            layerName + '.zip'
        );

        await execTerminal(
            `aws lambda publish-layer-version --layer-name ${layerName} --description "update ${layerName}" --zip-file fileb://${layerDIR} --compatible-runtimes nodejs18.x nodejs20.x nodejs22.x --profile ${profile} --region ${region}`
        );

        //

        console.log('\nFinalizado!\n');
    } catch (error) {
        console.log('\n');
        console.log('\x1b[31m%s\x1b[0m', 'Catch no updateLayer():');
        console.log(error);
        console.log(error.message);
        console.log('\n');
    } finally {
        process.exit();
    }
};

updateLayer();
