import { describe, test, expect, beforeEach, jest } from '@jest/globals';

// Mock dependencies
const mockDatabase = {
    getUserSession: jest.fn(),
    createUserSession: jest.fn(),
    updateUserSession: jest.fn(),
    saveUserData: jest.fn(),
    logInteraction: jest.fn(),
    findClinic: jest.fn(),
    markTermsAccepted: jest.fn(),
    deleteUserSession: jest.fn(),
    executeQuery: jest.fn(),
    getInteractionHistory: jest.fn(),
    cleanupOldSessions: jest.fn()
};

const mockMessageBuilder = {
    buildGreeting: jest.fn(() => 'Olá, tudo bem?'),
    buildClinicRequest: jest.fn(() => 'Qual o nome da clínica?'),
    buildNameRequest: jest.fn(() => 'Qual é o seu nome?'),
    buildCPFRequest: jest.fn(() => 'Qual seu CPF?'),
    buildEmailRequest: jest.fn(() => 'Qual seu e-mail?'),
    buildDataConfirmation: jest.fn(() => 'Confirme os dados'),
    buildTermsPresentation: jest.fn(() => 'Termos e condições'),
    buildCompletion: jest.fn(() => 'Obrigado!'),
    buildExitMessage: jest.fn(() => 'Até logo!'),
    buildInvalidName: jest.fn(() => 'Nome inválido'),
    buildInvalidCPF: jest.fn(() => 'CPF inválido'),
    buildInvalidEmail: jest.fn(() => 'Email inválido'),
    buildClinicNotFound: jest.fn(() => 'Clínica não encontrada'),
    buildClinicConfirmation: jest.fn(() => 'Confirme a clínica'),
    buildFAQMenu: jest.fn(() => 'Menu FAQ'),
    buildFAQResponse: jest.fn(() => 'Resposta FAQ')
};

const mockValidators = {
    sanitizeInput: jest.fn((input) => input),
    isExitCommand: jest.fn(() => false),
    isGS2Trigger: jest.fn(() => false),
    validateUserResponse: jest.fn(() => false),
    validateClinicName: jest.fn(() => true),
    validateName: jest.fn(() => true),
    validateCPF: jest.fn(() => true),
    validateEmail: jest.fn(() => true),
    sanitizeCPF: jest.fn((cpf) => cpf),
    formatCPF: jest.fn((cpf) => cpf),
    formatCNPJ: jest.fn((cnpj) => cnpj)
};

jest.unstable_mockModule('../src/lambdas/gs2api/wpphook/utils/validators.mjs', () => ({
    default: mockValidators
}));

// Import StateMachine after mocking dependencies
const { StateMachine, STATES } = await import('../src/lambdas/gs2api/wpphook/services/stateMachine.mjs');

describe('wpphook StateMachine', () => {
    let stateMachine;

    beforeEach(() => {
        jest.clearAllMocks();
        stateMachine = new StateMachine(mockDatabase, mockMessageBuilder);
        
        // Setup default mock responses
        mockValidators.sanitizeInput.mockImplementation((input) => input);
        mockValidators.isExitCommand.mockReturnValue(false);
        mockValidators.isGS2Trigger.mockReturnValue(false);
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('State Machine Initialization', () => {
        test('should initialize with correct dependencies', () => {
            expect(stateMachine.db).toBe(mockDatabase);
            expect(stateMachine.messageBuilder).toBe(mockMessageBuilder);
            expect(stateMachine.states).toBe(STATES);
            expect(stateMachine.sessionTimeout).toBe(24 * 60 * 60 * 1000);
        });
    });

    describe('Session Management', () => {
        test('should validate active session', async () => {
            const mockSession = {
                id: '123456789',
                updated_at: new Date()
            };

            mockDatabase.getUserSession.mockResolvedValue(mockSession);

            const result = await stateMachine.validateSession('123456789');

            expect(result).toEqual({
                valid: true,
                session: mockSession
            });
        });

        test('should detect expired session', async () => {
            const oldDate = new Date();
            oldDate.setHours(oldDate.getHours() - 25); // 25 hours ago

            const mockSession = {
                id: '123456789',
                updated_at: oldDate
            };

            mockDatabase.getUserSession.mockResolvedValue(mockSession);

            const result = await stateMachine.validateSession('123456789');

            expect(result).toEqual({
                valid: false,
                reason: 'expired'
            });
            expect(mockDatabase.deleteUserSession).toHaveBeenCalledWith('123456789');
        });

        test('should handle non-existent session', async () => {
            mockDatabase.getUserSession.mockResolvedValue(null);

            const result = await stateMachine.validateSession('123456789');

            expect(result).toEqual({
                valid: false,
                reason: 'no_session'
            });
        });

        test('should cleanup expired sessions', async () => {
            mockDatabase.cleanupOldSessions.mockResolvedValue(5);

            const result = await stateMachine.cleanupExpiredSessions();

            expect(mockDatabase.cleanupOldSessions).toHaveBeenCalledWith(24);
            expect(result).toBe(5);
        });

        test('should reset session', async () => {
            mockDatabase.deleteUserSession.mockResolvedValue(true);

            const result = await stateMachine.resetSession('123456789', 'test_reason');

            expect(mockDatabase.deleteUserSession).toHaveBeenCalledWith('123456789');
            expect(result).toBe(true);
        });

        test('should get session stats', async () => {
            const mockStats = [
                { current_state: 'GREETING', count: 10, avg_duration_minutes: 5 }
            ];

            mockDatabase.executeQuery.mockResolvedValue(mockStats);

            const result = await stateMachine.getSessionStats();

            expect(result).toEqual(mockStats);
        });

        test('should get user journey', async () => {
            const mockSession = { current_state: 'GREETING' };
            const mockInteractions = [
                { message_type: 'INCOMING', message_content: 'GS2' }
            ];

            mockDatabase.getInteractionHistory.mockResolvedValue(mockInteractions);
            mockDatabase.getUserSession.mockResolvedValue(mockSession);

            const result = await stateMachine.getUserJourney('123456789');

            expect(result).toEqual({
                session: mockSession,
                interactions: mockInteractions.reverse(),
                totalInteractions: 1
            });
        });
    });

    describe('Message Processing Flow', () => {
        test('should process message with valid session', async () => {
            const mockSession = {
                id: '123456789',
                current_state: 'GREETING',
                session_data: {},
                updated_at: new Date()
            };

            mockDatabase.getUserSession.mockResolvedValue(mockSession);
            mockValidators.validateUserResponse.mockReturnValue(true);

            const response = await stateMachine.processMessage('123456789', '✅ Claro', '+5511999999999');

            expect(mockDatabase.logInteraction).toHaveBeenCalledWith('123456789', 'INCOMING', '✅ Claro');
            expect(mockDatabase.logInteraction).toHaveBeenCalledWith('123456789', 'OUTGOING', expect.any(String));
            expect(response).toHaveProperty('message');
            expect(response).toHaveProperty('nextState');
        });

        test('should create new session for expired session', async () => {
            const oldDate = new Date();
            oldDate.setHours(oldDate.getHours() - 25);

            const expiredSession = {
                id: '123456789',
                updated_at: oldDate
            };

            const newSession = {
                id: '123456789',
                current_state: 'INITIAL',
                session_data: {}
            };

            mockDatabase.getUserSession.mockResolvedValue(expiredSession);
            mockDatabase.createUserSession.mockResolvedValue(newSession);

            const response = await stateMachine.processMessage('123456789', 'GS2', '+5511999999999');

            expect(mockDatabase.deleteUserSession).toHaveBeenCalledWith('123456789');
            expect(mockDatabase.createUserSession).toHaveBeenCalledWith('123456789', '+5511999999999');
        });

        test('should handle exit command', async () => {
            const mockSession = {
                id: '123456789',
                current_state: 'GREETING',
                session_data: {},
                updated_at: new Date()
            };

            mockDatabase.getUserSession.mockResolvedValue(mockSession);
            mockValidators.isExitCommand.mockReturnValue(true);

            const response = await stateMachine.processMessage('123456789', 'sair', '+5511999999999');

            expect(mockDatabase.updateUserSession).toHaveBeenCalledWith(
                '123456789',
                'END',
                mockSession.session_data
            );
            expect(response.nextState).toBe('END');
        });
    });

    describe('State Handlers', () => {
        test('should handle initial state with GS2 trigger', async () => {
            mockValidators.isGS2Trigger.mockReturnValue(true);

            const response = await stateMachine.handleInitialState('123456789', 'GS2', {});

            expect(mockDatabase.updateUserSession).toHaveBeenCalledWith(
                '123456789',
                'GREETING',
                expect.objectContaining({
                    startTime: expect.any(String)
                })
            );
            expect(response.nextState).toBe('GREETING');
            expect(mockMessageBuilder.buildGreeting).toHaveBeenCalled();
        });

        test('should handle greeting state acceptance', async () => {
            mockValidators.validateUserResponse.mockReturnValue(true);

            const response = await stateMachine.handleGreetingState('123456789', '✅ Claro', {});

            expect(mockDatabase.updateUserSession).toHaveBeenCalledWith(
                '123456789',
                'CLINIC_REQUEST',
                {}
            );
            expect(response.nextState).toBe('CLINIC_REQUEST');
            expect(mockMessageBuilder.buildClinicRequest).toHaveBeenCalled();
        });

        test('should handle clinic request with valid clinic', async () => {
            const mockClinic = {
                id: 1,
                name: 'Hospital Teste',
                razao_social: 'Hospital Teste Ltda',
                cnpj: '12345678000199'
            };

            mockValidators.validateClinicName.mockReturnValue(true);
            mockDatabase.findClinic.mockResolvedValue([mockClinic]);

            const response = await stateMachine.handleClinicRequestState('123456789', 'Hospital Teste', {});

            expect(mockDatabase.findClinic).toHaveBeenCalledWith('Hospital Teste');
            expect(mockDatabase.updateUserSession).toHaveBeenCalledWith(
                '123456789',
                'CLINIC_CONFIRMATION',
                expect.objectContaining({
                    clinicData: expect.objectContaining({
                        id: 1,
                        name: 'Hospital Teste'
                    })
                })
            );
            expect(response.nextState).toBe('CLINIC_CONFIRMATION');
        });

        test('should handle clinic not found', async () => {
            mockValidators.validateClinicName.mockReturnValue(true);
            mockDatabase.findClinic.mockResolvedValue([]);

            const response = await stateMachine.handleClinicRequestState('123456789', 'Clinica Inexistente', {});

            expect(response.nextState).toBe('CLINIC_REQUEST');
            expect(mockMessageBuilder.buildClinicNotFound).toHaveBeenCalled();
        });

        test('should handle name request with valid name', async () => {
            mockValidators.validateName.mockReturnValue(true);

            const response = await stateMachine.handleNameRequestState('123456789', 'João da Silva', { userData: {} });

            expect(mockDatabase.updateUserSession).toHaveBeenCalledWith(
                '123456789',
                'CPF_REQUEST',
                expect.objectContaining({
                    userData: expect.objectContaining({
                        fullName: 'João da Silva'
                    })
                })
            );
            expect(response.nextState).toBe('CPF_REQUEST');
        });

        test('should handle invalid name', async () => {
            mockValidators.validateName.mockReturnValue(false);

            const response = await stateMachine.handleNameRequestState('123456789', 'J', {});

            expect(response.nextState).toBe('NAME_REQUEST');
            expect(mockMessageBuilder.buildInvalidName).toHaveBeenCalled();
        });

        test('should handle CPF request with valid CPF', async () => {
            mockValidators.sanitizeCPF.mockReturnValue('12345678901');
            mockValidators.validateCPF.mockReturnValue(true);

            const sessionData = { userData: { fullName: 'João da Silva' } };
            const response = await stateMachine.handleCPFRequestState('123456789', '123.456.789-01', sessionData);

            expect(mockDatabase.updateUserSession).toHaveBeenCalledWith(
                '123456789',
                'EMAIL_REQUEST',
                expect.objectContaining({
                    userData: expect.objectContaining({
                        fullName: 'João da Silva',
                        cpf: '12345678901'
                    })
                })
            );
            expect(response.nextState).toBe('EMAIL_REQUEST');
        });

        test('should handle invalid CPF', async () => {
            mockValidators.sanitizeCPF.mockReturnValue('123');
            mockValidators.validateCPF.mockReturnValue(false);

            const response = await stateMachine.handleCPFRequestState('123456789', '123', {});

            expect(response.nextState).toBe('CPF_REQUEST');
            expect(mockMessageBuilder.buildInvalidCPF).toHaveBeenCalled();
        });

        test('should handle email request with valid email', async () => {
            mockValidators.validateEmail.mockReturnValue(true);

            const sessionData = { 
                userData: { 
                    fullName: 'João da Silva',
                    cpf: '12345678901'
                }
            };

            const response = await stateMachine.handleEmailRequestState('123456789', '<EMAIL>', sessionData);

            expect(mockDatabase.updateUserSession).toHaveBeenCalledWith(
                '123456789',
                'DATA_CONFIRMATION',
                expect.objectContaining({
                    userData: expect.objectContaining({
                        fullName: 'João da Silva',
                        cpf: '12345678901',
                        email: '<EMAIL>'
                    })
                })
            );
            expect(response.nextState).toBe('DATA_CONFIRMATION');
        });

        test('should handle terms acceptance', async () => {
            mockValidators.validateUserResponse.mockReturnValue(true);

            const sessionData = {
                clinicData: { id: 1 },
                userData: {
                    fullName: 'João da Silva',
                    cpf: '12345678901',
                    email: '<EMAIL>'
                }
            };

            const response = await stateMachine.handleTermsPresentationState('123456789', '✅ Sim', sessionData);

            expect(mockDatabase.saveUserData).toHaveBeenCalledWith(
                '123456789',
                1,
                expect.objectContaining({
                    fullName: 'João da Silva',
                    cpf: '12345678901',
                    email: '<EMAIL>',
                    termsAccepted: true
                })
            );
            expect(response.nextState).toBe('COMPLETION');
        });

        test('should handle FAQ navigation', async () => {
            // First call for "want to know more"
            mockValidators.validateUserResponse
                .mockReturnValueOnce(false) // Not accepting terms
                .mockReturnValueOnce(true); // Wants to know more

            const response = await stateMachine.handleTermsPresentationState('123456789', 'ℹ️ Quero saber mais', {});

            expect(mockDatabase.updateUserSession).toHaveBeenCalledWith(
                '123456789',
                'FAQ_MENU',
                {}
            );
            expect(response.nextState).toBe('FAQ_MENU');
        });

        test('should handle FAQ menu selection', async () => {
            mockValidators.validateUserResponse.mockReturnValue(true);

            const response = await stateMachine.handleFAQMenuState('123456789', '❓Como funciona', {});

            expect(mockDatabase.updateUserSession).toHaveBeenCalledWith(
                '123456789',
                'FAQ_RESPONSE',
                expect.objectContaining({
                    currentFAQ: 'como_funciona'
                })
            );
            expect(response.nextState).toBe('FAQ_RESPONSE');
            expect(mockMessageBuilder.buildFAQResponse).toHaveBeenCalledWith('como_funciona');
        });
    });

    describe('State Transition Validation', () => {
        test('should validate correct state transitions', () => {
            expect(stateMachine.isValidTransition('INITIAL', 'GREETING')).toBe(true);
            expect(stateMachine.isValidTransition('GREETING', 'CLINIC_REQUEST')).toBe(true);
            expect(stateMachine.isValidTransition('COMPLETION', 'END')).toBe(true);
        });

        test('should reject invalid state transitions', () => {
            expect(stateMachine.isValidTransition('INITIAL', 'COMPLETION')).toBe(false);
            expect(stateMachine.isValidTransition('GREETING', 'EMAIL_REQUEST')).toBe(false);
            expect(stateMachine.isValidTransition('END', 'GREETING')).toBe(false);
        });

        test('should handle non-existent states', () => {
            expect(stateMachine.isValidTransition('INVALID_STATE', 'GREETING')).toBe(false);
            expect(stateMachine.isValidTransition('GREETING', 'INVALID_STATE')).toBe(false);
        });
    });

    describe('Error Handling', () => {
        test('should handle database errors gracefully', async () => {
            mockDatabase.getUserSession.mockRejectedValue(new Error('Database connection failed'));

            const response = await stateMachine.processMessage('123456789', 'GS2', '+5511999999999');

            expect(response).toMatchObject({
                message: 'Desculpe, ocorreu um erro interno. Tente novamente em alguns instantes.',
                nextState: 'INITIAL'
            });
        });

        test('should handle session validation errors', async () => {
            mockDatabase.getUserSession.mockRejectedValue(new Error('Query failed'));

            const result = await stateMachine.validateSession('123456789');

            expect(result).toEqual({
                valid: false,
                reason: 'error'
            });
        });

        test('should handle cleanup errors', async () => {
            mockDatabase.cleanupOldSessions.mockRejectedValue(new Error('Cleanup failed'));

            await expect(stateMachine.cleanupExpiredSessions()).rejects.toThrow('Cleanup failed');
        });

        test('should handle stats retrieval errors', async () => {
            mockDatabase.executeQuery.mockRejectedValue(new Error('Stats query failed'));

            const result = await stateMachine.getSessionStats();

            expect(result).toEqual([]);
        });

        test('should handle user journey errors', async () => {
            mockDatabase.getInteractionHistory.mockRejectedValue(new Error('History query failed'));

            const result = await stateMachine.getUserJourney('123456789');

            expect(result).toBe(null);
        });

        test('should handle reset session errors', async () => {
            mockDatabase.deleteUserSession.mockRejectedValue(new Error('Delete failed'));

            const result = await stateMachine.resetSession('123456789');

            expect(result).toBe(false);
        });
    });
});