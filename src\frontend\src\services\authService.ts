// =====================================================
// AUTH SERVICE - SERVIÇO DE AUTENTICAÇÃO
// Gerenciamento de autenticação e autorização
// =====================================================

import { modernApi } from './modernApi'
import type { LoginCredentials, AuthResponse, User } from '@/types'

class AuthService {
  /**
   * Fazer login
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await apiService.lambda<AuthResponse['data']>('brwUsuario', {
        method: 'login',
        ...credentials
      })

      return {
        success: response.success,
        message: response.message,
        data: response.data
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Erro no login',
        data: undefined
      }
    }
  }

  /**
   * Fazer logout
   */
  async logout(): Promise<void> {
    try {
      await apiService.lambda('brwUsuario', {
        method: 'logout'
      })
    } catch (error) {
      console.error('Erro no logout:', error)
    } finally {
      this.clearToken()
    }
  }

  /**
   * Renovar token
   */
  async refreshToken(): Promise<AuthResponse> {
    try {
      const response = await apiService.lambda<AuthResponse['data']>('brwUsuario', {
        method: 'refreshToken'
      })

      return {
        success: response.success,
        message: response.message,
        data: response.data
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Erro ao renovar token',
        data: undefined
      }
    }
  }

  /**
   * Verificar token
   */
  async verifyToken(): Promise<{ valid: boolean; user?: User }> {
    try {
      const response = await apiService.lambda<{ user: User }>('brwUsuario', {
        method: 'verifyToken'
      })

      return {
        valid: response.success,
        user: response.data?.user
      }
    } catch (error) {
      return { valid: false }
    }
  }

  /**
   * Obter perfil do usuário
   */
  async getProfile(): Promise<User | null> {
    try {
      const response = await apiService.lambda<User>('brwUsuario', {
        method: 'getProfile'
      })

      return response.success ? response.data : null
    } catch (error) {
      console.error('Erro ao obter perfil:', error)
      return null
    }
  }

  /**
   * Atualizar perfil
   */
  async updateProfile(data: Partial<User>): Promise<boolean> {
    try {
      const response = await apiService.lambda('brwUsuario', {
        method: 'updateProfile',
        ...data
      })

      return response.success
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error)
      return false
    }
  }

  /**
   * Alterar senha
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<boolean> {
    try {
      const response = await apiService.lambda('brwUsuario', {
        method: 'changePassword',
        currentPassword,
        newPassword
      })

      return response.success
    } catch (error) {
      console.error('Erro ao alterar senha:', error)
      return false
    }
  }

  /**
   * Solicitar recuperação de senha
   */
  async requestPasswordReset(email: string): Promise<boolean> {
    try {
      const response = await apiService.lambda('brwUsuario', {
        method: 'requestPasswordReset',
        email
      })

      return response.success
    } catch (error) {
      console.error('Erro ao solicitar recuperação:', error)
      return false
    }
  }

  /**
   * Resetar senha
   */
  async resetPassword(token: string, newPassword: string): Promise<boolean> {
    try {
      const response = await apiService.lambda('brwUsuario', {
        method: 'resetPassword',
        token,
        newPassword
      })

      return response.success
    } catch (error) {
      console.error('Erro ao resetar senha:', error)
      return false
    }
  }

  /**
   * Definir token
   */
  setToken(token: string): void {
    apiService.setToken(token)
  }

  /**
   * Limpar token
   */
  clearToken(): void {
    apiService.clearToken()
  }

  /**
   * Verificar se tem permissão
   */
  hasPermission(user: User | null, permission: string): boolean {
    if (!user || !user.permissions) return false
    return user.permissions[permission] === true
  }

  /**
   * Verificar se tem grupo
   */
  hasGroup(user: User | null, group: string): boolean {
    if (!user || !user.groups) return false
    return user.groups.some(g => g.name === group)
  }

  /**
   * Verificar se tem algum dos grupos
   */
  hasAnyGroup(user: User | null, groups: string[]): boolean {
    if (!user || !user.groups) return false
    return user.groups.some(g => groups.includes(g.name))
  }

  /**
   * Verificar se é master
   */
  isMaster(user: User | null): boolean {
    return this.hasGroup(user, 'Master')
  }

  /**
   * Verificar se é admin
   */
  isAdmin(user: User | null): boolean {
    return this.hasAnyGroup(user, ['Master', 'Admin'])
  }

  /**
   * Verificar se é profissional
   */
  isProfessional(user: User | null): boolean {
    return this.hasGroup(user, 'Profissional')
  }

  /**
   * Verificar se tem acesso financeiro
   */
  hasFinancialAccess(user: User | null): boolean {
    return this.hasAnyGroup(user, ['Master', 'Admin', 'Financeiro'])
  }
}

// Instância singleton
export const authService = new AuthService()
