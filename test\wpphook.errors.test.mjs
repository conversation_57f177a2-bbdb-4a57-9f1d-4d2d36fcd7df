import { describe, test, expect, beforeEach, jest } from '@jest/globals';

// Mock capfunctions
const mockBaseResponse = {
    ok: jest.fn((message, data) => ({ statusCode: 200, message, data })),
    error: jest.fn((message, data) => ({ statusCode: 500, message, data }))
};

jest.unstable_mockModule('capfunctions', () => ({
    baseResponse: mockBaseResponse
}));

describe('wpphook Error Scenarios and Recovery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('Handler Error Recovery', () => {
        test('should handle malformed webhook payload', async () => {
            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            const malformedEvent = {
                body: 'invalid json string'
            };

            const result = await handler(malformedEvent, {});

            expect(result.statusCode).toBe(500);
            expect(result.body).toContain('erro interno');
        });

        test('should handle missing required webhook fields', async () => {
            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            const incompleteEvent = {
                body: JSON.stringify({
                    message: 'GS2'
                    // missing 'from' field
                })
            };

            const result = await handler(incompleteEvent, {});

            expect(result.statusCode).toBe(500);
            expect(result.body).toContain('erro interno');
        });

        test('should handle component initialization failures', async () => {
            // Mock database to fail initialization
            const mockFailingDatabase = jest.fn(() => {
                throw new Error('Database initialization failed');
            });

            jest.doMock('../src/lambdas/gs2api/wpphook/database.mjs', () => ({
                default: mockFailingDatabase
            }));

            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            const event = {
                body: JSON.stringify({
                    message: 'GS2',
                    from: '+5511999999999'
                })
            };

            const result = await handler(event, {});

            expect(result.statusCode).toBe(500);
            expect(result.body).toContain('erro interno');

            jest.dontMock('../src/lambdas/gs2api/wpphook/database.mjs');
        });

        test('should handle context timeout warnings', async () => {
            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');
            
            const mockContext = {
                getRemainingTimeInMillis: jest.fn(() => 1000) // Low time remaining
            };

            const event = {
                httpMethod: 'GET',
                path: '/health'
            };

            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

            // Set timeout to trigger immediately for testing
            setTimeout(() => {
                // This should trigger the timeout warning
            }, 0);

            const result = await handler(event, mockContext);

            expect(result.statusCode).toBe(200);
            consoleSpy.mockRestore();
        });
    });

    describe('Database Error Recovery', () => {
        test('should handle database connection failures', async () => {
            // Mock mysql to fail connection
            const mockFailingConnection = {
                connect: jest.fn((callback) => {
                    callback(new Error('Connection refused'));
                }),
                query: jest.fn(),
                end: jest.fn()
            };

            jest.unstable_mockModule('mysql', () => ({
                createConnection: jest.fn(() => mockFailingConnection)
            }));

            const Database = (await import('../src/lambdas/gs2api/wpphook/database.mjs')).default;
            const db = new Database();

            await expect(db.initialize()).rejects.toThrow('Connection refused');
        });

        test('should retry failed queries', async () => {
            let attemptCount = 0;
            const mockConnection = {
                connect: jest.fn((callback) => callback(null)),
                query: jest.fn((query, params, callback) => {
                    attemptCount++;
                    if (attemptCount < 3) {
                        callback(new Error('Temporary failure'));
                    } else {
                        callback(null, [{ result: 'success' }]);
                    }
                }),
                end: jest.fn()
            };

            jest.unstable_mockModule('mysql', () => ({
                createConnection: jest.fn(() => mockConnection)
            }));

            const Database = (await import('../src/lambdas/gs2api/wpphook/database.mjs')).default;
            const db = new Database();
            await db.initialize();

            const result = await db.executeQuery('SELECT * FROM test');

            expect(result).toEqual([{ result: 'success' }]);
            expect(mockConnection.query).toHaveBeenCalledTimes(3);
        });

        test('should fail after max retries', async () => {
            const mockConnection = {
                connect: jest.fn((callback) => callback(null)),
                query: jest.fn((query, params, callback) => {
                    callback(new Error('Persistent failure'));
                }),
                end: jest.fn()
            };

            jest.unstable_mockModule('mysql', () => ({
                createConnection: jest.fn(() => mockConnection)
            }));

            const Database = (await import('../src/lambdas/gs2api/wpphook/database.mjs')).default;
            const db = new Database();
            await db.initialize();

            await expect(db.executeQuery('SELECT * FROM test')).rejects.toThrow(
                'Database query failed after 3 attempts'
            );
            expect(mockConnection.query).toHaveBeenCalledTimes(3);
        });

        test('should handle malformed session data gracefully', async () => {
            const mockConnection = {
                connect: jest.fn((callback) => callback(null)),
                query: jest.fn((query, params, callback) => {
                    callback(null, [{
                        id: '123',
                        session_data: 'invalid json{'
                    }]);
                }),
                end: jest.fn()
            };

            jest.unstable_mockModule('mysql', () => ({
                createConnection: jest.fn(() => mockConnection)
            }));

            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            const Database = (await import('../src/lambdas/gs2api/wpphook/database.mjs')).default;
            const db = new Database();
            await db.initialize();

            const result = await db.getUserSession('123');

            expect(result.session_data).toEqual({});
            expect(consoleSpy).toHaveBeenCalledWith(
                'Failed to parse session data:',
                expect.any(Error)
            );

            consoleSpy.mockRestore();
        });
    });

    describe('State Machine Error Recovery', () => {
        test('should handle invalid state transitions', async () => {
            const mockDatabase = {
                getUserSession: jest.fn(),
                logInteraction: jest.fn(),
                createUserSession: jest.fn(),
                updateUserSession: jest.fn()
            };

            const mockMessageBuilder = {
                buildGreeting: jest.fn(() => 'Hello'),
                buildErrorMessage: jest.fn(() => 'Error occurred')
            };

            jest.unstable_mockModule('../src/lambdas/gs2api/wpphook/utils/validators.mjs', () => ({
                default: {
                    sanitizeInput: jest.fn((input) => input),
                    isExitCommand: jest.fn(() => false),
                    isGS2Trigger: jest.fn(() => false)
                }
            }));

            const { StateMachine } = await import('../src/lambdas/gs2api/wpphook/services/stateMachine.mjs');
            const stateMachine = new StateMachine(mockDatabase, mockMessageBuilder);

            // Test invalid state transition validation
            expect(stateMachine.isValidTransition('INVALID_STATE', 'GREETING')).toBe(false);
            expect(stateMachine.isValidTransition('INITIAL', 'COMPLETION')).toBe(false);
        });

        test('should handle state machine processing errors', async () => {
            const mockDatabase = {
                getUserSession: jest.fn(() => Promise.reject(new Error('Database error'))),
                logInteraction: jest.fn(),
                createUserSession: jest.fn(),
                updateUserSession: jest.fn()
            };

            const mockMessageBuilder = {
                buildErrorMessage: jest.fn(() => 'Error message')
            };

            jest.unstable_mockModule('../src/lambdas/gs2api/wpphook/utils/validators.mjs', () => ({
                default: {
                    sanitizeInput: jest.fn((input) => input),
                    isExitCommand: jest.fn(() => false)
                }
            }));

            const { StateMachine } = await import('../src/lambdas/gs2api/wpphook/services/stateMachine.mjs');
            const stateMachine = new StateMachine(mockDatabase, mockMessageBuilder);

            const result = await stateMachine.processMessage('123', 'test', '+5511999999999');

            expect(result).toMatchObject({
                message: 'Desculpe, ocorreu um erro interno. Tente novamente em alguns instantes.',
                nextState: 'INITIAL'
            });
        });

        test('should handle session validation errors gracefully', async () => {
            const mockDatabase = {
                getUserSession: jest.fn(() => Promise.reject(new Error('Query failed')))
            };

            const mockMessageBuilder = {};

            const { StateMachine } = await import('../src/lambdas/gs2api/wpphook/services/stateMachine.mjs');
            const stateMachine = new StateMachine(mockDatabase, mockMessageBuilder);

            const result = await stateMachine.validateSession('123');

            expect(result).toEqual({
                valid: false,
                reason: 'error'
            });
        });

        test('should handle session cleanup errors', async () => {
            const mockDatabase = {
                cleanupOldSessions: jest.fn(() => Promise.reject(new Error('Cleanup failed')))
            };

            const mockMessageBuilder = {};

            const { StateMachine } = await import('../src/lambdas/gs2api/wpphook/services/stateMachine.mjs');
            const stateMachine = new StateMachine(mockDatabase, mockMessageBuilder);

            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            await expect(stateMachine.cleanupExpiredSessions()).rejects.toThrow('Cleanup failed');

            consoleSpy.mockRestore();
        });
    });

    describe('Input Validation and Sanitization', () => {
        test('should handle malicious input attempts', async () => {
            const Validators = (await import('../src/lambdas/gs2api/wpphook/utils/validators.mjs')).default;

            // Test script injection attempts
            const maliciousInputs = [
                '<script>alert("xss")</script>',
                '"><script>alert("xss")</script>',
                'javascript:alert("xss")',
                'onload=alert("xss")',
                '${alert("xss")}'
            ];

            maliciousInputs.forEach(input => {
                const sanitized = Validators.sanitizeInput(input);
                expect(sanitized).not.toContain('<script>');
                expect(sanitized).not.toContain('javascript:');
                expect(sanitized).not.toContain('onload=');
            });
        });

        test('should handle extremely long inputs', async () => {
            const Validators = (await import('../src/lambdas/gs2api/wpphook/utils/validators.mjs')).default;

            const longInput = 'A'.repeat(10000);
            const sanitized = Validators.sanitizeInput(longInput);

            expect(typeof sanitized).toBe('string');
            expect(sanitized.length).toBeLessThanOrEqual(longInput.length);
        });

        test('should handle special unicode characters', async () => {
            const Validators = (await import('../src/lambdas/gs2api/wpphook/utils/validators.mjs')).default;

            const unicodeInputs = [
                'João da Silva 👤',
                'Clínica São Paulo 🏥',
                '<EMAIL> 📧',
                'José María García'
            ];

            unicodeInputs.forEach(input => {
                const sanitized = Validators.sanitizeInput(input);
                expect(typeof sanitized).toBe('string');
                expect(sanitized.length).toBeGreaterThan(0);
            });
        });

        test('should handle null and undefined inputs gracefully', async () => {
            const Validators = (await import('../src/lambdas/gs2api/wpphook/utils/validators.mjs')).default;

            expect(Validators.sanitizeInput(null)).toBe('');
            expect(Validators.sanitizeInput(undefined)).toBe('');
            expect(Validators.validateName(null)).toBe(false);
            expect(Validators.validateCPF(undefined)).toBe(false);
            expect(Validators.validateEmail(null)).toBe(false);
        });
    });

    describe('Message Builder Error Handling', () => {
        test('should handle undefined or null data gracefully', async () => {
            const MessageBuilder = (await import('../src/lambdas/gs2api/wpphook/utils/messageBuilder.mjs')).default;
            const mb = new MessageBuilder();

            // Test with undefined data
            expect(() => mb.buildClinicConfirmation(undefined)).not.toThrow();
            expect(() => mb.buildDataConfirmation(undefined)).not.toThrow();
            expect(() => mb.buildDataConfirmationRepeat(null)).not.toThrow();

            // Ensure they return strings
            expect(typeof mb.buildClinicConfirmation(undefined)).toBe('string');
            expect(typeof mb.buildDataConfirmation(null)).toBe('string');
        });

        test('should handle missing properties in data objects', async () => {
            const MessageBuilder = (await import('../src/lambdas/gs2api/wpphook/utils/messageBuilder.mjs')).default;
            const mb = new MessageBuilder();

            // Test with empty objects
            const emptyClinic = {};
            const emptyUserData = {};

            expect(() => mb.buildClinicConfirmation(emptyClinic)).not.toThrow();
            expect(() => mb.buildDataConfirmation(emptyUserData)).not.toThrow();

            const clinicMessage = mb.buildClinicConfirmation(emptyClinic);
            const userMessage = mb.buildDataConfirmation(emptyUserData);

            expect(typeof clinicMessage).toBe('string');
            expect(typeof userMessage).toBe('string');
        });

        test('should handle FAQ topic edge cases', async () => {
            const MessageBuilder = (await import('../src/lambdas/gs2api/wpphook/utils/messageBuilder.mjs')).default;
            const mb = new MessageBuilder();

            // Test with invalid FAQ topics
            expect(mb.buildFAQResponse('nonexistent_topic')).toBe('Informação não encontrada.');
            expect(mb.buildFAQResponse('')).toBe('Informação não encontrada.');
            expect(mb.buildFAQResponse(null)).toBe('Informação não encontrada.');
            expect(mb.buildFAQResponse(undefined)).toBe('Informação não encontrada.');
        });
    });

    describe('Network and External Service Failures', () => {
        test('should handle MySQL service unavailable', async () => {
            // Mock mysql to simulate service unavailable
            jest.unstable_mockModule('mysql', () => {
                throw new Error('MySQL service unavailable');
            });

            await expect(async () => {
                const Database = (await import('../src/lambdas/gs2api/wpphook/database.mjs')).default;
                new Database();
            }).rejects.toThrow('MySQL service unavailable');
        });

        test('should handle timeout scenarios', async () => {
            const mockConnection = {
                connect: jest.fn((callback) => {
                    // Simulate connection timeout
                    setTimeout(() => {
                        callback(new Error('Connection timeout'));
                    }, 100);
                }),
                query: jest.fn(),
                end: jest.fn()
            };

            jest.unstable_mockModule('mysql', () => ({
                createConnection: jest.fn(() => mockConnection)
            }));

            const Database = (await import('../src/lambdas/gs2api/wpphook/database.mjs')).default;
            const db = new Database();

            await expect(db.initialize()).rejects.toThrow('Connection timeout');
        });
    });

    describe('Recovery Mechanisms', () => {
        test('should reset state on critical errors', async () => {
            const mockDatabase = {
                getUserSession: jest.fn(),
                createUserSession: jest.fn(),
                updateUserSession: jest.fn(),
                logInteraction: jest.fn(),
                deleteUserSession: jest.fn()
            };

            const mockMessageBuilder = {
                buildExitMessage: jest.fn(() => 'Session reset')
            };

            jest.unstable_mockModule('../src/lambdas/gs2api/wpphook/utils/validators.mjs', () => ({
                default: {
                    sanitizeInput: jest.fn((input) => input),
                    isExitCommand: jest.fn(() => true) // Simulate exit command
                }
            }));

            const { StateMachine } = await import('../src/lambdas/gs2api/wpphook/services/stateMachine.mjs');
            const stateMachine = new StateMachine(mockDatabase, mockMessageBuilder);

            const session = {
                id: '123',
                session_data: { some: 'data' }
            };

            mockDatabase.getUserSession.mockResolvedValue(session);

            const result = await stateMachine.processMessage('123', 'sair', '+5511999999999');

            expect(mockDatabase.updateUserSession).toHaveBeenCalledWith(
                '123',
                'END',
                session.session_data
            );
            expect(result.nextState).toBe('END');
        });

        test('should provide helpful error messages', async () => {
            const MessageBuilder = (await import('../src/lambdas/gs2api/wpphook/utils/messageBuilder.mjs')).default;
            const mb = new MessageBuilder();

            const errorMessage = mb.buildErrorMessage();
            expect(errorMessage).toContain('erro interno');
            expect(errorMessage).toContain('Tente novamente');

            const invalidResponse = mb.buildInvalidResponse();
            expect(invalidResponse).toContain('Não entendi');
            expect(invalidResponse).toContain('opções disponíveis');
        });
    });
});