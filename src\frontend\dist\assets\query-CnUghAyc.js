var ye=e=>{throw TypeError(e)};var Jt=(e,t,s)=>t.has(e)||ye("Cannot "+s);var r=(e,t,s)=>(Jt(e,t,"read from private field"),s?s.call(e):t.get(e)),c=(e,t,s)=>t.has(e)?ye("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s),u=(e,t,s,i)=>(Jt(e,t,"write to private field"),i?i.call(e,s):t.set(e,s),s),y=(e,t,s)=>(Jt(e,t,"access private method"),s);var Gt=(e,t,s,i)=>({set _(n){u(e,t,n,s)},get _(){return r(e,t,i)}});import{r as I}from"./vendor-CBH9K-97.js";var Ae={exports:{}},Wt={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Je=I,Ye=Symbol.for("react.element"),Xe=Symbol.for("react.fragment"),Ze=Object.prototype.hasOwnProperty,ts=Je.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,es={key:!0,ref:!0,__self:!0,__source:!0};function qe(e,t,s){var i,n={},a=null,o=null;s!==void 0&&(a=""+s),t.key!==void 0&&(a=""+t.key),t.ref!==void 0&&(o=t.ref);for(i in t)Ze.call(t,i)&&!es.hasOwnProperty(i)&&(n[i]=t[i]);if(e&&e.defaultProps)for(i in t=e.defaultProps,t)n[i]===void 0&&(n[i]=t[i]);return{$$typeof:Ye,type:e,key:a,ref:o,props:n,_owner:ts.current}}Wt.Fragment=Xe;Wt.jsx=qe;Wt.jsxs=qe;Ae.exports=Wt;var ss=Ae.exports,Ut=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Ct=typeof window>"u"||"Deno"in globalThis;function j(){}function is(e,t){return typeof e=="function"?e(t):e}function Yt(e){return typeof e=="number"&&e>=0&&e!==1/0}function Ue(e,t){return Math.max(e+(t||0)-Date.now(),0)}function ct(e,t){return typeof e=="function"?e(t):e}function H(e,t){return typeof e=="function"?e(t):e}function pe(e,t){const{type:s="all",exact:i,fetchStatus:n,predicate:a,queryKey:o,stale:h}=e;if(o){if(i){if(t.queryHash!==ce(o,t.options))return!1}else if(!kt(t.queryKey,o))return!1}if(s!=="all"){const d=t.isActive();if(s==="active"&&!d||s==="inactive"&&d)return!1}return!(typeof h=="boolean"&&t.isStale()!==h||n&&n!==t.state.fetchStatus||a&&!a(t))}function me(e,t){const{exact:s,status:i,predicate:n,mutationKey:a}=e;if(a){if(!t.options.mutationKey)return!1;if(s){if(Ot(t.options.mutationKey)!==Ot(a))return!1}else if(!kt(t.options.mutationKey,a))return!1}return!(i&&t.state.status!==i||n&&!n(t))}function ce(e,t){return((t==null?void 0:t.queryKeyHashFn)||Ot)(e)}function Ot(e){return JSON.stringify(e,(t,s)=>Xt(s)?Object.keys(s).sort().reduce((i,n)=>(i[n]=s[n],i),{}):s)}function kt(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(s=>kt(e[s],t[s])):!1}function je(e,t){if(e===t)return e;const s=ve(e)&&ve(t);if(s||Xt(e)&&Xt(t)){const i=s?e:Object.keys(e),n=i.length,a=s?t:Object.keys(t),o=a.length,h=s?[]:{},d=new Set(i);let g=0;for(let O=0;O<o;O++){const l=s?O:a[O];(!s&&d.has(l)||s)&&e[l]===void 0&&t[l]===void 0?(h[l]=void 0,g++):(h[l]=je(e[l],t[l]),h[l]===e[l]&&e[l]!==void 0&&g++)}return n===o&&g===n?e:h}return t}function $t(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}function ve(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Xt(e){if(!be(e))return!1;const t=e.constructor;if(t===void 0)return!0;const s=t.prototype;return!(!be(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function be(e){return Object.prototype.toString.call(e)==="[object Object]"}function rs(e){return new Promise(t=>{setTimeout(t,e)})}function Zt(e,t,s){return typeof s.structuralSharing=="function"?s.structuralSharing(e,t):s.structuralSharing!==!1?je(e,t):t}function ns(e,t,s=0){const i=[...e,t];return s&&i.length>s?i.slice(1):i}function as(e,t,s=0){const i=[t,...e];return s&&i.length>s?i.slice(0,-1):i}var le=Symbol();function ke(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===le?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}function _e(e,t){return typeof e=="function"?e(...t):!!e}var dt,tt,Rt,Se,us=(Se=class extends Ut{constructor(){super();c(this,dt);c(this,tt);c(this,Rt);u(this,Rt,t=>{if(!Ct&&window.addEventListener){const s=()=>t();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){r(this,tt)||this.setEventListener(r(this,Rt))}onUnsubscribe(){var t;this.hasListeners()||((t=r(this,tt))==null||t.call(this),u(this,tt,void 0))}setEventListener(t){var s;u(this,Rt,t),(s=r(this,tt))==null||s.call(this),u(this,tt,t(i=>{typeof i=="boolean"?this.setFocused(i):this.onFocus()}))}setFocused(t){r(this,dt)!==t&&(u(this,dt,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(s=>{s(t)})}isFocused(){var t;return typeof r(this,dt)=="boolean"?r(this,dt):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},dt=new WeakMap,tt=new WeakMap,Rt=new WeakMap,Se),de=new us,St,et,Pt,Pe,os=(Pe=class extends Ut{constructor(){super();c(this,St,!0);c(this,et);c(this,Pt);u(this,Pt,t=>{if(!Ct&&window.addEventListener){const s=()=>t(!0),i=()=>t(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",i,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",i)}}})}onSubscribe(){r(this,et)||this.setEventListener(r(this,Pt))}onUnsubscribe(){var t;this.hasListeners()||((t=r(this,et))==null||t.call(this),u(this,et,void 0))}setEventListener(t){var s;u(this,Pt,t),(s=r(this,et))==null||s.call(this),u(this,et,t(this.setOnline.bind(this)))}setOnline(t){r(this,St)!==t&&(u(this,St,t),this.listeners.forEach(i=>{i(t)}))}isOnline(){return r(this,St)}},St=new WeakMap,et=new WeakMap,Pt=new WeakMap,Pe),Vt=new os;function te(){let e,t;const s=new Promise((n,a)=>{e=n,t=a});s.status="pending",s.catch(()=>{});function i(n){Object.assign(s,n),delete s.resolve,delete s.reject}return s.resolve=n=>{i({status:"fulfilled",value:n}),e(n)},s.reject=n=>{i({status:"rejected",reason:n}),t(n)},s}function hs(e){return Math.min(1e3*2**e,3e4)}function Le(e){return(e??"online")==="online"?Vt.isOnline():!0}var Ke=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function He(e){let t=!1,s=0,i;const n=te(),a=()=>n.status!=="pending",o=m=>{var v;a()||(Q(new Ke(m)),(v=e.abort)==null||v.call(e))},h=()=>{t=!0},d=()=>{t=!1},g=()=>de.isFocused()&&(e.networkMode==="always"||Vt.isOnline())&&e.canRun(),O=()=>Le(e.networkMode)&&e.canRun(),l=m=>{a()||(i==null||i(),n.resolve(m))},Q=m=>{a()||(i==null||i(),n.reject(m))},b=()=>new Promise(m=>{var v;i=R=>{(a()||g())&&m(R)},(v=e.onPause)==null||v.call(e)}).then(()=>{var m;i=void 0,a()||(m=e.onContinue)==null||m.call(e)}),M=()=>{if(a())return;let m;const v=s===0?e.initialPromise:void 0;try{m=v??e.fn()}catch(R){m=Promise.reject(R)}Promise.resolve(m).then(l).catch(R=>{var k;if(a())return;const x=e.retry??(Ct?0:3),w=e.retryDelay??hs,f=typeof w=="function"?w(s,R):w,D=x===!0||typeof x=="number"&&s<x||typeof x=="function"&&x(s,R);if(t||!D){Q(R);return}s++,(k=e.onFail)==null||k.call(e,s,R),rs(f).then(()=>g()?void 0:b()).then(()=>{t?Q(R):M()})})};return{promise:n,status:()=>n.status,cancel:o,continue:()=>(i==null||i(),n),cancelRetry:h,continueRetry:d,canStart:O,start:()=>(O()?M():b().then(M),n)}}var cs=e=>setTimeout(e,0);function ls(){let e=[],t=0,s=h=>{h()},i=h=>{h()},n=cs;const a=h=>{t?e.push(h):n(()=>{s(h)})},o=()=>{const h=e;e=[],h.length&&n(()=>{i(()=>{h.forEach(d=>{s(d)})})})};return{batch:h=>{let d;t++;try{d=h()}finally{t--,t||o()}return d},batchCalls:h=>(...d)=>{a(()=>{h(...d)})},schedule:a,setNotifyFunction:h=>{s=h},setBatchNotifyFunction:h=>{i=h},setScheduler:h=>{n=h}}}var E=ls(),ft,Fe,Ne=(Fe=class{constructor(){c(this,ft)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Yt(this.gcTime)&&u(this,ft,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Ct?1/0:5*60*1e3))}clearGcTimeout(){r(this,ft)&&(clearTimeout(r(this,ft)),u(this,ft,void 0))}},ft=new WeakMap,Fe),Ft,Et,K,yt,T,_t,pt,N,V,Ee,ds=(Ee=class extends Ne{constructor(t){super();c(this,N);c(this,Ft);c(this,Et);c(this,K);c(this,yt);c(this,T);c(this,_t);c(this,pt);u(this,pt,!1),u(this,_t,t.defaultOptions),this.setOptions(t.options),this.observers=[],u(this,yt,t.client),u(this,K,r(this,yt).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,u(this,Ft,fs(this.options)),this.state=t.state??r(this,Ft),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=r(this,T))==null?void 0:t.promise}setOptions(t){this.options={...r(this,_t),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&r(this,K).remove(this)}setData(t,s){const i=Zt(this.state.data,t,this.options);return y(this,N,V).call(this,{data:i,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),i}setState(t,s){y(this,N,V).call(this,{type:"setState",state:t,setStateOptions:s})}cancel(t){var i,n;const s=(i=r(this,T))==null?void 0:i.promise;return(n=r(this,T))==null||n.cancel(t),s?s.then(j).catch(j):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(r(this,Ft))}isActive(){return this.observers.some(t=>H(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===le||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>ct(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!Ue(this.state.dataUpdatedAt,t)}onFocus(){var s;const t=this.observers.find(i=>i.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(s=r(this,T))==null||s.continue()}onOnline(){var s;const t=this.observers.find(i=>i.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(s=r(this,T))==null||s.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),r(this,K).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(s=>s!==t),this.observers.length||(r(this,T)&&(r(this,pt)?r(this,T).cancel({revert:!0}):r(this,T).cancelRetry()),this.scheduleGc()),r(this,K).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||y(this,N,V).call(this,{type:"invalidate"})}async fetch(t,s){var d,g,O,l,Q,b,M,m,v,R,x,w;if(this.state.fetchStatus!=="idle"&&((d=r(this,T))==null?void 0:d.status())!=="rejected"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(r(this,T))return r(this,T).continueRetry(),r(this,T).promise}if(t&&this.setOptions(t),!this.options.queryFn){const f=this.observers.find(D=>D.options.queryFn);f&&this.setOptions(f.options)}const i=new AbortController,n=f=>{Object.defineProperty(f,"signal",{enumerable:!0,get:()=>(u(this,pt,!0),i.signal)})},a=()=>{const f=ke(this.options,s),k=(()=>{const X={client:r(this,yt),queryKey:this.queryKey,meta:this.meta};return n(X),X})();return u(this,pt,!1),this.options.persister?this.options.persister(f,k,this):f(k)},h=(()=>{const f={fetchOptions:s,options:this.options,queryKey:this.queryKey,client:r(this,yt),state:this.state,fetchFn:a};return n(f),f})();(g=this.options.behavior)==null||g.onFetch(h,this),u(this,Et,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((O=h.fetchOptions)==null?void 0:O.meta))&&y(this,N,V).call(this,{type:"fetch",meta:(l=h.fetchOptions)==null?void 0:l.meta}),u(this,T,He({initialPromise:s==null?void 0:s.initialPromise,fn:h.fetchFn,abort:i.abort.bind(i),onFail:(f,D)=>{y(this,N,V).call(this,{type:"failed",failureCount:f,error:D})},onPause:()=>{y(this,N,V).call(this,{type:"pause"})},onContinue:()=>{y(this,N,V).call(this,{type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay,networkMode:h.options.networkMode,canRun:()=>!0}));try{const f=await r(this,T).start();if(f===void 0)throw new Error(`${this.queryHash} data is undefined`);return this.setData(f),(b=(Q=r(this,K).config).onSuccess)==null||b.call(Q,f,this),(m=(M=r(this,K).config).onSettled)==null||m.call(M,f,this.state.error,this),f}catch(f){if(f instanceof Ke){if(f.silent)return r(this,T).promise;if(f.revert)return this.setState({...r(this,Et),fetchStatus:"idle"}),this.state.data}throw y(this,N,V).call(this,{type:"error",error:f}),(R=(v=r(this,K).config).onError)==null||R.call(v,f,this),(w=(x=r(this,K).config).onSettled)==null||w.call(x,this.state.data,f,this),f}finally{this.scheduleGc()}}},Ft=new WeakMap,Et=new WeakMap,K=new WeakMap,yt=new WeakMap,T=new WeakMap,_t=new WeakMap,pt=new WeakMap,N=new WeakSet,V=function(t){const s=i=>{switch(t.type){case"failed":return{...i,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...i,fetchStatus:"paused"};case"continue":return{...i,fetchStatus:"fetching"};case"fetch":return{...i,...Ge(i.data,this.options),fetchMeta:t.meta??null};case"success":const n={...i,data:t.data,dataUpdateCount:i.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};return u(this,Et,t.manual?n:void 0),n;case"error":const a=t.error;return{...i,error:a,errorUpdateCount:i.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:i.fetchFailureCount+1,fetchFailureReason:a,fetchStatus:"idle",status:"error"};case"invalidate":return{...i,isInvalidated:!0};case"setState":return{...i,...t.state}}};this.state=s(this.state),E.batch(()=>{this.observers.forEach(i=>{i.onQueryUpdate()}),r(this,K).notify({query:this,type:"updated",action:t})})},Ee);function Ge(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Le(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function fs(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,s=t!==void 0,i=s?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var B,Qe,ys=(Qe=class extends Ut{constructor(t={}){super();c(this,B);this.config=t,u(this,B,new Map)}build(t,s,i){const n=s.queryKey,a=s.queryHash??ce(n,s);let o=this.get(a);return o||(o=new ds({client:t,queryKey:n,queryHash:a,options:t.defaultQueryOptions(s),state:i,defaultOptions:t.getQueryDefaults(n)}),this.add(o)),o}add(t){r(this,B).has(t.queryHash)||(r(this,B).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const s=r(this,B).get(t.queryHash);s&&(t.destroy(),s===t&&r(this,B).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){E.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return r(this,B).get(t)}getAll(){return[...r(this,B).values()]}find(t){const s={exact:!0,...t};return this.getAll().find(i=>pe(s,i))}findAll(t={}){const s=this.getAll();return Object.keys(t).length>0?s.filter(i=>pe(t,i)):s}notify(t){E.batch(()=>{this.listeners.forEach(s=>{s(t)})})}onFocus(){E.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){E.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},B=new WeakMap,Qe),z,q,mt,$,Z,Me,ps=(Me=class extends Ne{constructor(t){super();c(this,$);c(this,z);c(this,q);c(this,mt);this.mutationId=t.mutationId,u(this,q,t.mutationCache),u(this,z,[]),this.state=t.state||Be(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){r(this,z).includes(t)||(r(this,z).push(t),this.clearGcTimeout(),r(this,q).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){u(this,z,r(this,z).filter(s=>s!==t)),this.scheduleGc(),r(this,q).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){r(this,z).length||(this.state.status==="pending"?this.scheduleGc():r(this,q).remove(this))}continue(){var t;return((t=r(this,mt))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var a,o,h,d,g,O,l,Q,b,M,m,v,R,x,w,f,D,k,X,A;const s=()=>{y(this,$,Z).call(this,{type:"continue"})};u(this,mt,He({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(P,F)=>{y(this,$,Z).call(this,{type:"failed",failureCount:P,error:F})},onPause:()=>{y(this,$,Z).call(this,{type:"pause"})},onContinue:s,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>r(this,q).canRun(this)}));const i=this.state.status==="pending",n=!r(this,mt).canStart();try{if(i)s();else{y(this,$,Z).call(this,{type:"pending",variables:t,isPaused:n}),await((o=(a=r(this,q).config).onMutate)==null?void 0:o.call(a,t,this));const F=await((d=(h=this.options).onMutate)==null?void 0:d.call(h,t));F!==this.state.context&&y(this,$,Z).call(this,{type:"pending",context:F,variables:t,isPaused:n})}const P=await r(this,mt).start();return await((O=(g=r(this,q).config).onSuccess)==null?void 0:O.call(g,P,t,this.state.context,this)),await((Q=(l=this.options).onSuccess)==null?void 0:Q.call(l,P,t,this.state.context)),await((M=(b=r(this,q).config).onSettled)==null?void 0:M.call(b,P,null,this.state.variables,this.state.context,this)),await((v=(m=this.options).onSettled)==null?void 0:v.call(m,P,null,t,this.state.context)),y(this,$,Z).call(this,{type:"success",data:P}),P}catch(P){try{throw await((x=(R=r(this,q).config).onError)==null?void 0:x.call(R,P,t,this.state.context,this)),await((f=(w=this.options).onError)==null?void 0:f.call(w,P,t,this.state.context)),await((k=(D=r(this,q).config).onSettled)==null?void 0:k.call(D,void 0,P,this.state.variables,this.state.context,this)),await((A=(X=this.options).onSettled)==null?void 0:A.call(X,void 0,P,t,this.state.context)),P}finally{y(this,$,Z).call(this,{type:"error",error:P})}}finally{r(this,q).runNext(this)}}},z=new WeakMap,q=new WeakMap,mt=new WeakMap,$=new WeakSet,Z=function(t){const s=i=>{switch(t.type){case"failed":return{...i,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...i,isPaused:!0};case"continue":return{...i,isPaused:!1};case"pending":return{...i,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...i,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...i,data:void 0,error:t.error,failureCount:i.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=s(this.state),E.batch(()=>{r(this,z).forEach(i=>{i.onMutationUpdate(t)}),r(this,q).notify({mutation:this,type:"updated",action:t})})},Me);function Be(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var W,G,Lt,xe,ms=(xe=class extends Ut{constructor(t={}){super();c(this,W);c(this,G);c(this,Lt);this.config=t,u(this,W,new Set),u(this,G,new Map),u(this,Lt,0)}build(t,s,i){const n=new ps({mutationCache:this,mutationId:++Gt(this,Lt)._,options:t.defaultMutationOptions(s),state:i});return this.add(n),n}add(t){r(this,W).add(t);const s=Bt(t);if(typeof s=="string"){const i=r(this,G).get(s);i?i.push(t):r(this,G).set(s,[t])}this.notify({type:"added",mutation:t})}remove(t){if(r(this,W).delete(t)){const s=Bt(t);if(typeof s=="string"){const i=r(this,G).get(s);if(i)if(i.length>1){const n=i.indexOf(t);n!==-1&&i.splice(n,1)}else i[0]===t&&r(this,G).delete(s)}}this.notify({type:"removed",mutation:t})}canRun(t){const s=Bt(t);if(typeof s=="string"){const i=r(this,G).get(s),n=i==null?void 0:i.find(a=>a.state.status==="pending");return!n||n===t}else return!0}runNext(t){var i;const s=Bt(t);if(typeof s=="string"){const n=(i=r(this,G).get(s))==null?void 0:i.find(a=>a!==t&&a.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}else return Promise.resolve()}clear(){E.batch(()=>{r(this,W).forEach(t=>{this.notify({type:"removed",mutation:t})}),r(this,W).clear(),r(this,G).clear()})}getAll(){return Array.from(r(this,W))}find(t){const s={exact:!0,...t};return this.getAll().find(i=>me(s,i))}findAll(t={}){return this.getAll().filter(s=>me(t,s))}notify(t){E.batch(()=>{this.listeners.forEach(s=>{s(t)})})}resumePausedMutations(){const t=this.getAll().filter(s=>s.state.isPaused);return E.batch(()=>Promise.all(t.map(s=>s.continue().catch(j))))}},W=new WeakMap,G=new WeakMap,Lt=new WeakMap,xe);function Bt(e){var t;return(t=e.options.scope)==null?void 0:t.id}function ge(e){return{onFetch:(t,s)=>{var O,l,Q,b,M;const i=t.options,n=(Q=(l=(O=t.fetchOptions)==null?void 0:O.meta)==null?void 0:l.fetchMore)==null?void 0:Q.direction,a=((b=t.state.data)==null?void 0:b.pages)||[],o=((M=t.state.data)==null?void 0:M.pageParams)||[];let h={pages:[],pageParams:[]},d=0;const g=async()=>{let m=!1;const v=w=>{Object.defineProperty(w,"signal",{enumerable:!0,get:()=>(t.signal.aborted?m=!0:t.signal.addEventListener("abort",()=>{m=!0}),t.signal)})},R=ke(t.options,t.fetchOptions),x=async(w,f,D)=>{if(m)return Promise.reject();if(f==null&&w.pages.length)return Promise.resolve(w);const X=(()=>{const lt={client:t.client,queryKey:t.queryKey,pageParam:f,direction:D?"backward":"forward",meta:t.options.meta};return v(lt),lt})(),A=await R(X),{maxPages:P}=t.options,F=D?as:ns;return{pages:F(w.pages,A,P),pageParams:F(w.pageParams,f,P)}};if(n&&a.length){const w=n==="backward",f=w?vs:Ce,D={pages:a,pageParams:o},k=f(i,D);h=await x(D,k,w)}else{const w=e??a.length;do{const f=d===0?o[0]??i.initialPageParam:Ce(i,h);if(d>0&&f==null)break;h=await x(h,f),d++}while(d<w)}return h};t.options.persister?t.fetchFn=()=>{var m,v;return(v=(m=t.options).persister)==null?void 0:v.call(m,g,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s)}:t.fetchFn=g}}}function Ce(e,{pages:t,pageParams:s}){const i=t.length-1;return t.length>0?e.getNextPageParam(t[i],t,s[i],s):void 0}function vs(e,{pages:t,pageParams:s}){var i;return t.length>0?(i=e.getPreviousPageParam)==null?void 0:i.call(e,t[0],t,s[0],s):void 0}var S,st,it,Qt,Mt,rt,xt,Dt,De,qs=(De=class{constructor(e={}){c(this,S);c(this,st);c(this,it);c(this,Qt);c(this,Mt);c(this,rt);c(this,xt);c(this,Dt);u(this,S,e.queryCache||new ys),u(this,st,e.mutationCache||new ms),u(this,it,e.defaultOptions||{}),u(this,Qt,new Map),u(this,Mt,new Map),u(this,rt,0)}mount(){Gt(this,rt)._++,r(this,rt)===1&&(u(this,xt,de.subscribe(async e=>{e&&(await this.resumePausedMutations(),r(this,S).onFocus())})),u(this,Dt,Vt.subscribe(async e=>{e&&(await this.resumePausedMutations(),r(this,S).onOnline())})))}unmount(){var e,t;Gt(this,rt)._--,r(this,rt)===0&&((e=r(this,xt))==null||e.call(this),u(this,xt,void 0),(t=r(this,Dt))==null||t.call(this),u(this,Dt,void 0))}isFetching(e){return r(this,S).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return r(this,st).findAll({...e,status:"pending"}).length}getQueryData(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=r(this,S).get(t.queryHash))==null?void 0:s.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),s=r(this,S).build(this,t),i=s.state.data;return i===void 0?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime(ct(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(i))}getQueriesData(e){return r(this,S).findAll(e).map(({queryKey:t,state:s})=>{const i=s.data;return[t,i]})}setQueryData(e,t,s){const i=this.defaultQueryOptions({queryKey:e}),n=r(this,S).get(i.queryHash),a=n==null?void 0:n.state.data,o=is(t,a);if(o!==void 0)return r(this,S).build(this,i).setData(o,{...s,manual:!0})}setQueriesData(e,t,s){return E.batch(()=>r(this,S).findAll(e).map(({queryKey:i})=>[i,this.setQueryData(i,t,s)]))}getQueryState(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=r(this,S).get(t.queryHash))==null?void 0:s.state}removeQueries(e){const t=r(this,S);E.batch(()=>{t.findAll(e).forEach(s=>{t.remove(s)})})}resetQueries(e,t){const s=r(this,S);return E.batch(()=>(s.findAll(e).forEach(i=>{i.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const s={revert:!0,...t},i=E.batch(()=>r(this,S).findAll(e).map(n=>n.cancel(s)));return Promise.all(i).then(j).catch(j)}invalidateQueries(e,t={}){return E.batch(()=>(r(this,S).findAll(e).forEach(s=>{s.invalidate()}),(e==null?void 0:e.refetchType)==="none"?Promise.resolve():this.refetchQueries({...e,type:(e==null?void 0:e.refetchType)??(e==null?void 0:e.type)??"active"},t)))}refetchQueries(e,t={}){const s={...t,cancelRefetch:t.cancelRefetch??!0},i=E.batch(()=>r(this,S).findAll(e).filter(n=>!n.isDisabled()&&!n.isStatic()).map(n=>{let a=n.fetch(void 0,s);return s.throwOnError||(a=a.catch(j)),n.state.fetchStatus==="paused"?Promise.resolve():a}));return Promise.all(i).then(j)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const s=r(this,S).build(this,t);return s.isStaleByTime(ct(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(j).catch(j)}fetchInfiniteQuery(e){return e.behavior=ge(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(j).catch(j)}ensureInfiniteQueryData(e){return e.behavior=ge(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Vt.isOnline()?r(this,st).resumePausedMutations():Promise.resolve()}getQueryCache(){return r(this,S)}getMutationCache(){return r(this,st)}getDefaultOptions(){return r(this,it)}setDefaultOptions(e){u(this,it,e)}setQueryDefaults(e,t){r(this,Qt).set(Ot(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...r(this,Qt).values()],s={};return t.forEach(i=>{kt(e,i.queryKey)&&Object.assign(s,i.defaultOptions)}),s}setMutationDefaults(e,t){r(this,Mt).set(Ot(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...r(this,Mt).values()],s={};return t.forEach(i=>{kt(e,i.mutationKey)&&Object.assign(s,i.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;const t={...r(this,it).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=ce(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===le&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...r(this,it).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){r(this,S).clear(),r(this,st).clear()}},S=new WeakMap,st=new WeakMap,it=new WeakMap,Qt=new WeakMap,Mt=new WeakMap,rt=new WeakMap,xt=new WeakMap,Dt=new WeakMap,De),_,p,Kt,U,vt,Tt,nt,at,Ht,It,At,bt,gt,ut,qt,C,jt,ee,se,ie,re,ne,ae,ue,ze,Te,bs=(Te=class extends Ut{constructor(t,s){super();c(this,C);c(this,_);c(this,p);c(this,Kt);c(this,U);c(this,vt);c(this,Tt);c(this,nt);c(this,at);c(this,Ht);c(this,It);c(this,At);c(this,bt);c(this,gt);c(this,ut);c(this,qt,new Set);this.options=s,u(this,_,t),u(this,at,null),u(this,nt,te()),this.options.experimental_prefetchInRender||r(this,nt).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(r(this,p).addObserver(this),Oe(r(this,p),this.options)?y(this,C,jt).call(this):this.updateResult(),y(this,C,re).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return oe(r(this,p),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return oe(r(this,p),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,y(this,C,ne).call(this),y(this,C,ae).call(this),r(this,p).removeObserver(this)}setOptions(t){const s=this.options,i=r(this,p);if(this.options=r(this,_).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof H(this.options.enabled,r(this,p))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");y(this,C,ue).call(this),r(this,p).setOptions(this.options),s._defaulted&&!$t(this.options,s)&&r(this,_).getQueryCache().notify({type:"observerOptionsUpdated",query:r(this,p),observer:this});const n=this.hasListeners();n&&we(r(this,p),i,this.options,s)&&y(this,C,jt).call(this),this.updateResult(),n&&(r(this,p)!==i||H(this.options.enabled,r(this,p))!==H(s.enabled,r(this,p))||ct(this.options.staleTime,r(this,p))!==ct(s.staleTime,r(this,p)))&&y(this,C,ee).call(this);const a=y(this,C,se).call(this);n&&(r(this,p)!==i||H(this.options.enabled,r(this,p))!==H(s.enabled,r(this,p))||a!==r(this,ut))&&y(this,C,ie).call(this,a)}getOptimisticResult(t){const s=r(this,_).getQueryCache().build(r(this,_),t),i=this.createResult(s,t);return Cs(this,i)&&(u(this,U,i),u(this,Tt,this.options),u(this,vt,r(this,p).state)),i}getCurrentResult(){return r(this,U)}trackResult(t,s){return new Proxy(t,{get:(i,n)=>(this.trackProp(n),s==null||s(n),Reflect.get(i,n))})}trackProp(t){r(this,qt).add(t)}getCurrentQuery(){return r(this,p)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const s=r(this,_).defaultQueryOptions(t),i=r(this,_).getQueryCache().build(r(this,_),s);return i.fetch().then(()=>this.createResult(i,s))}fetch(t){return y(this,C,jt).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),r(this,U)))}createResult(t,s){var P;const i=r(this,p),n=this.options,a=r(this,U),o=r(this,vt),h=r(this,Tt),g=t!==i?t.state:r(this,Kt),{state:O}=t;let l={...O},Q=!1,b;if(s._optimisticResults){const F=this.hasListeners(),lt=!F&&Oe(t,s),wt=F&&we(t,i,s,n);(lt||wt)&&(l={...l,...Ge(O.data,t.options)}),s._optimisticResults==="isRestoring"&&(l.fetchStatus="idle")}let{error:M,errorUpdatedAt:m,status:v}=l;b=l.data;let R=!1;if(s.placeholderData!==void 0&&b===void 0&&v==="pending"){let F;a!=null&&a.isPlaceholderData&&s.placeholderData===(h==null?void 0:h.placeholderData)?(F=a.data,R=!0):F=typeof s.placeholderData=="function"?s.placeholderData((P=r(this,At))==null?void 0:P.state.data,r(this,At)):s.placeholderData,F!==void 0&&(v="success",b=Zt(a==null?void 0:a.data,F,s),Q=!0)}if(s.select&&b!==void 0&&!R)if(a&&b===(o==null?void 0:o.data)&&s.select===r(this,Ht))b=r(this,It);else try{u(this,Ht,s.select),b=s.select(b),b=Zt(a==null?void 0:a.data,b,s),u(this,It,b),u(this,at,null)}catch(F){u(this,at,F)}r(this,at)&&(M=r(this,at),b=r(this,It),m=Date.now(),v="error");const x=l.fetchStatus==="fetching",w=v==="pending",f=v==="error",D=w&&x,k=b!==void 0,A={status:v,fetchStatus:l.fetchStatus,isPending:w,isSuccess:v==="success",isError:f,isInitialLoading:D,isLoading:D,data:b,dataUpdatedAt:l.dataUpdatedAt,error:M,errorUpdatedAt:m,failureCount:l.fetchFailureCount,failureReason:l.fetchFailureReason,errorUpdateCount:l.errorUpdateCount,isFetched:l.dataUpdateCount>0||l.errorUpdateCount>0,isFetchedAfterMount:l.dataUpdateCount>g.dataUpdateCount||l.errorUpdateCount>g.errorUpdateCount,isFetching:x,isRefetching:x&&!w,isLoadingError:f&&!k,isPaused:l.fetchStatus==="paused",isPlaceholderData:Q,isRefetchError:f&&k,isStale:fe(t,s),refetch:this.refetch,promise:r(this,nt),isEnabled:H(s.enabled,t)!==!1};if(this.options.experimental_prefetchInRender){const F=Nt=>{A.status==="error"?Nt.reject(A.error):A.data!==void 0&&Nt.resolve(A.data)},lt=()=>{const Nt=u(this,nt,A.promise=te());F(Nt)},wt=r(this,nt);switch(wt.status){case"pending":t.queryHash===i.queryHash&&F(wt);break;case"fulfilled":(A.status==="error"||A.data!==wt.value)&&lt();break;case"rejected":(A.status!=="error"||A.error!==wt.reason)&&lt();break}}return A}updateResult(){const t=r(this,U),s=this.createResult(r(this,p),this.options);if(u(this,vt,r(this,p).state),u(this,Tt,this.options),r(this,vt).data!==void 0&&u(this,At,r(this,p)),$t(s,t))return;u(this,U,s);const i=()=>{if(!t)return!0;const{notifyOnChangeProps:n}=this.options,a=typeof n=="function"?n():n;if(a==="all"||!a&&!r(this,qt).size)return!0;const o=new Set(a??r(this,qt));return this.options.throwOnError&&o.add("error"),Object.keys(r(this,U)).some(h=>{const d=h;return r(this,U)[d]!==t[d]&&o.has(d)})};y(this,C,ze).call(this,{listeners:i()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&y(this,C,re).call(this)}},_=new WeakMap,p=new WeakMap,Kt=new WeakMap,U=new WeakMap,vt=new WeakMap,Tt=new WeakMap,nt=new WeakMap,at=new WeakMap,Ht=new WeakMap,It=new WeakMap,At=new WeakMap,bt=new WeakMap,gt=new WeakMap,ut=new WeakMap,qt=new WeakMap,C=new WeakSet,jt=function(t){y(this,C,ue).call(this);let s=r(this,p).fetch(this.options,t);return t!=null&&t.throwOnError||(s=s.catch(j)),s},ee=function(){y(this,C,ne).call(this);const t=ct(this.options.staleTime,r(this,p));if(Ct||r(this,U).isStale||!Yt(t))return;const i=Ue(r(this,U).dataUpdatedAt,t)+1;u(this,bt,setTimeout(()=>{r(this,U).isStale||this.updateResult()},i))},se=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(r(this,p)):this.options.refetchInterval)??!1},ie=function(t){y(this,C,ae).call(this),u(this,ut,t),!(Ct||H(this.options.enabled,r(this,p))===!1||!Yt(r(this,ut))||r(this,ut)===0)&&u(this,gt,setInterval(()=>{(this.options.refetchIntervalInBackground||de.isFocused())&&y(this,C,jt).call(this)},r(this,ut)))},re=function(){y(this,C,ee).call(this),y(this,C,ie).call(this,y(this,C,se).call(this))},ne=function(){r(this,bt)&&(clearTimeout(r(this,bt)),u(this,bt,void 0))},ae=function(){r(this,gt)&&(clearInterval(r(this,gt)),u(this,gt,void 0))},ue=function(){const t=r(this,_).getQueryCache().build(r(this,_),this.options);if(t===r(this,p))return;const s=r(this,p);u(this,p,t),u(this,Kt,t.state),this.hasListeners()&&(s==null||s.removeObserver(this),t.addObserver(this))},ze=function(t){E.batch(()=>{t.listeners&&this.listeners.forEach(s=>{s(r(this,U))}),r(this,_).getQueryCache().notify({query:r(this,p),type:"observerResultsUpdated"})})},Te);function gs(e,t){return H(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Oe(e,t){return gs(e,t)||e.state.data!==void 0&&oe(e,t,t.refetchOnMount)}function oe(e,t,s){if(H(t.enabled,e)!==!1&&ct(t.staleTime,e)!=="static"){const i=typeof s=="function"?s(e):s;return i==="always"||i!==!1&&fe(e,t)}return!1}function we(e,t,s,i){return(e!==t||H(i.enabled,e)===!1)&&(!s.suspense||e.state.status!=="error")&&fe(e,s)}function fe(e,t){return H(t.enabled,e)!==!1&&e.isStaleByTime(ct(t.staleTime,e))}function Cs(e,t){return!$t(e.getCurrentResult(),t)}var ot,ht,L,J,Y,zt,he,Ie,Os=(Ie=class extends Ut{constructor(t,s){super();c(this,Y);c(this,ot);c(this,ht);c(this,L);c(this,J);u(this,ot,t),this.setOptions(s),this.bindMethods(),y(this,Y,zt).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var i;const s=this.options;this.options=r(this,ot).defaultMutationOptions(t),$t(this.options,s)||r(this,ot).getMutationCache().notify({type:"observerOptionsUpdated",mutation:r(this,L),observer:this}),s!=null&&s.mutationKey&&this.options.mutationKey&&Ot(s.mutationKey)!==Ot(this.options.mutationKey)?this.reset():((i=r(this,L))==null?void 0:i.state.status)==="pending"&&r(this,L).setOptions(this.options)}onUnsubscribe(){var t;this.hasListeners()||(t=r(this,L))==null||t.removeObserver(this)}onMutationUpdate(t){y(this,Y,zt).call(this),y(this,Y,he).call(this,t)}getCurrentResult(){return r(this,ht)}reset(){var t;(t=r(this,L))==null||t.removeObserver(this),u(this,L,void 0),y(this,Y,zt).call(this),y(this,Y,he).call(this)}mutate(t,s){var i;return u(this,J,s),(i=r(this,L))==null||i.removeObserver(this),u(this,L,r(this,ot).getMutationCache().build(r(this,ot),this.options)),r(this,L).addObserver(this),r(this,L).execute(t)}},ot=new WeakMap,ht=new WeakMap,L=new WeakMap,J=new WeakMap,Y=new WeakSet,zt=function(){var s;const t=((s=r(this,L))==null?void 0:s.state)??Be();u(this,ht,{...t,isPending:t.status==="pending",isSuccess:t.status==="success",isError:t.status==="error",isIdle:t.status==="idle",mutate:this.mutate,reset:this.reset})},he=function(t){E.batch(()=>{var s,i,n,a,o,h,d,g;if(r(this,J)&&this.hasListeners()){const O=r(this,ht).variables,l=r(this,ht).context;(t==null?void 0:t.type)==="success"?((i=(s=r(this,J)).onSuccess)==null||i.call(s,t.data,O,l),(a=(n=r(this,J)).onSettled)==null||a.call(n,t.data,null,O,l)):(t==null?void 0:t.type)==="error"&&((h=(o=r(this,J)).onError)==null||h.call(o,t.error,O,l),(g=(d=r(this,J)).onSettled)==null||g.call(d,void 0,t.error,O,l))}this.listeners.forEach(O=>{O(r(this,ht))})})},Ie),$e=I.createContext(void 0),Ve=e=>{const t=I.useContext($e);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},Us=({client:e,children:t})=>(I.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),ss.jsx($e.Provider,{value:e,children:t})),We=I.createContext(!1),ws=()=>I.useContext(We);We.Provider;function Rs(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var Ss=I.createContext(Rs()),Ps=()=>I.useContext(Ss),Fs=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},Es=e=>{I.useEffect(()=>{e.clearReset()},[e])},Qs=({result:e,errorResetBoundary:t,throwOnError:s,query:i,suspense:n})=>e.isError&&!t.isReset()&&!e.isFetching&&i&&(n&&e.data===void 0||_e(s,[e.error,i])),Ms=e=>{if(e.suspense){const t=i=>i==="static"?i:Math.max(i??1e3,1e3),s=e.staleTime;e.staleTime=typeof s=="function"?(...i)=>t(s(...i)):t(s),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},xs=(e,t)=>e.isLoading&&e.isFetching&&!t,Ds=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,Re=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function Ts(e,t,s){var l,Q,b,M,m;const i=ws(),n=Ps(),a=Ve(),o=a.defaultQueryOptions(e);(Q=(l=a.getDefaultOptions().queries)==null?void 0:l._experimental_beforeQuery)==null||Q.call(l,o),o._optimisticResults=i?"isRestoring":"optimistic",Ms(o),Fs(o,n),Es(n);const h=!a.getQueryCache().get(o.queryHash),[d]=I.useState(()=>new t(a,o)),g=d.getOptimisticResult(o),O=!i&&e.subscribed!==!1;if(I.useSyncExternalStore(I.useCallback(v=>{const R=O?d.subscribe(E.batchCalls(v)):j;return d.updateResult(),R},[d,O]),()=>d.getCurrentResult(),()=>d.getCurrentResult()),I.useEffect(()=>{d.setOptions(o)},[o,d]),Ds(o,g))throw Re(o,d,n);if(Qs({result:g,errorResetBoundary:n,throwOnError:o.throwOnError,query:a.getQueryCache().get(o.queryHash),suspense:o.suspense}))throw g.error;if((M=(b=a.getDefaultOptions().queries)==null?void 0:b._experimental_afterQuery)==null||M.call(b,o,g),o.experimental_prefetchInRender&&!Ct&&xs(g,i)){const v=h?Re(o,d,n):(m=a.getQueryCache().get(o.queryHash))==null?void 0:m.promise;v==null||v.catch(j).finally(()=>{d.updateResult()})}return o.notifyOnChangeProps?g:d.trackResult(g)}function js(e,t){return Ts(e,bs)}function ks(e,t){const s=Ve(),[i]=I.useState(()=>new Os(s,e));I.useEffect(()=>{i.setOptions(e)},[i,e]);const n=I.useSyncExternalStore(I.useCallback(o=>i.subscribe(E.batchCalls(o)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),a=I.useCallback((o,h)=>{i.mutate(o,h).catch(j)},[i]);if(n.error&&_e(i.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:a,mutateAsync:n.mutate}}export{qs as Q,ks as a,js as b,Us as c,ss as j,Ve as u};
//# sourceMappingURL=query-CnUghAyc.js.map
