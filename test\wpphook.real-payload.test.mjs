import { describe, test, expect, beforeEach, jest } from '@jest/globals';

// Mock capfunctions module
const mockBaseResponse = {
    ok: jest.fn((message, data) => ({ statusCode: 200, message, data })),
    error: jest.fn((message, data) => ({ statusCode: 500, message, data }))
};

jest.unstable_mockModule('capfunctions', () => ({
    baseResponse: mockBaseResponse
}));

const { parseWebhookPayload } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

describe('Real WhatsApp Payload Test', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('should parse the exact payload from the error log', () => {
        // This is the exact payload from the error log provided
        const realPayload = {
            "object": "whatsapp_business_account",
            "entry": [{
                "id": "***************",
                "changes": [{
                    "value": {
                        "messaging_product": "whatsapp",
                        "metadata": {
                            "display_phone_number": "*************",
                            "phone_number_id": "***************"
                        },
                        "contacts": [{
                            "profile": {
                                "name": "Vinicius"
                            },
                            "wa_id": "*************"
                        }],
                        "messages": [{
                            "from": "*************",
                            "id": "wamid.************************************************************",
                            "timestamp": "**********",
                            "text": {
                                "body": "Ola"
                            },
                            "type": "text"
                        }]
                    },
                    "field": "messages"
                }]
            }]
        };

        const event = {
            body: JSON.stringify(realPayload)
        };

        const result = parseWebhookPayload(event);

        expect(result).toBeDefined();
        expect(result.userId).toBe('*************');
        expect(result.phoneNumber).toBe('*************');
        expect(result.message).toBe('Ola');
        expect(result.profileName).toBe('Vinicius');
        expect(result.messageId).toBe('wamid.************************************************************');
        expect(result.timestamp).toBe('**********');
        expect(result.originalPayload).toEqual(realPayload);
    });

    test('should simulate the exact event structure from AWS Lambda', () => {
        // Simulating the full AWS Lambda event structure from the log
        const fullEvent = {
            "version": "2.0",
            "routeKey": "$default",
            "rawPath": "/wpphook",
            "rawQueryString": "",
            "headers": {
                "x-hub-signature-256": "sha256=dd2959473f5d403149e61285fbae4feb554424cc58a14b70b9557342c708380e",
                "content-length": "477",
                "content-type": "application/json",
                "user-agent": "facebookexternalua"
            },
            "requestContext": {
                "http": {
                    "method": "POST",
                    "path": "/wpphook"
                }
            },
            "body": JSON.stringify({
                "object": "whatsapp_business_account",
                "entry": [{
                    "id": "***************",
                    "changes": [{
                        "value": {
                            "messaging_product": "whatsapp",
                            "metadata": {
                                "display_phone_number": "*************",
                                "phone_number_id": "***************"
                            },
                            "contacts": [{
                                "profile": {
                                    "name": "Vinicius"
                                },
                                "wa_id": "*************"
                            }],
                            "messages": [{
                                "from": "*************",
                                "id": "wamid.************************************************************",
                                "timestamp": "**********",
                                "text": {
                                    "body": "Ola"
                                },
                                "type": "text"
                            }]
                        },
                        "field": "messages"
                    }]
                }]
            }),
            "isBase64Encoded": false
        };

        const result = parseWebhookPayload(fullEvent);

        expect(result).toBeDefined();
        expect(result.userId).toBe('*************');
        expect(result.phoneNumber).toBe('*************');
        expect(result.message).toBe('Ola');
        expect(result.profileName).toBe('Vinicius');
        
        // Verify that the parsing doesn't fail and returns the expected structure
        expect(typeof result.userId).toBe('string');
        expect(typeof result.phoneNumber).toBe('string');
        expect(typeof result.message).toBe('string');
        expect(result.message.length).toBeGreaterThan(0);
    });
});