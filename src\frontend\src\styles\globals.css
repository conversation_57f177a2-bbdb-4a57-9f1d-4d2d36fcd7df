/* =====================================================
   GLOBAL STYLES - ESTILOS GLOBAIS
   Configuração do Tailwind CSS e estilos customizados
   ===================================================== */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Variáveis CSS para temas */
:root {
  /* Toast colors */
  --toast-bg: #ffffff;
  --toast-color: #1f2937;
  --toast-border: #e5e7eb;
  
  /* Design system colors */
  --background: 0 0% 100%;
  --foreground: 220 14% 24%;
  --border: 214 13% 92%;
}

.dark {
  --toast-bg: #1f2937;
  --toast-color: #f9fafb;
  --toast-border: #374151;
  
  /* Design system colors */
  --background: 220 14% 24%;
  --foreground: 220 12% 98%;
  --border: 217 12% 32%;
}

/* Base styles */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Scrollbar customizado */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-secondary-100 dark:bg-secondary-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-secondary-300 dark:bg-secondary-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary-400 dark:bg-secondary-500;
  }
  
  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) rgb(241 245 249);
  }
  
  .dark * {
    scrollbar-color: rgb(75 85 99) rgb(31 41 55);
  }
}

/* Component styles */
@layer components {
  /* Loading spinner */
  .animate-spin-slow {
    animation: spin 3s linear infinite;
  }
  
  /* Fade in animation */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Slide in animation */
  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }
  
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  /* Bounce animation */
  .animate-bounce-subtle {
    animation: bounceSubtle 2s infinite;
  }
  
  @keyframes bounceSubtle {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }
  
  /* Pulse animation */
  .animate-pulse-slow {
    animation: pulse 3s infinite;
  }
  
  /* Focus ring */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-secondary-900;
  }
  
  /* Button base */
  .btn-base {
    @apply inline-flex items-center justify-center font-medium transition-all duration-200 focus-ring disabled:opacity-50 disabled:cursor-not-allowed select-none;
  }
  
  /* Input base */
  .input-base {
    @apply block w-full transition-all duration-200 placeholder:text-secondary-400 dark:placeholder:text-secondary-500 focus-ring disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-secondary-50 dark:disabled:bg-secondary-800;
  }
  
  /* Card base */
  .card-base {
    @apply bg-white border border-secondary-200 dark:bg-secondary-900 dark:border-secondary-700 transition-all duration-200;
  }
  
  /* Text styles */
  .text-heading {
    @apply text-secondary-900 dark:text-secondary-100 font-semibold;
  }
  
  .text-body {
    @apply text-secondary-700 dark:text-secondary-300;
  }
  
  .text-muted {
    @apply text-secondary-500 dark:text-secondary-400;
  }
  
  .text-error {
    @apply text-error-600 dark:text-error-400;
  }
  
  .text-success {
    @apply text-success-600 dark:text-success-400;
  }
  
  .text-warning {
    @apply text-warning-600 dark:text-warning-400;
  }
  
  /* Background styles */
  .bg-surface {
    @apply bg-white dark:bg-secondary-900;
  }
  
  .bg-surface-secondary {
    @apply bg-secondary-50 dark:bg-secondary-800;
  }
  
  /* Border styles */
  .border-default {
    @apply border-secondary-200 dark:border-secondary-700;
  }
  
  /* Shadow styles */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-strong {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 4px 25px -5px rgba(0, 0, 0, 0.1);
  }
  
  /* Dark mode shadows */
  .dark .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.3), 0 10px 20px -2px rgba(0, 0, 0, 0.2);
  }
  
  .dark .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  }
  
  .dark .shadow-strong {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.5), 0 4px 25px -5px rgba(0, 0, 0, 0.3);
  }
}

/* Utility styles */
@layer utilities {
  /* Hide scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Gradient text */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-400 bg-clip-text text-transparent;
  }
  
  /* Backdrop blur */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  /* Safe area */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
