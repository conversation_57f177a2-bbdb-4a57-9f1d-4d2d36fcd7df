const fs = require('fs');
const { getParams, execTerminal } = require('./functions');

const main = async () => {
  try {
    const { profile, region = 'us-east-2' } = await getParams(['profile']);

    const result = await execTerminal(
      `aws lambda list-layers --profile ${profile} --region ${region}`
    );

    const fileName = `layers-${profile}-${region}.local.json`;
    fs.writeFileSync(fileName, result);

    console.log('\nFinalizado!\nVisualize em ' + fileName);
    console.log('\n');
  } catch (error) {
    console.log('\n');
    console.log('\x1b[31m%s\x1b[0m', 'Catch no listLayers():');
    console.log(error.message);
    console.log('\n');
  } finally {
    process.exit();
  }
};

main();
