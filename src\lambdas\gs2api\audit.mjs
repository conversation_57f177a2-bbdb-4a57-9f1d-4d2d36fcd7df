import { v4 as uuidv4 } from 'uuid';
import db from './database.mjs';

class AuditLogger {
  constructor() {
    this.tableName = 'audit_logs';
  }

  /**
   * Registra uma ação no log de auditoria
   */
  async log({
    userCpf = null,
    userName = null,
    userEmail = null,
    operationType,
    resourceType = null,
    resourceId = null,
    resourceIdentifier = null,
    actionDescription = null,
    oldValues = null,
    newValues = null,
    ipAddress = null,
    userAgent = null,
    success = true,
    complianceLevel = 'STANDARD',
    metadata = null
  }) {
    try {
      const auditRecord = {
        uuid: uuidv4(),
        user_cpf: userCpf,
        user_name: userName,
        user_email: userEmail,
        operation_type: operationType,
        resource_type: resourceType,
        resource_id: resourceId ? String(resourceId) : null,
        resource_identifier: resourceIdentifier,
        action_description: actionDescription,
        old_values: oldValues ? JSON.stringify(oldValues) : null,
        new_values: newValues ? JSON.stringify(newValues) : null,
        ip_address: ipAddress,
        user_agent: userAgent,
        success: success,
        compliance_level: complianceLevel,
        metadata: metadata ? JSON.stringify(metadata) : null,
        created_at: new Date()
      };

      await db(this.tableName).insert(auditRecord);
      
      console.log(`Audit log created: ${operationType} by ${userCpf || 'system'}`);
      return auditRecord.uuid;
    } catch (error) {
      console.error('Failed to create audit log:', error);
      // Não falhar a operação principal por causa do log
      return null;
    }
  }

  /**
   * Log de login
   */
  async logLogin(userCpf, userName, userEmail, success, ipAddress, userAgent, errorMessage = null) {
    return this.log({
      userCpf,
      userName,
      userEmail,
      operationType: 'LOGIN',
      actionDescription: success ? 'Login realizado com sucesso' : `Falha no login: ${errorMessage}`,
      ipAddress,
      userAgent,
      success,
      complianceLevel: 'HIGH'
    });
  }

  /**
   * Log de logout
   */
  async logLogout(userCpf, userName, userEmail, ipAddress, userAgent) {
    return this.log({
      userCpf,
      userName,
      userEmail,
      operationType: 'LOGOUT',
      actionDescription: 'Logout realizado',
      ipAddress,
      userAgent,
      success: true,
      complianceLevel: 'STANDARD'
    });
  }

  /**
   * Log de criação de recurso
   */
  async logCreate(userCpf, userName, resourceType, resourceId, newValues, ipAddress, userAgent) {
    return this.log({
      userCpf,
      userName,
      operationType: 'CREATE',
      resourceType,
      resourceId,
      actionDescription: `${resourceType} criado`,
      newValues,
      ipAddress,
      userAgent,
      success: true,
      complianceLevel: 'STANDARD'
    });
  }

  /**
   * Log de atualização de recurso
   */
  async logUpdate(userCpf, userName, resourceType, resourceId, oldValues, newValues, ipAddress, userAgent) {
    return this.log({
      userCpf,
      userName,
      operationType: 'UPDATE',
      resourceType,
      resourceId,
      actionDescription: `${resourceType} atualizado`,
      oldValues,
      newValues,
      ipAddress,
      userAgent,
      success: true,
      complianceLevel: 'STANDARD'
    });
  }

  /**
   * Log de exclusão de recurso
   */
  async logDelete(userCpf, userName, resourceType, resourceId, oldValues, ipAddress, userAgent) {
    return this.log({
      userCpf,
      userName,
      operationType: 'DELETE',
      resourceType,
      resourceId,
      actionDescription: `${resourceType} excluído`,
      oldValues,
      ipAddress,
      userAgent,
      success: true,
      complianceLevel: 'HIGH'
    });
  }

  /**
   * Log de acesso a dados sensíveis
   */
  async logDataAccess(userCpf, userName, resourceType, resourceId, actionDescription, ipAddress, userAgent) {
    return this.log({
      userCpf,
      userName,
      operationType: 'DATA_ACCESS',
      resourceType,
      resourceId,
      actionDescription,
      ipAddress,
      userAgent,
      success: true,
      complianceLevel: 'CRITICAL'
    });
  }

  /**
   * Buscar logs de auditoria
   */
  async getLogs(filters = {}) {
    try {
      let query = db(this.tableName);

      if (filters.userCpf) {
        query = query.where('user_cpf', filters.userCpf);
      }

      if (filters.operationType) {
        query = query.where('operation_type', filters.operationType);
      }

      if (filters.resourceType) {
        query = query.where('resource_type', filters.resourceType);
      }

      if (filters.dateFrom) {
        query = query.where('created_at', '>=', filters.dateFrom);
      }

      if (filters.dateTo) {
        query = query.where('created_at', '<=', filters.dateTo);
      }

      if (filters.success !== undefined) {
        query = query.where('success', filters.success);
      }

      const logs = await query
        .orderBy('created_at', 'desc')
        .limit(filters.limit || 100)
        .offset(filters.offset || 0);

      return logs;
    } catch (error) {
      console.error('Failed to get audit logs:', error);
      throw error;
    }
  }
}

export default new AuditLogger();
