// =====================================================
// BUTTON COMPONENT - COMPONENTE DE BOTÃO
// Componente de botão reutilizável e customizável
// =====================================================

import React from 'react'
import { motion } from 'framer-motion'
import { Loader2 } from 'lucide-react'
import { cn } from '@/utils'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success' | 'warning'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  loading?: boolean
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
  fullWidth?: boolean
  rounded?: boolean
  children: React.ReactNode
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    loading = false,
    icon,
    iconPosition = 'left',
    fullWidth = false,
    rounded = false,
    disabled,
    children,
    ...props
  }, ref) => {
    const baseClasses = [
      'inline-flex items-center justify-center font-medium transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'select-none'
    ]

    const variantClasses = {
      primary: [
        'bg-primary-600 text-white shadow-sm',
        'hover:bg-primary-700 focus:ring-primary-500',
        'active:bg-primary-800'
      ],
      secondary: [
        'bg-secondary-100 text-secondary-900 shadow-sm',
        'hover:bg-secondary-200 focus:ring-secondary-500',
        'active:bg-secondary-300',
        'dark:bg-secondary-800 dark:text-secondary-100',
        'dark:hover:bg-secondary-700 dark:active:bg-secondary-600'
      ],
      outline: [
        'border border-secondary-300 bg-white text-secondary-700 shadow-sm',
        'hover:bg-secondary-50 focus:ring-primary-500',
        'active:bg-secondary-100',
        'dark:border-secondary-600 dark:bg-secondary-900 dark:text-secondary-300',
        'dark:hover:bg-secondary-800 dark:active:bg-secondary-700'
      ],
      ghost: [
        'text-secondary-700 bg-transparent',
        'hover:bg-secondary-100 focus:ring-primary-500',
        'active:bg-secondary-200',
        'dark:text-secondary-300 dark:hover:bg-secondary-800',
        'dark:active:bg-secondary-700'
      ],
      danger: [
        'bg-error-600 text-white shadow-sm',
        'hover:bg-error-700 focus:ring-error-500',
        'active:bg-error-800'
      ],
      success: [
        'bg-success-600 text-white shadow-sm',
        'hover:bg-success-700 focus:ring-success-500',
        'active:bg-success-800'
      ],
      warning: [
        'bg-warning-600 text-white shadow-sm',
        'hover:bg-warning-700 focus:ring-warning-500',
        'active:bg-warning-800'
      ]
    }

    const sizeClasses = {
      xs: 'px-2 py-1 text-xs rounded',
      sm: 'px-3 py-1.5 text-sm rounded-md',
      md: 'px-4 py-2 text-sm rounded-md',
      lg: 'px-6 py-3 text-base rounded-lg',
      xl: 'px-8 py-4 text-lg rounded-lg'
    }

    const iconSizeClasses = {
      xs: 'w-3 h-3',
      sm: 'w-4 h-4',
      md: 'w-4 h-4',
      lg: 'w-5 h-5',
      xl: 'w-6 h-6'
    }

    const spacingClasses = {
      xs: icon ? (iconPosition === 'left' ? 'ml-1' : 'mr-1') : '',
      sm: icon ? (iconPosition === 'left' ? 'ml-1.5' : 'mr-1.5') : '',
      md: icon ? (iconPosition === 'left' ? 'ml-2' : 'mr-2') : '',
      lg: icon ? (iconPosition === 'left' ? 'ml-2.5' : 'mr-2.5') : '',
      xl: icon ? (iconPosition === 'left' ? 'ml-3' : 'mr-3') : ''
    }

    const classes = cn(
      baseClasses,
      variantClasses[variant],
      rounded ? 'rounded-full' : sizeClasses[size],
      fullWidth && 'w-full',
      className
    )

    const iconElement = loading ? (
      <Loader2 className={cn(iconSizeClasses[size], 'animate-spin')} />
    ) : icon ? (
      <span className={iconSizeClasses[size]}>{icon}</span>
    ) : null

    return (
      <motion.button
        ref={ref}
        className={classes}
        disabled={disabled || loading}
        whileHover={{ scale: disabled || loading ? 1 : 1.02 }}
        whileTap={{ scale: disabled || loading ? 1 : 0.98 }}
        transition={{ duration: 0.1 }}
        {...props}
      >
        {iconElement && iconPosition === 'left' && (
          <span className={children ? spacingClasses[size] : ''}>{iconElement}</span>
        )}
        
        {loading ? (
          <span className="ml-2">Carregando...</span>
        ) : (
          children
        )}
        
        {iconElement && iconPosition === 'right' && !loading && (
          <span className={children ? spacingClasses[size] : ''}>{iconElement}</span>
        )}
      </motion.button>
    )
  }
)

Button.displayName = 'Button'

export { Button }
export type { ButtonProps }
