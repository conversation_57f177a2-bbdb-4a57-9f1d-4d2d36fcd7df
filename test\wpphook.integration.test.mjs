import { describe, test, expect, beforeEach, jest } from '@jest/globals';

// Mock capfunctions module
const mockBaseResponse = {
    ok: jest.fn((message, data) => ({ statusCode: 200, message, data })),
    error: jest.fn((message, data) => ({ statusCode: 500, message, data }))
};

jest.unstable_mockModule('capfunctions', () => ({
    baseResponse: mockBaseResponse
}));

// Mock dependencies for integration tests
const mockDatabaseInstance = {
    initialize: jest.fn(),
    getUserSession: jest.fn(),
    createUserSession: jest.fn(),
    updateUserSession: jest.fn(),
    saveUserData: jest.fn(),
    logInteraction: jest.fn(),
    healthCheck: jest.fn(),
    executeQuery: jest.fn(),
    getInteractionHistory: jest.fn(),
    cleanupOldSessions: jest.fn(),
    deleteUserSession: jest.fn(),
    markTermsAccepted: jest.fn(),
    findClinic: jest.fn()
};

jest.unstable_mockModule('../src/lambdas/gs2api/wpphook/database.mjs', () => ({
    default: jest.fn(() => mockDatabaseInstance)
}));

const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

describe('wpphook Integration Tests', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        
        // Setup default mock responses
        mockDatabaseInstance.initialize.mockResolvedValue(true);
        mockDatabaseInstance.healthCheck.mockResolvedValue({
            status: 'healthy',
            timestamp: new Date().toISOString()
        });
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('Complete Chatbot Flow', () => {
        test('should handle complete registration flow', async () => {
            const userId = '5511999999999';
            const phoneNumber = '+5511999999999';
            
            // Step 1: Initial GS2 trigger
            mockDatabaseInstance.getUserSession.mockResolvedValueOnce(null);
            mockDatabaseInstance.createUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'INITIAL',
                session_data: {}
            });

            const initialEvent = {
                body: JSON.stringify({
                    message: 'GS2',
                    from: phoneNumber,
                    phone: phoneNumber
                })
            };

            const initialResult = await handler(initialEvent, {});
            expect(initialResult.statusCode).toBe(200);
            expect(mockDatabaseInstance.createUserSession).toHaveBeenCalled();
            expect(mockDatabaseInstance.logInteraction).toHaveBeenCalledWith(
                userId, 'INCOMING', 'GS2'
            );

            // Step 2: User confirms they want to proceed
            mockDatabaseInstance.getUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'GREETING',
                session_data: { startTime: expect.any(String) }
            });

            const greetingEvent = {
                body: JSON.stringify({
                    message: '✅ Claro',
                    from: phoneNumber
                })
            };

            const greetingResult = await handler(greetingEvent, {});
            expect(greetingResult.statusCode).toBe(200);
            expect(mockDatabaseInstance.updateUserSession).toHaveBeenCalled();

            // Step 3: User provides clinic name
            mockDatabaseInstance.getUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'CLINIC_REQUEST',
                session_data: { startTime: expect.any(String) }
            });

            mockDatabaseInstance.findClinic.mockResolvedValueOnce([{
                id: 1,
                name: 'Hospital Teste',
                razao_social: 'Hospital Teste Ltda',
                cnpj: '12345678000199'
            }]);

            const clinicEvent = {
                body: JSON.stringify({
                    message: 'Hospital Teste',
                    from: phoneNumber
                })
            };

            const clinicResult = await handler(clinicEvent, {});
            expect(clinicResult.statusCode).toBe(200);
            expect(mockDatabaseInstance.findClinic).toHaveBeenCalledWith('Hospital Teste');

            // Step 4: User confirms clinic
            mockDatabaseInstance.getUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'CLINIC_CONFIRMATION',
                session_data: {
                    startTime: expect.any(String),
                    clinicData: {
                        id: 1,
                        name: 'Hospital Teste',
                        razaoSocial: 'Hospital Teste Ltda',
                        cnpj: '12345678000199'
                    }
                }
            });

            const confirmClinicEvent = {
                body: JSON.stringify({
                    message: '✅ Sim',
                    from: phoneNumber
                })
            };

            const confirmClinicResult = await handler(confirmClinicEvent, {});
            expect(confirmClinicResult.statusCode).toBe(200);

            // Step 5: User provides name
            mockDatabaseInstance.getUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'NAME_REQUEST',
                session_data: {
                    startTime: expect.any(String),
                    clinicData: {
                        id: 1,
                        name: 'Hospital Teste',
                        razaoSocial: 'Hospital Teste Ltda',
                        cnpj: '12345678000199'
                    }
                }
            });

            const nameEvent = {
                body: JSON.stringify({
                    message: 'João da Silva',
                    from: phoneNumber
                })
            };

            const nameResult = await handler(nameEvent, {});
            expect(nameResult.statusCode).toBe(200);

            // Step 6: User provides CPF
            mockDatabaseInstance.getUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'CPF_REQUEST',
                session_data: {
                    startTime: expect.any(String),
                    clinicData: {
                        id: 1,
                        name: 'Hospital Teste',
                        razaoSocial: 'Hospital Teste Ltda',
                        cnpj: '12345678000199'
                    },
                    userData: {
                        fullName: 'João da Silva'
                    }
                }
            });

            const cpfEvent = {
                body: JSON.stringify({
                    message: '12345678901',
                    from: phoneNumber
                })
            };

            const cpfResult = await handler(cpfEvent, {});
            expect(cpfResult.statusCode).toBe(200);

            // Step 7: User provides email
            mockDatabaseInstance.getUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'EMAIL_REQUEST',
                session_data: {
                    startTime: expect.any(String),
                    clinicData: {
                        id: 1,
                        name: 'Hospital Teste',
                        razaoSocial: 'Hospital Teste Ltda',
                        cnpj: '12345678000199'
                    },
                    userData: {
                        fullName: 'João da Silva',
                        cpf: '12345678901'
                    }
                }
            });

            const emailEvent = {
                body: JSON.stringify({
                    message: '<EMAIL>',
                    from: phoneNumber
                })
            };

            const emailResult = await handler(emailEvent, {});
            expect(emailResult.statusCode).toBe(200);

            // Step 8: User confirms data
            mockDatabaseInstance.getUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'DATA_CONFIRMATION',
                session_data: {
                    startTime: expect.any(String),
                    clinicData: {
                        id: 1,
                        name: 'Hospital Teste',
                        razaoSocial: 'Hospital Teste Ltda',
                        cnpj: '12345678000199'
                    },
                    userData: {
                        fullName: 'João da Silva',
                        cpf: '12345678901',
                        email: '<EMAIL>'
                    }
                }
            });

            const confirmDataEvent = {
                body: JSON.stringify({
                    message: '👍 Está certo',
                    from: phoneNumber
                })
            };

            const confirmDataResult = await handler(confirmDataEvent, {});
            expect(confirmDataResult.statusCode).toBe(200);

            // Step 9: User accepts terms
            mockDatabaseInstance.getUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'TERMS_PRESENTATION',
                session_data: {
                    startTime: expect.any(String),
                    clinicData: {
                        id: 1,
                        name: 'Hospital Teste',
                        razaoSocial: 'Hospital Teste Ltda',
                        cnpj: '12345678000199'
                    },
                    userData: {
                        fullName: 'João da Silva',
                        cpf: '12345678901',
                        email: '<EMAIL>'
                    }
                }
            });

            const acceptTermsEvent = {
                body: JSON.stringify({
                    message: '✅ Sim, de acordo',
                    from: phoneNumber
                })
            };

            const acceptTermsResult = await handler(acceptTermsEvent, {});
            expect(acceptTermsResult.statusCode).toBe(200);
            expect(mockDatabaseInstance.saveUserData).toHaveBeenCalledWith(
                userId,
                1,
                expect.objectContaining({
                    fullName: 'João da Silva',
                    cpf: '12345678901',
                    email: '<EMAIL>',
                    termsAccepted: true
                })
            );
        });

        test('should handle exit command at any point', async () => {
            const userId = '5511999999999';
            const phoneNumber = '+5511999999999';

            mockDatabaseInstance.getUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'GREETING',
                session_data: { startTime: expect.any(String) }
            });

            const exitEvent = {
                body: JSON.stringify({
                    message: 'sair',
                    from: phoneNumber
                })
            };

            const result = await handler(exitEvent, {});
            expect(result.statusCode).toBe(200);
            expect(mockDatabaseInstance.updateUserSession).toHaveBeenCalledWith(
                userId,
                'END',
                expect.any(Object)
            );
        });

        test('should handle invalid input gracefully', async () => {
            const userId = '5511999999999';
            const phoneNumber = '+5511999999999';

            mockDatabaseInstance.getUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'CPF_REQUEST',
                session_data: {
                    userData: { fullName: 'João da Silva' }
                }
            });

            const invalidCpfEvent = {
                body: JSON.stringify({
                    message: '123', // Invalid CPF
                    from: phoneNumber
                })
            };

            const result = await handler(invalidCpfEvent, {});
            expect(result.statusCode).toBe(200);
            // Should remain in same state and ask for valid CPF
        });

        test('should handle FAQ flow correctly', async () => {
            const userId = '5511999999999';
            const phoneNumber = '+5511999999999';

            mockDatabaseInstance.getUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'TERMS_PRESENTATION',
                session_data: {
                    clinicData: { id: 1 },
                    userData: {
                        fullName: 'João da Silva',
                        cpf: '12345678901',
                        email: '<EMAIL>'
                    }
                }
            });

            // User wants to know more about terms
            const faqEvent = {
                body: JSON.stringify({
                    message: 'ℹ️ Quero saber mais',
                    from: phoneNumber
                })
            };

            const faqResult = await handler(faqEvent, {});
            expect(faqResult.statusCode).toBe(200);

            // User asks about how it works
            mockDatabaseInstance.getUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'FAQ_MENU',
                session_data: {
                    clinicData: { id: 1 },
                    userData: {
                        fullName: 'João da Silva',
                        cpf: '12345678901',
                        email: '<EMAIL>'
                    }
                }
            });

            const howItWorksEvent = {
                body: JSON.stringify({
                    message: '❓Como funciona',
                    from: phoneNumber
                })
            };

            const howItWorksResult = await handler(howItWorksEvent, {});
            expect(howItWorksResult.statusCode).toBe(200);
        });
    });

    describe('Error Handling', () => {
        test('should handle database connection errors', async () => {
            mockDatabaseInstance.initialize.mockRejectedValue(new Error('Database connection failed'));

            const event = {
                body: JSON.stringify({
                    message: 'GS2',
                    from: '+5511999999999'
                })
            };

            const result = await handler(event, {});
            expect(result.statusCode).toBe(500);
            expect(result.body).toContain('erro interno');
        });

        test('should handle malformed webhook payloads', async () => {
            const event = {
                body: 'invalid json'
            };

            const result = await handler(event, {});
            expect(result.statusCode).toBe(500);
            expect(result.body).toContain('erro interno');
        });

        test('should handle missing required fields in webhook', async () => {
            const event = {
                body: JSON.stringify({
                    message: 'GS2'
                    // missing 'from' field
                })
            };

            const result = await handler(event, {});
            expect(result.statusCode).toBe(500);
            expect(result.body).toContain('erro interno');
        });
    });

    describe('Health Check', () => {
        test('should respond to health check requests', async () => {
            const healthEvent = {
                httpMethod: 'GET',
                path: '/health'
            };

            const result = await handler(healthEvent, {});
            expect(result.statusCode).toBe(200);
            expect(result.body).toContain('healthy');
        });
    });

    describe('Session Management', () => {
        test('should create new session for new user', async () => {
            const userId = '5511999999999';
            const phoneNumber = '+5511999999999';

            mockDatabaseInstance.getUserSession.mockResolvedValueOnce(null);
            mockDatabaseInstance.createUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'INITIAL',
                session_data: {}
            });

            const event = {
                body: JSON.stringify({
                    message: 'GS2',
                    from: phoneNumber
                })
            };

            const result = await handler(event, {});
            expect(result.statusCode).toBe(200);
            expect(mockDatabaseInstance.createUserSession).toHaveBeenCalledWith(
                userId,
                phoneNumber
            );
        });

        test('should resume existing session', async () => {
            const userId = '5511999999999';
            const phoneNumber = '+5511999999999';

            mockDatabaseInstance.getUserSession.mockResolvedValueOnce({
                id: userId,
                phone_number: phoneNumber,
                current_state: 'GREETING',
                session_data: { startTime: expect.any(String) },
                updated_at: new Date() // Recent update
            });

            const event = {
                body: JSON.stringify({
                    message: '✅ Claro',
                    from: phoneNumber
                })
            };

            const result = await handler(event, {});
            expect(result.statusCode).toBe(200);
            expect(mockDatabaseInstance.createUserSession).not.toHaveBeenCalled();
        });
    });
});