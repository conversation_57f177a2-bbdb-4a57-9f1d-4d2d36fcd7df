-- Verificar charset para utf8mb4;

CREATE TABLE capAtendimentoFechaAditivo_bkp SELECT * FROM capAtendimentoFechaAditivo;
DROP TABLE capAtendimentoFechaAditivo;

CREATE TABLE capAtendimentoFechamento_bkp SELECT * FROM capAtendimentoFechamento;
DROP TABLE capAtendimentoFechamento;

CREATE TABLE `capAtendimentoFechamento` (
  `isInstSaude` VARCHAR(100) NOT NULL,
  `laNome` VARCHAR(100) NOT NULL,
  `esEspecialidade` VARCHAR(100) NOT NULL,
  `ocNrContrato` VARCHAR(50) NOT NULL,
  `clCliente` VARCHAR(100) NOT NULL,
  `ceTipoPagamento` VARCHAR(100) NOT NULL,
  `opNr<PERSON>lantao` INT NOT NULL,
  `psCPF` VARCHAR(11) NOT NULL,
  `adDataSolicitacao` TIMESTAMP NOT NULL,
  `afNome` VARCHAR(100) NOT NULL,
  `afIni` TIMESTAMP NOT NULL,
  `afFim` TIMESTAMP NOT NULL,
  `codFechamento` INT NULL,
  `afValor` FLOAT NULL,
  `anNrAntecipacao` INT NULL,
  `adSituacao` VARCHAR(45) NULL,
  `afUsuarioAprovacao` VARCHAR(11) NULL,
  `afDataAprovacao` TIMESTAMP NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`, `psCPF`, `adDataSolicitacao`, `afNome`, `afIni`),
  INDEX `fk_capAtendimentoFechamento_capAditivoProfSaude1_idx` (`isInstSaude` ASC, `laNome` ASC, `esEspecialidade` ASC, `ocNrContrato` ASC, `clCliente` ASC, `ceTipoPagamento` ASC, `opNrPlantao` ASC, `psCPF` ASC, `adDataSolicitacao` ASC) VISIBLE,
  CONSTRAINT `fk_capAtendimentoFechamento_capAditivoProfSaude1`
    FOREIGN KEY (`isInstSaude` , `laNome` , `esEspecialidade` , `ocNrContrato` , `clCliente` , `ceTipoPagamento` , `opNrPlantao` , `psCPF` , `adDataSolicitacao`)
    REFERENCES `capAditivoProfSaude` (`isInstSaude` , `laNome` , `esEspecialidade` , `ocNrContrato` , `clCliente` , `ceTipoPagamento` , `opNrPlantao` , `psCPF` , `adDataSolicitacao`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO capAtendimentoFechamento (isInstSaude, laNome, esEspecialidade, ocNrContrato, clCliente, ceTipoPagamento, opNrPlantao, psCPF, adDataSolicitacao, afNome, afIni, afFim, codFechamento, afValor, anNrAntecipacao, adSituacao, afUsuarioAprovacao, afDataAprovacao)
SELECT isInstSaude, laNome, esEspecialidade, ocNrContrato, clCliente, ceTipoPagamento, opNrPlantao, psCPF, adDataSolicitacao, afNome, afIni, afFim, codFechamento, afValor, anNrAntecipacao, adSituacao, afUsuarioAprovacao, afDataAprovacao
FROM capAtendimentoFechamento_bkp;

CREATE TABLE capAtendimentoFechaAditivo SELECT * FROM capAtendimentoFechaAditivo_bkp;
DROP TABLE capAtendimentoFechaAditivo_bkp;