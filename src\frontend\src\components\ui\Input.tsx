// =====================================================
// INPUT COMPONENT - COMPONENTE DE INPUT
// Componente de input reutilizável e customizável
// =====================================================

import React from 'react'
import { motion } from 'framer-motion'
import { Eye, EyeOff, AlertCircle } from 'lucide-react'
import { cn } from '@/utils'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  hint?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  variant?: 'default' | 'filled' | 'outline'
  inputSize?: 'sm' | 'md' | 'lg'
  fullWidth?: boolean
  showPasswordToggle?: boolean
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({
    className,
    type = 'text',
    label,
    error,
    hint,
    leftIcon,
    rightIcon,
    variant = 'default',
    inputSize = 'md',
    fullWidth = true,
    showPasswordToggle = false,
    disabled,
    required,
    ...props
  }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false)
    const [isFocused, setIsFocused] = React.useState(false)
    
    const inputType = type === 'password' && showPassword ? 'text' : type
    const hasError = !!error
    const hasLeftIcon = !!leftIcon
    const hasRightIcon = !!rightIcon || (type === 'password' && showPasswordToggle) || hasError

    const baseClasses = [
      'block w-full transition-all duration-200',
      'placeholder:text-secondary-400 dark:placeholder:text-secondary-500',
      'focus:outline-none focus:ring-2 focus:ring-offset-0',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'disabled:bg-secondary-50 dark:disabled:bg-secondary-800'
    ]

    const variantClasses = {
      default: [
        'border border-secondary-300 bg-white text-secondary-900',
        'focus:border-primary-500 focus:ring-primary-500/20',
        'dark:border-secondary-600 dark:bg-secondary-900 dark:text-secondary-100',
        'dark:focus:border-primary-400'
      ],
      filled: [
        'border-0 bg-secondary-100 text-secondary-900',
        'focus:bg-white focus:ring-primary-500/20',
        'dark:bg-secondary-800 dark:text-secondary-100',
        'dark:focus:bg-secondary-700'
      ],
      outline: [
        'border-2 border-secondary-200 bg-transparent text-secondary-900',
        'focus:border-primary-500 focus:ring-primary-500/10',
        'dark:border-secondary-700 dark:text-secondary-100',
        'dark:focus:border-primary-400'
      ]
    }

    const sizeClasses = {
      sm: 'px-3 py-2 text-sm rounded-md',
      md: 'px-4 py-2.5 text-sm rounded-lg',
      lg: 'px-5 py-3 text-base rounded-lg'
    }

    const errorClasses = hasError ? [
      'border-error-300 focus:border-error-500 focus:ring-error-500/20',
      'dark:border-error-600 dark:focus:border-error-400'
    ] : []

    const paddingClasses = {
      left: hasLeftIcon ? {
        sm: 'pl-10',
        md: 'pl-11',
        lg: 'pl-12'
      }[inputSize] : '',
      right: hasRightIcon ? {
        sm: 'pr-10',
        md: 'pr-11',
        lg: 'pr-12'
      }[inputSize] : ''
    }

    const iconSizeClasses = {
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6'
    }

    const iconPositionClasses = {
      sm: 'top-2.5',
      md: 'top-3',
      lg: 'top-3.5'
    }

    const inputClasses = cn(
      baseClasses,
      variantClasses[variant],
      sizeClasses[inputSize],
      errorClasses,
      paddingClasses.left,
      paddingClasses.right,
      !fullWidth && 'w-auto',
      className
    )

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword)
    }

    return (
      <div className={cn('relative', fullWidth ? 'w-full' : 'w-auto')}>
        {label && (
          <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
            {label}
            {required && <span className="text-error-500 ml-1">*</span>}
          </label>
        )}
        
        <div className="relative">
          {/* Left Icon */}
          {leftIcon && (
            <div className={cn(
              'absolute left-3 flex items-center pointer-events-none',
              iconPositionClasses[inputSize]
            )}>
              <span className={cn(
                iconSizeClasses[inputSize],
                'text-secondary-400 dark:text-secondary-500'
              )}>
                {leftIcon}
              </span>
            </div>
          )}

          {/* Input */}
          <motion.input
            ref={ref}
            type={inputType}
            className={inputClasses}
            disabled={disabled}
            required={required}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            animate={{
              scale: isFocused && !disabled ? 1.01 : 1,
            }}
            transition={{ duration: 0.1 }}
            {...props}
          />

          {/* Right Icons */}
          {hasRightIcon && (
            <div className={cn(
              'absolute right-3 flex items-center space-x-1',
              iconPositionClasses[inputSize]
            )}>
              {/* Error Icon */}
              {hasError && (
                <AlertCircle className={cn(
                  iconSizeClasses[inputSize],
                  'text-error-500'
                )} />
              )}

              {/* Password Toggle */}
              {type === 'password' && showPasswordToggle && (
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className={cn(
                    'text-secondary-400 hover:text-secondary-600',
                    'dark:text-secondary-500 dark:hover:text-secondary-300',
                    'transition-colors duration-200'
                  )}
                  tabIndex={-1}
                >
                  {showPassword ? (
                    <EyeOff className={iconSizeClasses[inputSize]} />
                  ) : (
                    <Eye className={iconSizeClasses[inputSize]} />
                  )}
                </button>
              )}

              {/* Custom Right Icon */}
              {rightIcon && !hasError && (
                <span className={cn(
                  iconSizeClasses[inputSize],
                  'text-secondary-400 dark:text-secondary-500'
                )}>
                  {rightIcon}
                </span>
              )}
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <motion.p
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-1 text-sm text-error-600 dark:text-error-400"
          >
            {error}
          </motion.p>
        )}

        {/* Hint */}
        {hint && !error && (
          <p className="mt-1 text-sm text-secondary-500 dark:text-secondary-400">
            {hint}
          </p>
        )}
      </div>
    )
  }
)

Input.displayName = 'Input'

export { Input }
export type { InputProps }
