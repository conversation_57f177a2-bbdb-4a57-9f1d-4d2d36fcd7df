// =====================================================
// USERS PAGE - PÁGINA DE USUÁRIOS
// Gerenciamento de usuários do sistema
// =====================================================

import React from 'react'
import { useTranslation } from 'react-i18next'
import { useQuery } from '@tanstack/react-query'
import { motion } from 'framer-motion'
import { Plus, Users as UsersIcon, Search, Filter } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Table } from '@/components/ui/Table'
import { SectionLoading } from '@/components/ui/LoadingSpinner'
import { modernApi } from '@/services/modernApi'
import { formatDate, getStatusColor } from '@/utils'
import { useUsers } from '@/hooks/useApi'
import type { User, TableColumn } from '@/types'

export function Users() {
  const { t } = useTranslation()
  const [searchTerm, setSearchTerm] = React.useState('')
  const [page, setPage] = React.useState(1)
  const [limit, setLimit] = React.useState(25)

  // Usar o hook personalizado de usuários
  const {
    users: usersData,
    isLoading,
    createUser,
    updateUser,
    isCreating,
    isUpdating
  } = useUsers({
    limit,
    offset: (page - 1) * limit,
    search: searchTerm
  })

  // Colunas da tabela
  const columns: TableColumn<User>[] = [
    {
      key: 'name',
      label: t('users.name'),
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium text-heading">{value}</div>
          <div className="text-sm text-muted">{row.email}</div>
        </div>
      )
    },
    {
      key: 'cpf',
      label: t('users.cpf'),
      render: (value) => (
        <span className="font-mono text-sm">{value}</span>
      )
    },
    {
      key: 'groups',
      label: t('users.groups'),
      render: (value: User['groups']) => (
        <div className="flex flex-wrap gap-1">
          {value?.map((group) => (
            <span
              key={group.id}
              className="px-2 py-1 text-xs font-medium bg-primary-100 text-primary-700 rounded-full dark:bg-primary-900/20 dark:text-primary-300"
            >
              {group.name}
            </span>
          ))}
        </div>
      )
    },
    {
      key: 'active',
      label: t('users.status'),
      render: (value) => (
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(value ? 'ativo' : 'inativo')}`}>
          {value ? t('common.active') : t('common.inactive')}
        </span>
      )
    },
    {
      key: 'created_at',
      label: t('users.createdAt'),
      render: (value) => (
        <span className="text-sm text-muted">
          {formatDate(value)}
        </span>
      )
    },
    {
      key: 'actions',
      label: t('common.actions'),
      render: (_, row) => (
        <div className="flex items-center space-x-2">
          <Button size="sm" variant="outline">
            {t('common.edit')}
          </Button>
          <Button size="sm" variant="ghost">
            {t('common.view')}
          </Button>
        </div>
      )
    }
  ]

  const users = usersData?.items || []
  const total = usersData?.pagination?.total || 0
  const pages = Math.ceil(total / limit)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-heading flex items-center space-x-3">
            <UsersIcon className="w-8 h-8 text-primary-600" />
            <span>{t('users.title')}</span>
          </h1>
          <p className="text-muted mt-1">
            {t('users.subtitle')}
          </p>
        </div>
        
        <Button
          variant="primary"
          icon={<Plus className="w-5 h-5" />}
        >
          {t('users.createUser')}
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1 max-w-md">
              <Input
                type="text"
                placeholder={t('users.searchUsers')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                leftIcon={<Search className="w-4 h-4" />}
                size="sm"
              />
            </div>
            
            <Button variant="outline" size="sm" icon={<Filter className="w-4 h-4" />}>
              Filtros
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        {isLoading ? (
          <Card>
            <CardContent className="p-6">
              <SectionLoading />
            </CardContent>
          </Card>
        ) : (
          <Table
            data={users}
            columns={columns}
            loading={isLoading}
            pagination={{
              page,
              limit,
              total,
              pages,
              onPageChange: setPage,
              onLimitChange: setLimit
            }}
          />
        )}
      </motion.div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium text-muted">
                Total de Usuários
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-heading">
                {total}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium text-muted">
                Usuários Ativos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-success-600">
                {users.filter(u => u.active).length}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium text-muted">
                Usuários Inativos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-error-600">
                {users.filter(u => !u.active).length}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
