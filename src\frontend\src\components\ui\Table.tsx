// =====================================================
// TABLE COMPONENT - COMPONENTE DE TABELA
// Componente de tabela reutilizável e responsiva
// =====================================================

import React from 'react'
import { motion } from 'framer-motion'
import { ChevronUp, ChevronDown, MoreHorizontal, Search } from 'lucide-react'
import { cn } from '@/utils'
import { Button } from './Button'
import { Input } from './Input'
import { LoadingSpinner, TableLoading } from './LoadingSpinner'
import type { TableColumn, TableProps } from '@/types'

export function Table<T = any>({
  data,
  columns,
  loading = false,
  pagination,
  onSort,
  onRowClick,
  className
}: TableProps<T> & { className?: string }) {
  const [sortColumn, setSortColumn] = React.useState<string | null>(null)
  const [sortDirection, setSortDirection] = React.useState<'asc' | 'desc'>('asc')
  const [searchTerm, setSearchTerm] = React.useState('')

  // Filtrar dados baseado na busca
  const filteredData = React.useMemo(() => {
    if (!searchTerm) return data

    return data.filter(item =>
      columns.some(column => {
        const value = item[column.key as keyof T]
        return String(value).toLowerCase().includes(searchTerm.toLowerCase())
      })
    )
  }, [data, searchTerm, columns])

  const handleSort = (column: TableColumn<T>) => {
    if (!column.sortable) return

    const columnKey = String(column.key)
    let newDirection: 'asc' | 'desc' = 'asc'

    if (sortColumn === columnKey && sortDirection === 'asc') {
      newDirection = 'desc'
    }

    setSortColumn(columnKey)
    setSortDirection(newDirection)
    onSort?.(columnKey, newDirection)
  }

  const handleRowClick = (row: T, index: number) => {
    onRowClick?.(row)
  }

  if (loading) {
    return (
      <div className={cn('bg-white dark:bg-secondary-900 rounded-lg border border-secondary-200 dark:border-secondary-700', className)}>
        <div className="p-6">
          <TableLoading rows={5} columns={columns.length} />
        </div>
      </div>
    )
  }

  return (
    <div className={cn('bg-white dark:bg-secondary-900 rounded-lg border border-secondary-200 dark:border-secondary-700', className)}>
      {/* Header com busca */}
      <div className="p-4 border-b border-secondary-200 dark:border-secondary-700">
        <div className="flex items-center justify-between">
          <div className="flex-1 max-w-sm">
            <Input
              type="text"
              placeholder="Buscar..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<Search className="w-4 h-4" />}
              size="sm"
            />
          </div>
          
          {pagination && (
            <div className="flex items-center space-x-2 text-sm text-muted">
              <span>
                {((pagination.page - 1) * pagination.limit) + 1}-{Math.min(pagination.page * pagination.limit, pagination.total)} de {pagination.total}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Tabela */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Header */}
          <thead className="bg-secondary-50 dark:bg-secondary-800">
            <tr>
              {columns.map((column, index) => (
                <th
                  key={String(column.key)}
                  className={cn(
                    'px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider',
                    column.sortable && 'cursor-pointer hover:bg-secondary-100 dark:hover:bg-secondary-700 transition-colors',
                    column.align === 'center' && 'text-center',
                    column.align === 'right' && 'text-right'
                  )}
                  style={{ width: column.width }}
                  onClick={() => handleSort(column)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.label}</span>
                    {column.sortable && (
                      <div className="flex flex-col">
                        <ChevronUp 
                          className={cn(
                            'w-3 h-3 -mb-1',
                            sortColumn === String(column.key) && sortDirection === 'asc'
                              ? 'text-primary-600'
                              : 'text-secondary-400'
                          )} 
                        />
                        <ChevronDown 
                          className={cn(
                            'w-3 h-3',
                            sortColumn === String(column.key) && sortDirection === 'desc'
                              ? 'text-primary-600'
                              : 'text-secondary-400'
                          )} 
                        />
                      </div>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>

          {/* Body */}
          <tbody className="bg-white dark:bg-secondary-900 divide-y divide-secondary-200 dark:divide-secondary-700">
            {filteredData.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-12 text-center">
                  <div className="text-secondary-500 dark:text-secondary-400">
                    <p className="text-lg font-medium mb-2">Nenhum resultado encontrado</p>
                    <p className="text-sm">Tente ajustar os filtros ou termos de busca</p>
                  </div>
                </td>
              </tr>
            ) : (
              filteredData.map((row, rowIndex) => (
                <motion.tr
                  key={rowIndex}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: rowIndex * 0.05 }}
                  className={cn(
                    'hover:bg-secondary-50 dark:hover:bg-secondary-800 transition-colors',
                    onRowClick && 'cursor-pointer'
                  )}
                  onClick={() => handleRowClick(row, rowIndex)}
                >
                  {columns.map((column, colIndex) => {
                    const value = row[column.key as keyof T]
                    const cellContent = column.render ? column.render(value, row) : String(value || '')

                    return (
                      <td
                        key={String(column.key)}
                        className={cn(
                          'px-6 py-4 whitespace-nowrap text-sm',
                          column.align === 'center' && 'text-center',
                          column.align === 'right' && 'text-right'
                        )}
                      >
                        {cellContent}
                      </td>
                    )
                  })}
                </motion.tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Paginação */}
      {pagination && pagination.pages > 1 && (
        <div className="px-6 py-4 border-t border-secondary-200 dark:border-secondary-700">
          <TablePagination {...pagination} />
        </div>
      )}
    </div>
  )
}

// Componente de paginação
interface PaginationProps {
  page: number
  limit: number
  total: number
  pages: number
  onPageChange: (page: number) => void
  onLimitChange: (limit: number) => void
}

function TablePagination({
  page,
  limit,
  total,
  pages,
  onPageChange,
  onLimitChange
}: PaginationProps) {
  const getVisiblePages = () => {
    const delta = 2
    const range = []
    const rangeWithDots = []

    for (let i = Math.max(2, page - delta); i <= Math.min(pages - 1, page + delta); i++) {
      range.push(i)
    }

    if (page - delta > 2) {
      rangeWithDots.push(1, '...')
    } else {
      rangeWithDots.push(1)
    }

    rangeWithDots.push(...range)

    if (page + delta < pages - 1) {
      rangeWithDots.push('...', pages)
    } else if (pages > 1) {
      rangeWithDots.push(pages)
    }

    return rangeWithDots
  }

  return (
    <div className="flex items-center justify-between">
      {/* Items per page */}
      <div className="flex items-center space-x-2">
        <span className="text-sm text-muted">Itens por página:</span>
        <select
          value={limit}
          onChange={(e) => onLimitChange(Number(e.target.value))}
          className="text-sm border border-secondary-300 dark:border-secondary-600 rounded px-2 py-1 bg-white dark:bg-secondary-800"
        >
          <option value={10}>10</option>
          <option value={25}>25</option>
          <option value={50}>50</option>
          <option value={100}>100</option>
        </select>
      </div>

      {/* Pagination buttons */}
      <div className="flex items-center space-x-1">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(page - 1)}
          disabled={page <= 1}
        >
          Anterior
        </Button>

        {getVisiblePages().map((pageNum, index) => (
          <React.Fragment key={index}>
            {pageNum === '...' ? (
              <span className="px-3 py-1 text-sm text-muted">...</span>
            ) : (
              <Button
                variant={pageNum === page ? 'primary' : 'outline'}
                size="sm"
                onClick={() => onPageChange(pageNum as number)}
              >
                {pageNum}
              </Button>
            )}
          </React.Fragment>
        ))}

        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(page + 1)}
          disabled={page >= pages}
        >
          Próxima
        </Button>
      </div>
    </div>
  )
}
