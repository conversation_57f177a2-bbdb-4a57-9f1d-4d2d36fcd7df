import{r as s,R as ge}from"./vendor-CBH9K-97.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function T(){return T=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},T.apply(this,arguments)}var S;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(S||(S={}));const H="popstate";function ye(e){e===void 0&&(e={});function t(r,a){let{pathname:l,search:i,hash:u}=r.location;return A("",{pathname:l,search:i,hash:u},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:k(a)}return Ce(t,n,null,e)}function x(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function re(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function xe(){return Math.random().toString(36).substr(2,8)}function Q(e,t){return{usr:e.state,key:e.key,idx:t}}function A(e,t,n,r){return n===void 0&&(n=null),T({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?U(t):t,{state:n,key:t&&t.key||r||xe()})}function k(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function U(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Ce(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:l=!1}=r,i=a.history,u=S.Pop,o=null,f=h();f==null&&(f=0,i.replaceState(T({},i.state,{idx:f}),""));function h(){return(i.state||{idx:null}).idx}function c(){u=S.Pop;let d=h(),C=d==null?null:d-f;f=d,o&&o({action:u,location:m.location,delta:C})}function p(d,C){u=S.Push;let v=A(m.location,d,C);f=h()+1;let y=Q(v,f),E=m.createHref(v);try{i.pushState(y,"",E)}catch(b){if(b instanceof DOMException&&b.name==="DataCloneError")throw b;a.location.assign(E)}l&&o&&o({action:u,location:m.location,delta:1})}function P(d,C){u=S.Replace;let v=A(m.location,d,C);f=h();let y=Q(v,f),E=m.createHref(v);i.replaceState(y,"",E),l&&o&&o({action:u,location:m.location,delta:0})}function g(d){let C=a.location.origin!=="null"?a.location.origin:a.location.href,v=typeof d=="string"?d:k(d);return v=v.replace(/ $/,"%20"),x(C,"No window.location.(origin|href) available to create URL for href: "+v),new URL(v,C)}let m={get action(){return u},get location(){return e(a,i)},listen(d){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(H,c),o=d,()=>{a.removeEventListener(H,c),o=null}},createHref(d){return t(a,d)},createURL:g,encodeLocation(d){let C=g(d);return{pathname:C.pathname,search:C.search,hash:C.hash}},push:p,replace:P,go(d){return i.go(d)}};return m}var Y;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Y||(Y={}));function Ee(e,t,n){return n===void 0&&(n="/"),Pe(e,t,n)}function Pe(e,t,n,r){let a=typeof t=="string"?U(t):t,l=O(a.pathname||"/",n);if(l==null)return null;let i=ae(e);we(i);let u=null;for(let o=0;u==null&&o<i.length;++o){let f=ke(l);u=Te(i[o],f)}return u}function ae(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(l,i,u)=>{let o={relativePath:u===void 0?l.path||"":u,caseSensitive:l.caseSensitive===!0,childrenIndex:i,route:l};o.relativePath.startsWith("/")&&(x(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(r.length));let f=L([r,o.relativePath]),h=n.concat(o);l.children&&l.children.length>0&&(x(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+f+'".')),ae(l.children,t,h,f)),!(l.path==null&&!l.index)&&t.push({path:f,score:Ne(f,l.index),routesMeta:h})};return e.forEach((l,i)=>{var u;if(l.path===""||!((u=l.path)!=null&&u.includes("?")))a(l,i);else for(let o of le(l.path))a(l,i,o)}),t}function le(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(r.length===0)return a?[l,""]:[l];let i=le(r.join("/")),u=[];return u.push(...i.map(o=>o===""?l:[l,o].join("/"))),a&&u.push(...i),u.map(o=>e.startsWith("/")&&o===""?"/":o)}function we(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Be(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Re=/^:[\w-]+$/,be=3,Se=2,Le=1,Oe=10,Ue=-2,Z=e=>e==="*";function Ne(e,t){let n=e.split("/"),r=n.length;return n.some(Z)&&(r+=Ue),t&&(r+=Se),n.filter(a=>!Z(a)).reduce((a,l)=>a+(Re.test(l)?be:l===""?Le:Oe),r)}function Be(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function Te(e,t,n){let{routesMeta:r}=e,a={},l="/",i=[];for(let u=0;u<r.length;++u){let o=r[u],f=u===r.length-1,h=l==="/"?t:t.slice(l.length)||"/",c=J({path:o.relativePath,caseSensitive:o.caseSensitive,end:f},h),p=o.route;if(!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:L([l,c.pathname]),pathnameBase:_e(L([l,c.pathnameBase])),route:p}),c.pathnameBase!=="/"&&(l=L([l,c.pathnameBase]))}return i}function J(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Ie(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),u=a.slice(1);return{params:r.reduce((f,h,c)=>{let{paramName:p,isOptional:P}=h;if(p==="*"){let m=u[c]||"";i=l.slice(0,l.length-m.length).replace(/(.)\/+$/,"$1")}const g=u[c];return P&&!g?f[p]=void 0:f[p]=(g||"").replace(/%2F/g,"/"),f},{}),pathname:l,pathnameBase:i,pattern:e}}function Ie(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),re(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,u,o)=>(r.push({paramName:u,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function ke(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return re(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function O(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function je(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?U(e):e;return{pathname:n?n.startsWith("/")?n:We(n,t):t,search:Fe(r),hash:Me(a)}}function We(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function D(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function $e(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function q(e,t){let n=$e(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function G(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=U(e):(a=T({},e),x(!a.pathname||!a.pathname.includes("?"),D("?","pathname","search",a)),x(!a.pathname||!a.pathname.includes("#"),D("#","pathname","hash",a)),x(!a.search||!a.search.includes("#"),D("#","search","hash",a)));let l=e===""||a.pathname==="",i=l?"/":a.pathname,u;if(i==null)u=n;else{let c=t.length-1;if(!r&&i.startsWith("..")){let p=i.split("/");for(;p[0]==="..";)p.shift(),c-=1;a.pathname=p.join("/")}u=c>=0?t[c]:"/"}let o=je(a,u),f=i&&i!=="/"&&i.endsWith("/"),h=(l||i===".")&&n.endsWith("/");return!o.pathname.endsWith("/")&&(f||h)&&(o.pathname+="/"),o}const L=e=>e.join("/").replace(/\/\/+/g,"/"),_e=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Fe=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Me=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Ve(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const ie=["post","put","patch","delete"];new Set(ie);const De=["get",...ie];new Set(De);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function I(){return I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},I.apply(this,arguments)}const W=s.createContext(null),oe=s.createContext(null),w=s.createContext(null),$=s.createContext(null),R=s.createContext({outlet:null,matches:[],isDataRoute:!1}),se=s.createContext(null);function Ae(e,t){let{relative:n}=t===void 0?{}:t;N()||x(!1);let{basename:r,navigator:a}=s.useContext(w),{hash:l,pathname:i,search:u}=_(e,{relative:n}),o=i;return r!=="/"&&(o=i==="/"?r:L([r,i])),a.createHref({pathname:o,search:u,hash:l})}function N(){return s.useContext($)!=null}function B(){return N()||x(!1),s.useContext($).location}function ue(e){s.useContext(w).static||s.useLayoutEffect(e)}function ce(){let{isDataRoute:e}=s.useContext(R);return e?at():Je()}function Je(){N()||x(!1);let e=s.useContext(W),{basename:t,future:n,navigator:r}=s.useContext(w),{matches:a}=s.useContext(R),{pathname:l}=B(),i=JSON.stringify(q(a,n.v7_relativeSplatPath)),u=s.useRef(!1);return ue(()=>{u.current=!0}),s.useCallback(function(f,h){if(h===void 0&&(h={}),!u.current)return;if(typeof f=="number"){r.go(f);return}let c=G(f,JSON.parse(i),l,h.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:L([t,c.pathname])),(h.replace?r.replace:r.push)(c,h.state,h)},[t,r,i,l,e])}const ze=s.createContext(null);function Ke(e){let t=s.useContext(R).outlet;return t&&s.createElement(ze.Provider,{value:e},t)}function _(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=s.useContext(w),{matches:a}=s.useContext(R),{pathname:l}=B(),i=JSON.stringify(q(a,r.v7_relativeSplatPath));return s.useMemo(()=>G(e,JSON.parse(i),l,n==="path"),[e,i,l,n])}function qe(e,t){return Ge(e,t)}function Ge(e,t,n,r){N()||x(!1);let{navigator:a}=s.useContext(w),{matches:l}=s.useContext(R),i=l[l.length-1],u=i?i.params:{};i&&i.pathname;let o=i?i.pathnameBase:"/";i&&i.route;let f=B(),h;if(t){var c;let d=typeof t=="string"?U(t):t;o==="/"||(c=d.pathname)!=null&&c.startsWith(o)||x(!1),h=d}else h=f;let p=h.pathname||"/",P=p;if(o!=="/"){let d=o.replace(/^\//,"").split("/");P="/"+p.replace(/^\//,"").split("/").slice(d.length).join("/")}let g=Ee(e,{pathname:P}),m=Ze(g&&g.map(d=>Object.assign({},d,{params:Object.assign({},u,d.params),pathname:L([o,a.encodeLocation?a.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?o:L([o,a.encodeLocation?a.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),l,n,r);return t&&m?s.createElement($.Provider,{value:{location:I({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:S.Pop}},m):m}function Xe(){let e=rt(),t=Ve(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return s.createElement(s.Fragment,null,s.createElement("h2",null,"Unexpected Application Error!"),s.createElement("h3",{style:{fontStyle:"italic"}},t),n?s.createElement("pre",{style:a},n):null,null)}const He=s.createElement(Xe,null);class Qe extends s.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?s.createElement(R.Provider,{value:this.props.routeContext},s.createElement(se.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Ye(e){let{routeContext:t,match:n,children:r}=e,a=s.useContext(W);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),s.createElement(R.Provider,{value:t},r)}function Ze(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var l;if(!n)return null;if(n.errors)e=n.matches;else if((l=r)!=null&&l.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,u=(a=n)==null?void 0:a.errors;if(u!=null){let h=i.findIndex(c=>c.route.id&&(u==null?void 0:u[c.route.id])!==void 0);h>=0||x(!1),i=i.slice(0,Math.min(i.length,h+1))}let o=!1,f=-1;if(n&&r&&r.v7_partialHydration)for(let h=0;h<i.length;h++){let c=i[h];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(f=h),c.route.id){let{loaderData:p,errors:P}=n,g=c.route.loader&&p[c.route.id]===void 0&&(!P||P[c.route.id]===void 0);if(c.route.lazy||g){o=!0,f>=0?i=i.slice(0,f+1):i=[i[0]];break}}}return i.reduceRight((h,c,p)=>{let P,g=!1,m=null,d=null;n&&(P=u&&c.route.id?u[c.route.id]:void 0,m=c.route.errorElement||He,o&&(f<0&&p===0?(lt("route-fallback"),g=!0,d=null):f===p&&(g=!0,d=c.route.hydrateFallbackElement||null)));let C=t.concat(i.slice(0,p+1)),v=()=>{let y;return P?y=m:g?y=d:c.route.Component?y=s.createElement(c.route.Component,null):c.route.element?y=c.route.element:y=h,s.createElement(Ye,{match:c,routeContext:{outlet:h,matches:C,isDataRoute:n!=null},children:y})};return n&&(c.route.ErrorBoundary||c.route.errorElement||p===0)?s.createElement(Qe,{location:n.location,revalidation:n.revalidation,component:m,error:P,children:v(),routeContext:{outlet:null,matches:C,isDataRoute:!0}}):v()},null)}var fe=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(fe||{}),he=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(he||{});function et(e){let t=s.useContext(W);return t||x(!1),t}function tt(e){let t=s.useContext(oe);return t||x(!1),t}function nt(e){let t=s.useContext(R);return t||x(!1),t}function de(e){let t=nt(),n=t.matches[t.matches.length-1];return n.route.id||x(!1),n.route.id}function rt(){var e;let t=s.useContext(se),n=tt(),r=de();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function at(){let{router:e}=et(fe.UseNavigateStable),t=de(he.UseNavigateStable),n=s.useRef(!1);return ue(()=>{n.current=!0}),s.useCallback(function(a,l){l===void 0&&(l={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,I({fromRouteId:t},l)))},[e,t])}const ee={};function lt(e,t,n){ee[e]||(ee[e]=!0)}function it(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function wt(e){let{to:t,replace:n,state:r,relative:a}=e;N()||x(!1);let{future:l,static:i}=s.useContext(w),{matches:u}=s.useContext(R),{pathname:o}=B(),f=ce(),h=G(t,q(u,l.v7_relativeSplatPath),o,a==="path"),c=JSON.stringify(h);return s.useEffect(()=>f(JSON.parse(c),{replace:n,state:r,relative:a}),[f,c,a,n,r]),null}function Rt(e){return Ke(e.context)}function ot(e){x(!1)}function st(e){let{basename:t="/",children:n=null,location:r,navigationType:a=S.Pop,navigator:l,static:i=!1,future:u}=e;N()&&x(!1);let o=t.replace(/^\/*/,"/"),f=s.useMemo(()=>({basename:o,navigator:l,static:i,future:I({v7_relativeSplatPath:!1},u)}),[o,u,l,i]);typeof r=="string"&&(r=U(r));let{pathname:h="/",search:c="",hash:p="",state:P=null,key:g="default"}=r,m=s.useMemo(()=>{let d=O(h,o);return d==null?null:{location:{pathname:d,search:c,hash:p,state:P,key:g},navigationType:a}},[o,h,c,p,P,g,a]);return m==null?null:s.createElement(w.Provider,{value:f},s.createElement($.Provider,{children:n,value:m}))}function bt(e){let{children:t,location:n}=e;return qe(z(t),n)}new Promise(()=>{});function z(e,t){t===void 0&&(t=[]);let n=[];return s.Children.forEach(e,(r,a)=>{if(!s.isValidElement(r))return;let l=[...t,a];if(r.type===s.Fragment){n.push.apply(n,z(r.props.children,l));return}r.type!==ot&&x(!1),!r.props.index||!r.props.children||x(!1);let i={id:r.props.id||l.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=z(r.props.children,l)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function j(){return j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},j.apply(this,arguments)}function pe(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,l;for(l=0;l<r.length;l++)a=r[l],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function ut(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function ct(e,t){return e.button===0&&(!t||t==="_self")&&!ut(e)}const ft=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],ht=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],dt="6";try{window.__reactRouterVersion=dt}catch{}const pt=s.createContext({isTransitioning:!1}),mt="startTransition",te=ge[mt];function St(e){let{basename:t,children:n,future:r,window:a}=e,l=s.useRef();l.current==null&&(l.current=ye({window:a,v5Compat:!0}));let i=l.current,[u,o]=s.useState({action:i.action,location:i.location}),{v7_startTransition:f}=r||{},h=s.useCallback(c=>{f&&te?te(()=>o(c)):o(c)},[o,f]);return s.useLayoutEffect(()=>i.listen(h),[i,h]),s.useEffect(()=>it(r),[r]),s.createElement(st,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:i,future:r})}const vt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",gt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,yt=s.forwardRef(function(t,n){let{onClick:r,relative:a,reloadDocument:l,replace:i,state:u,target:o,to:f,preventScrollReset:h,viewTransition:c}=t,p=pe(t,ft),{basename:P}=s.useContext(w),g,m=!1;if(typeof f=="string"&&gt.test(f)&&(g=f,vt))try{let y=new URL(window.location.href),E=f.startsWith("//")?new URL(y.protocol+f):new URL(f),b=O(E.pathname,P);E.origin===y.origin&&b!=null?f=b+E.search+E.hash:m=!0}catch{}let d=Ae(f,{relative:a}),C=Ct(f,{replace:i,state:u,target:o,preventScrollReset:h,relative:a,viewTransition:c});function v(y){r&&r(y),y.defaultPrevented||C(y)}return s.createElement("a",j({},p,{href:g||d,onClick:m||l?r:v,ref:n,target:o}))}),Lt=s.forwardRef(function(t,n){let{"aria-current":r="page",caseSensitive:a=!1,className:l="",end:i=!1,style:u,to:o,viewTransition:f,children:h}=t,c=pe(t,ht),p=_(o,{relative:c.relative}),P=B(),g=s.useContext(oe),{navigator:m,basename:d}=s.useContext(w),C=g!=null&&Et(p)&&f===!0,v=m.encodeLocation?m.encodeLocation(p).pathname:p.pathname,y=P.pathname,E=g&&g.navigation&&g.navigation.location?g.navigation.location.pathname:null;a||(y=y.toLowerCase(),E=E?E.toLowerCase():null,v=v.toLowerCase()),E&&d&&(E=O(E,d)||E);const b=v!=="/"&&v.endsWith("/")?v.length-1:v.length;let F=y===v||!i&&y.startsWith(v)&&y.charAt(b)==="/",X=E!=null&&(E===v||!i&&E.startsWith(v)&&E.charAt(v.length)==="/"),M={isActive:F,isPending:X,isTransitioning:C},me=F?r:void 0,V;typeof l=="function"?V=l(M):V=[l,F?"active":null,X?"pending":null,C?"transitioning":null].filter(Boolean).join(" ");let ve=typeof u=="function"?u(M):u;return s.createElement(yt,j({},c,{"aria-current":me,className:V,ref:n,style:ve,to:o,viewTransition:f}),typeof h=="function"?h(M):h)});var K;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(K||(K={}));var ne;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(ne||(ne={}));function xt(e){let t=s.useContext(W);return t||x(!1),t}function Ct(e,t){let{target:n,replace:r,state:a,preventScrollReset:l,relative:i,viewTransition:u}=t===void 0?{}:t,o=ce(),f=B(),h=_(e,{relative:i});return s.useCallback(c=>{if(ct(c,n)){c.preventDefault();let p=r!==void 0?r:k(f)===k(h);o(e,{replace:p,state:a,preventScrollReset:l,relative:i,viewTransition:u})}},[f,o,h,r,a,n,e,l,i,u])}function Et(e,t){t===void 0&&(t={});let n=s.useContext(pt);n==null&&x(!1);let{basename:r}=xt(K.useViewTransitionState),a=_(e,{relative:t.relative});if(!n.isTransitioning)return!1;let l=O(n.currentLocation.pathname,r)||n.currentLocation.pathname,i=O(n.nextLocation.pathname,r)||n.nextLocation.pathname;return J(a.pathname,i)!=null||J(a.pathname,l)!=null}export{St as B,yt as L,wt as N,Rt as O,bt as R,B as a,Lt as b,ot as c,ce as u};
//# sourceMappingURL=router-BQnfC52V.js.map
