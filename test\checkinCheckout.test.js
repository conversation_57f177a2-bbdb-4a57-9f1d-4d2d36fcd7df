import { describe, test, expect } from '@jest/globals';
import { calcularQtRealizadasEaprovadas } from '../src/layers/capfunctions/index.mjs';

describe('calcularQtRealizadasEaprovadas', () => {
    test('deve calcular qtRealizadas e qtAprovadas corretamente', () => {
        const ocCheckIn = '2023-10-01T08:00:00';
        const ocCheckOut = '2023-10-01T10:30:00';
        const ocQtGlosadas = '01:00:00';

        const result = calcularQtRealizadasEaprovadas(
            ocCheckIn,
            ocCheckOut,
            ocQtGlosadas
        );

        expect(result.qtRealizadas).toBe('02:30:00');
        expect(result.qtAprovadas).toBe('01:30:00');
    });

    test('deve lidar corretamente com glosadas zero', () => {
        const ocCheckIn = '2023-10-01 08:00:00';
        const ocCheckOut = '2023-10-01 10:30:00';
        const ocQtGlosadas = '00:00:00';

        const result = calcularQtRealizadasEaprovadas(
            ocCheckIn,
            ocCheckOut,
            ocQtGlosadas
        );

        expect(result.qtRealizadas).toBe('02:30:00');
        expect(result.qtAprovadas).toBe('02:30:00');
    });

    test('deve lidar corretamente com glosadas totais', () => {
        const ocCheckIn = '2023-10-01T08:00:00';
        const ocCheckOut = '2023-10-01T10:30:00';
        const ocQtGlosadas = '02:30:00';

        const result = calcularQtRealizadasEaprovadas(
            ocCheckIn,
            ocCheckOut,
            ocQtGlosadas
        );

        expect(result.qtRealizadas).toBe('02:30:00');
        expect(result.qtAprovadas).toBe('00:00:00');
    });

    test('deve lidar corretamente com horas quebradas', () => {
        const ocCheckIn = '2023-10-01 08:15:00';
        const ocCheckOut = '2023-10-01 10:45:00';
        const ocQtGlosadas = '00:30:00';

        const result = calcularQtRealizadasEaprovadas(
            ocCheckIn,
            ocCheckOut,
            ocQtGlosadas
        );

        expect(result.qtRealizadas).toBe('02:30:00');
        expect(result.qtAprovadas).toBe('02:00:00');
    });

    test('deve falhar quando ocCheckOut é antes de ocCheckIn', () => {
        const ocCheckIn = '2023-10-01T10:30:00';
        const ocCheckOut = '2023-10-01T08:00:00';
        const ocQtGlosadas = '00:30:00';

        expect(() => {
            calcularQtRealizadasEaprovadas(ocCheckIn, ocCheckOut, ocQtGlosadas);
        }).toThrow('ocCheckOut deve ser depois de ocCheckIn');
    });
});

describe('calcularQtAprovadas', () => {
    test('deve calcular qtAprovadas corretamente', () => {
        const qtRealizadas = '02:30:00';
        const ocQtGlosadas = '01:00:00';

        const result = calcularQtAprovadas(qtRealizadas, ocQtGlosadas);

        expect(result).toBe('01:30:00');
    });

    test('deve lidar corretamente com glosadas zero', () => {
        const qtRealizadas = '02:30:00';
        const ocQtGlosadas = '00:00:00';

        const result = calcularQtAprovadas(qtRealizadas, ocQtGlosadas);

        expect(result).toBe('02:30:00');
    });

    test('deve lidar corretamente com glosadas totais', () => {
        const qtRealizadas = '02:30:00';
        const ocQtGlosadas = '02:30:00';

        const result = calcularQtAprovadas(qtRealizadas, ocQtGlosadas);

        expect(result).toBe('00:00:00');
    });

    test('deve lidar corretamente com horas quebradas', () => {
        const qtRealizadas = '02:45:00';
        const ocQtGlosadas = '01:15:00';

        const result = calcularQtAprovadas(qtRealizadas, ocQtGlosadas);

        expect(result).toBe('01:30:00');
    });

    test('deve falhar quando ocQtGlosadas é maior que qtRealizadas', () => {
        const qtRealizadas = '01:00';
        const ocQtGlosadas = '01:30:00';

        expect(() => {
            calcularQtAprovadas(qtRealizadas, ocQtGlosadas);
        }).toThrow('ocQtGlosadas não pode ser maior que qtRealizadas');
    });
});
