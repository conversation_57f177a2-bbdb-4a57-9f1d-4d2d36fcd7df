import { describe, test, expect, beforeEach, jest } from '@jest/globals';

// Mock environment for testing
process.env.MYSQL_HOST = 'localhost';
process.env.MYSQL_USER = 'test';
process.env.MYSQL_PASSWORD = 'test';
process.env.MYSQL_PORT = '3306';

// Mock capfunctions
const mockBaseResponse = {
    ok: jest.fn((message, data) => ({ statusCode: 200, message, data })),
    error: jest.fn((message, data) => ({ statusCode: 500, message, data }))
};

jest.unstable_mockModule('capfunctions', () => ({
    baseResponse: mockBaseResponse
}));

describe('wpphook Backward Compatibility Tests', () => {
    let mockDatabase;
    let mockConnection;

    beforeEach(() => {
        jest.clearAllMocks();

        // Setup database mocks
        mockConnection = {
            connect: jest.fn((callback) => callback(null)),
            query: jest.fn(),
            end: jest.fn((callback) => callback())
        };

        jest.unstable_mockModule('mysql', () => ({
            createConnection: jest.fn(() => mockConnection)
        }));

        // Default successful query responses
        mockConnection.query.mockImplementation((query, params, callback) => {
            if (query.includes('SELECT 1 as health')) {
                callback(null, [{ health: 1 }]);
            } else if (query.includes('SELECT * FROM user_sessions')) {
                callback(null, []);
            } else if (query.includes('INSERT INTO user_sessions')) {
                callback(null, { insertId: 1 });
            } else {
                callback(null, []);
            }
        });
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('Real WhatsApp Webhook Payloads', () => {
        test('should handle Twilio WhatsApp webhook format', async () => {
            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            const twilioPayload = {
                body: JSON.stringify({
                    From: 'whatsapp:+*************',
                    To: 'whatsapp:+*************',
                    Body: 'GS2',
                    MessageSid: 'SM**********abcdef**********abcdef',
                    AccountSid: 'AC**********abcdef**********abcdef',
                    MessagingServiceSid: 'MG**********abcdef**********abcdef',
                    NumMedia: '0',
                    ProfileName: 'João Silva',
                    WaId: '*************'
                }),
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Twilio-Signature': 'signature'
                }
            };

            const result = await handler(twilioPayload, {});
            expect(result.statusCode).toBe(200);
        });

        test('should handle WhatsApp Business API webhook format', async () => {
            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            const whatsappBusinessPayload = {
                body: JSON.stringify({
                    object: 'whatsapp_business_account',
                    entry: [{
                        id: '***************',
                        changes: [{
                            value: {
                                messaging_product: 'whatsapp',
                                metadata: {
                                    display_phone_number: '*************',
                                    phone_number_id: '***************'
                                },
                                contacts: [{
                                    profile: {
                                        name: 'João Silva'
                                    },
                                    wa_id: '*************'
                                }],
                                messages: [{
                                    from: '*************',
                                    id: 'wamid.ID',
                                    timestamp: '**********',
                                    text: {
                                        body: 'GS2'
                                    },
                                    type: 'text'
                                }]
                            },
                            field: 'messages'
                        }]
                    }]
                })
            };

            const result = await handler(whatsappBusinessPayload, {});
            expect(result.statusCode).toBe(200);
        });

        test('should handle generic webhook format', async () => {
            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            const genericPayload = {
                body: JSON.stringify({
                    message: 'GS2',
                    from: '+*************',
                    phone: '*************',
                    timestamp: '2023-10-01T10:00:00Z',
                    messageId: 'msg_123',
                    chat: {
                        id: '<EMAIL>',
                        name: 'João Silva'
                    }
                })
            };

            const result = await handler(genericPayload, {});
            expect(result.statusCode).toBe(200);
        });

        test('should handle webhook with JSON string body', async () => {
            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            const event = {
                body: '{"message":"GS2","from":"+*************","timestamp":"2023-10-01T10:00:00Z"}'
            };

            const result = await handler(event, {});
            expect(result.statusCode).toBe(200);
        });

        test('should handle webhook with already parsed body', async () => {
            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            const event = {
                message: 'GS2',
                from: '+*************',
                timestamp: '2023-10-01T10:00:00Z'
            };

            const result = await handler(event, {});
            expect(result.statusCode).toBe(200);
        });

        test('should extract userId from various phone formats', async () => {
            const { parseWebhookPayload } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            const formats = [
                { from: '+*************', expected: '*************' },
                { from: '*************', expected: '*************' },
                { from: 'whatsapp:+*************', expected: '*************' },
                { phone: '+55 11 99999-9999', expected: '*************' },
                { From: '+55(11)99999-9999', expected: '*************' }
            ];

            formats.forEach(format => {
                const payload = {
                    message: 'test',
                    ...format
                };

                const result = parseWebhookPayload(payload);
                expect(result.userId).toBe(format.expected);
            });
        });
    });

    describe('Existing User Sessions Compatibility', () => {
        test('should handle legacy session data format', async () => {
            // Mock existing session with old format
            const legacySession = {
                id: '*************',
                phone_number: '+*************',
                current_state: 'GREETING',
                session_data: JSON.stringify({
                    userId: '*************',
                    phoneNumber: '+*************',
                    startTime: '2023-09-01T10:00:00Z',
                    // Legacy format might have different property names
                    user_data: {
                        full_name: 'João Silva',
                        user_cpf: '**********1',
                        user_email: '<EMAIL>'
                    }
                }),
                created_at: new Date('2023-09-01T10:00:00Z'),
                updated_at: new Date('2023-10-01T09:00:00Z') // Recent update
            };

            mockConnection.query.mockImplementation((query, params, callback) => {
                if (query.includes('SELECT * FROM user_sessions')) {
                    callback(null, [legacySession]);
                } else if (query.includes('INSERT INTO interaction_logs')) {
                    callback(null, { insertId: 1 });
                } else if (query.includes('UPDATE user_sessions')) {
                    callback(null, { affectedRows: 1 });
                } else {
                    callback(null, []);
                }
            });

            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            const event = {
                body: JSON.stringify({
                    message: '✅ Claro',
                    from: '+*************'
                })
            };

            const result = await handler(event, {});
            expect(result.statusCode).toBe(200);
        });

        test('should migrate session data to new format automatically', async () => {
            const oldFormatSession = {
                id: '*************',
                phone_number: '+*************',
                current_state: 'NAME_REQUEST',
                session_data: JSON.stringify({
                    // Old format with different naming
                    clinic_info: {
                        clinic_id: 1,
                        clinic_name: 'Hospital Test'
                    }
                }),
                updated_at: new Date()
            };

            mockConnection.query
                .mockImplementationOnce((query, params, callback) => {
                    // getUserSession call
                    callback(null, [oldFormatSession]);
                })
                .mockImplementationOnce((query, params, callback) => {
                    // logInteraction call
                    callback(null, { insertId: 1 });
                })
                .mockImplementationOnce((query, params, callback) => {
                    // updateUserSession call - verify it gets called with proper format
                    expect(query).toContain('UPDATE user_sessions');
                    callback(null, { affectedRows: 1 });
                })
                .mockImplementationOnce((query, params, callback) => {
                    // logInteraction call for response
                    callback(null, { insertId: 2 });
                });

            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            const event = {
                body: JSON.stringify({
                    message: 'João da Silva',
                    from: '+*************'
                })
            };

            const result = await handler(event, {});
            expect(result.statusCode).toBe(200);
        });

        test('should handle sessions with missing or corrupt session_data', async () => {
            const corruptSession = {
                id: '*************',
                phone_number: '+*************',
                current_state: 'GREETING',
                session_data: null, // Missing session data
                updated_at: new Date()
            };

            mockConnection.query.mockImplementation((query, params, callback) => {
                if (query.includes('SELECT * FROM user_sessions')) {
                    callback(null, [corruptSession]);
                } else {
                    callback(null, []);
                }
            });

            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            const event = {
                body: JSON.stringify({
                    message: '✅ Claro',
                    from: '+*************'
                })
            };

            const result = await handler(event, {});
            expect(result.statusCode).toBe(200);
        });
    });

    describe('Database Schema Compatibility', () => {
        test('should work with existing user_sessions table structure', async () => {
            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            // Simulate existing table structure response
            mockConnection.query.mockImplementation((query, params, callback) => {
                if (query.includes('SELECT * FROM user_sessions')) {
                    callback(null, [{
                        id: '*************',
                        phone_number: '+*************', 
                        current_state: 'INITIAL',
                        session_data: '{}',
                        created_at: new Date(),
                        updated_at: new Date()
                    }]);
                } else if (query.includes('UPDATE user_sessions')) {
                    callback(null, { affectedRows: 1 });
                } else {
                    callback(null, []);
                }
            });

            const event = {
                body: JSON.stringify({
                    message: 'GS2',
                    from: '+*************'
                })
            };

            const result = await handler(event, {});
            expect(result.statusCode).toBe(200);
        });

        test('should handle existing clinics table references', async () => {
            mockConnection.query.mockImplementation((query, params, callback) => {
                if (query.includes('SELECT * FROM clinics')) {
                    callback(null, [{
                        id: 1,
                        name: 'Hospital Teste',
                        razao_social: 'Hospital Teste Ltda',
                        cnpj: '12345678000199',
                        active: 1 // MySQL boolean as integer
                    }]);
                } else {
                    callback(null, []);
                }
            });

            const Database = (await import('../src/lambdas/gs2api/wpphook/database.mjs')).default;
            const db = new Database();
            await db.initialize();

            const clinics = await db.findClinic('Hospital Teste');
            expect(clinics).toHaveLength(1);
            expect(clinics[0].name).toBe('Hospital Teste');
        });

        test('should maintain data integrity in user_data operations', async () => {
            let savedData = null;

            mockConnection.query.mockImplementation((query, params, callback) => {
                if (query.includes('INSERT INTO user_data')) {
                    // Capture the data being saved
                    savedData = {
                        session_id: params[0],
                        clinic_id: params[1],
                        full_name: params[2],
                        cpf: params[3],
                        email: params[4],
                        terms_accepted: params[5],
                        terms_accepted_at: params[6]
                    };
                    callback(null, { insertId: 1 });
                } else {
                    callback(null, []);
                }
            });

            const Database = (await import('../src/lambdas/gs2api/wpphook/database.mjs')).default;
            const db = new Database();
            await db.initialize();

            const userData = {
                fullName: 'João da Silva',
                cpf: '**********1',
                email: '<EMAIL>',
                termsAccepted: true
            };

            await db.saveUserData('*************', 1, userData);

            expect(savedData).toMatchObject({
                session_id: '*************',
                clinic_id: 1,
                full_name: 'João da Silva',
                cpf: '**********1',
                email: '<EMAIL>',
                terms_accepted: true
            });
        });
    });

    describe('State Machine Backward Compatibility', () => {
        test('should handle all existing states correctly', async () => {
            const { STATES } = await import('../src/lambdas/gs2api/wpphook/services/stateMachine.mjs');

            // Verify all expected states exist
            const expectedStates = [
                'INITIAL', 'GREETING', 'CLINIC_REQUEST', 'CLINIC_VALIDATION',
                'CLINIC_CONFIRMATION', 'NAME_REQUEST', 'CPF_REQUEST', 'EMAIL_REQUEST',
                'DATA_CONFIRMATION', 'DATA_CORRECTION', 'TERMS_PRESENTATION',
                'FAQ_MENU', 'FAQ_RESPONSE', 'TERMS_ACCEPTANCE', 'COMPLETION', 'END'
            ];

            expectedStates.forEach(state => {
                expect(STATES[state]).toBeDefined();
                expect(typeof STATES[state]).toBe('string');
            });
        });

        test('should maintain existing state transition rules', async () => {
            const mockDatabase = {
                updateUserSession: jest.fn(),
                saveUserData: jest.fn(),
                findClinic: jest.fn(),
                markTermsAccepted: jest.fn()
            };

            const mockMessageBuilder = {
                buildGreeting: jest.fn(() => 'Greeting'),
                buildClinicRequest: jest.fn(() => 'Clinic request'),
                buildNameRequest: jest.fn(() => 'Name request'),
                buildCompletion: jest.fn(() => 'Completion')
            };

            jest.unstable_mockModule('../src/lambdas/gs2api/wpphook/utils/validators.mjs', () => ({
                default: {
                    validateUserResponse: jest.fn(() => true),
                    validateClinicName: jest.fn(() => true),
                    validateName: jest.fn(() => true)
                }
            }));

            const { StateMachine } = await import('../src/lambdas/gs2api/wpphook/services/stateMachine.mjs');
            const sm = new StateMachine(mockDatabase, mockMessageBuilder);

            // Test critical state transitions
            expect(sm.isValidTransition('INITIAL', 'GREETING')).toBe(true);
            expect(sm.isValidTransition('GREETING', 'CLINIC_REQUEST')).toBe(true);
            expect(sm.isValidTransition('CLINIC_REQUEST', 'CLINIC_VALIDATION')).toBe(true);
            expect(sm.isValidTransition('TERMS_ACCEPTANCE', 'COMPLETION')).toBe(true);
            expect(sm.isValidTransition('COMPLETION', 'END')).toBe(true);

            // Test invalid transitions are still blocked
            expect(sm.isValidTransition('INITIAL', 'COMPLETION')).toBe(false);
            expect(sm.isValidTransition('GREETING', 'EMAIL_REQUEST')).toBe(false);
        });
    });

    describe('Message Format Compatibility', () => {
        test('should generate messages compatible with existing format', async () => {
            const MessageBuilder = (await import('../src/lambdas/gs2api/wpphook/utils/messageBuilder.mjs')).default;
            const mb = new MessageBuilder();

            // Test key message formats
            const greeting = mb.buildGreeting();
            expect(greeting).toContain('👋 Olá');
            expect(greeting).toContain('Kuará');
            expect(greeting).toContain('Capitale Holding');
            expect(greeting).toContain('GS2');

            const clinicRequest = mb.buildClinicRequest();
            expect(clinicRequest).toContain('🏥');
            expect(clinicRequest).toContain('clínica');
            expect(clinicRequest).toContain('sair');

            const completion = mb.buildCompletion();
            expect(completion).toContain('🎉 Obrigado');
            expect(completion).toContain('entraremos em contato');
        });

        test('should maintain emoji and formatting consistency', async () => {
            const MessageBuilder = (await import('../src/lambdas/gs2api/wpphook/utils/messageBuilder.mjs')).default;
            const mb = new MessageBuilder();

            // Verify specific emojis are preserved
            expect(mb.buildNameRequest()).toContain('👤');
            expect(mb.buildCPFRequest()).toContain('📄');
            expect(mb.buildEmailRequest()).toContain('📧');
            expect(mb.buildFAQMenu()).toContain('❓');
            expect(mb.buildFAQMenu()).toContain('💸');
            expect(mb.buildFAQMenu()).toContain('🔒');
        });
    });

    describe('Error Handling Backward Compatibility', () => {
        test('should maintain error message consistency', async () => {
            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            const malformedEvent = {
                body: 'invalid json'
            };

            const result = await handler(malformedEvent, {});
            expect(result.statusCode).toBe(500);
            expect(result.body).toContain('erro interno');
        });

        test('should handle database connection failures gracefully', async () => {
            mockConnection.connect.mockImplementation((callback) => {
                callback(new Error('Connection failed'));
            });

            const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

            const event = {
                body: JSON.stringify({
                    message: 'GS2',
                    from: '+*************'
                })
            };

            const result = await handler(event, {});
            expect(result.statusCode).toBe(500);
            expect(result.body).toContain('erro interno');
        });
    });
});