-- =====================================================
-- SCHEMA REFATORADO - SISTEMA GS2
-- Gestão de Profissionais de Saúde e Antecipação de Recebíveis
-- =====================================================

-- Configurações iniciais
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_VALUE_ON_ZERO,NO_ENGINE_SUBSTITUTION';

-- =====================================================
-- 1. MÓDUL<PERSON> DE USUÁRIOS E AUTENTICAÇÃO
-- =====================================================

-- Tabela principal de usuários
CREATE TABLE users (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    cpf VARCHAR(11) NOT NULL,
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    birth_date DATE,
    mother_name VARCHAR(255),
    gender ENUM('masculino', 'feminino', 'outro'),
    marital_status ENUM('solteiro', 'casado', 'divorciado', 'viuvo', 'uniao_estavel'),
    nationality VARCHAR(100) DEFAULT 'brasileira',
    rg VARCHAR(20),
    active BOOLEAN DEFAULT TRUE,
    lgpd_accepted BOOLEAN DEFAULT FALSE,
    lgpd_accepted_at TIMESTAMP NULL,
    password_reset_required BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_users_uuid (uuid),
    UNIQUE KEY uk_users_cpf (cpf),
    UNIQUE KEY uk_users_email (email),
    INDEX idx_users_active (active),
    INDEX idx_users_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Endereços dos usuários
CREATE TABLE user_addresses (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    user_id BIGINT UNSIGNED NOT NULL,
    type ENUM('residencial', 'comercial', 'correspondencia') DEFAULT 'residencial',
    cep VARCHAR(8),
    street VARCHAR(255),
    number VARCHAR(20),
    complement VARCHAR(255),
    neighborhood VARCHAR(100),
    city VARCHAR(100),
    state CHAR(2),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_addresses_uuid (uuid),
    FOREIGN KEY fk_user_addresses_user (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_addresses_user_id (user_id),
    INDEX idx_user_addresses_primary (is_primary)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Grupos de usuários (roles)
CREATE TABLE user_groups (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type ENUM('master', 'admin', 'profissional', 'atendente') NOT NULL,
    permissions JSON,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_groups_uuid (uuid),
    UNIQUE KEY uk_user_groups_name (name),
    INDEX idx_user_groups_type (type),
    INDEX idx_user_groups_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Relacionamento usuário-grupo
CREATE TABLE user_group_memberships (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    user_id BIGINT UNSIGNED NOT NULL,
    group_id BIGINT UNSIGNED NOT NULL,
    client_id BIGINT UNSIGNED NULL, -- Para grupos específicos por cliente
    institution_id BIGINT UNSIGNED NULL, -- Para grupos específicos por instituição
    can_assign_permissions BOOLEAN DEFAULT FALSE,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_group_memberships_uuid (uuid),
    UNIQUE KEY uk_user_group_client_institution (user_id, group_id, client_id, institution_id),
    FOREIGN KEY fk_user_group_memberships_user (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY fk_user_group_memberships_group (group_id) REFERENCES user_groups(id) ON DELETE CASCADE,
    INDEX idx_user_group_memberships_user (user_id),
    INDEX idx_user_group_memberships_group (group_id),
    INDEX idx_user_group_memberships_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 2. MÓDULO DE REFERÊNCIAS E CONFIGURAÇÕES
-- =====================================================

-- Especialidades médicas
CREATE TABLE specialties (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_specialties_uuid (uuid),
    UNIQUE KEY uk_specialties_name (name),
    INDEX idx_specialties_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Conselhos de classe profissional
CREATE TABLE professional_councils (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    code VARCHAR(10) NOT NULL,
    name VARCHAR(200) NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_professional_councils_uuid (uuid),
    UNIQUE KEY uk_professional_councils_code (code),
    INDEX idx_professional_councils_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bancos
CREATE TABLE banks (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    code VARCHAR(4) NOT NULL,
    name VARCHAR(200) NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_banks_uuid (uuid),
    UNIQUE KEY uk_banks_code (code),
    INDEX idx_banks_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tipos de pagamento
CREATE TABLE payment_types (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    type ENUM('hora', 'fixo', 'unitario') NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_payment_types_uuid (uuid),
    UNIQUE KEY uk_payment_types_name (name),
    INDEX idx_payment_types_type (type),
    INDEX idx_payment_types_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tipos de PIX
CREATE TABLE pix_types (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    code VARCHAR(10) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(200),
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_pix_types_uuid (uuid),
    UNIQUE KEY uk_pix_types_code (code),
    INDEX idx_pix_types_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tipos de assinatura eletrônica
CREATE TABLE electronic_signature_types (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    code VARCHAR(10) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(200),
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_electronic_signature_types_uuid (uuid),
    UNIQUE KEY uk_electronic_signature_types_code (code),
    INDEX idx_electronic_signature_types_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 3. MÓDULO DE PROFISSIONAIS DE SAÚDE
-- =====================================================

-- Profissionais de saúde
CREATE TABLE health_professionals (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    user_id BIGINT UNSIGNED NOT NULL,
    secondary_contact VARCHAR(100),
    cnes VARCHAR(100),
    cns VARCHAR(100),
    professional_council_id BIGINT UNSIGNED NOT NULL,
    council_registration VARCHAR(100) NOT NULL,
    council_registration_city VARCHAR(100) NOT NULL,
    council_registration_state CHAR(2) NOT NULL,
    council_issuing_body VARCHAR(100) NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_health_professionals_uuid (uuid),
    UNIQUE KEY uk_health_professionals_user (user_id),
    FOREIGN KEY fk_health_professionals_user (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY fk_health_professionals_council (professional_council_id) REFERENCES professional_councils(id),
    INDEX idx_health_professionals_active (active),
    INDEX idx_health_professionals_council (professional_council_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Especialidades dos profissionais
CREATE TABLE health_professional_specialties (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    health_professional_id BIGINT UNSIGNED NOT NULL,
    specialty_id BIGINT UNSIGNED NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_health_professional_specialties_uuid (uuid),
    UNIQUE KEY uk_health_professional_specialties_unique (health_professional_id, specialty_id),
    FOREIGN KEY fk_health_professional_specialties_professional (health_professional_id) REFERENCES health_professionals(id) ON DELETE CASCADE,
    FOREIGN KEY fk_health_professional_specialties_specialty (specialty_id) REFERENCES specialties(id),
    INDEX idx_health_professional_specialties_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dados bancários dos profissionais
CREATE TABLE health_professional_bank_accounts (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    health_professional_id BIGINT UNSIGNED NOT NULL,
    bank_id BIGINT UNSIGNED NOT NULL,
    agency VARCHAR(10) NOT NULL,
    agency_digit CHAR(1),
    account VARCHAR(20) NOT NULL,
    account_digit CHAR(1),
    account_type ENUM('corrente', 'poupanca') DEFAULT 'corrente',
    account_holder_document VARCHAR(20), -- Para contas PJ
    is_primary BOOLEAN DEFAULT FALSE,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_health_professional_bank_accounts_uuid (uuid),
    FOREIGN KEY fk_health_professional_bank_accounts_professional (health_professional_id) REFERENCES health_professionals(id) ON DELETE CASCADE,
    FOREIGN KEY fk_health_professional_bank_accounts_bank (bank_id) REFERENCES banks(id),
    INDEX idx_health_professional_bank_accounts_professional (health_professional_id),
    INDEX idx_health_professional_bank_accounts_primary (is_primary),
    INDEX idx_health_professional_bank_accounts_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Chaves PIX dos profissionais
CREATE TABLE health_professional_pix_keys (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    health_professional_id BIGINT UNSIGNED NOT NULL,
    pix_type_id BIGINT UNSIGNED NOT NULL,
    pix_key VARCHAR(100) NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_health_professional_pix_keys_uuid (uuid),
    UNIQUE KEY uk_health_professional_pix_keys_key (pix_key),
    FOREIGN KEY fk_health_professional_pix_keys_professional (health_professional_id) REFERENCES health_professionals(id) ON DELETE CASCADE,
    FOREIGN KEY fk_health_professional_pix_keys_type (pix_type_id) REFERENCES pix_types(id),
    INDEX idx_health_professional_pix_keys_professional (health_professional_id),
    INDEX idx_health_professional_pix_keys_primary (is_primary),
    INDEX idx_health_professional_pix_keys_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Assinaturas eletrônicas dos profissionais
CREATE TABLE health_professional_electronic_signatures (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    health_professional_id BIGINT UNSIGNED NOT NULL,
    electronic_signature_type_id BIGINT UNSIGNED NOT NULL,
    signature_data VARCHAR(400),
    is_primary BOOLEAN DEFAULT FALSE,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_health_professional_electronic_signatures_uuid (uuid),
    FOREIGN KEY fk_health_professional_electronic_signatures_professional (health_professional_id) REFERENCES health_professionals(id) ON DELETE CASCADE,
    FOREIGN KEY fk_health_professional_electronic_signatures_type (electronic_signature_type_id) REFERENCES electronic_signature_types(id),
    INDEX idx_health_professional_electronic_signatures_professional (health_professional_id),
    INDEX idx_health_professional_electronic_signatures_primary (is_primary),
    INDEX idx_health_professional_electronic_signatures_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 4. MÓDULO DE CLIENTES E INSTITUIÇÕES
-- =====================================================

-- Clientes (hospitais/clínicas)
CREATE TABLE clients (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    cnpj VARCHAR(14) NOT NULL,
    company_name VARCHAR(255) NOT NULL,
    trade_name VARCHAR(255),
    contact_name VARCHAR(255),
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255) NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_clients_uuid (uuid),
    UNIQUE KEY uk_clients_cnpj (cnpj),
    INDEX idx_clients_active (active),
    INDEX idx_clients_company_name (company_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Endereços dos clientes
CREATE TABLE client_addresses (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    client_id BIGINT UNSIGNED NOT NULL,
    type ENUM('sede', 'filial', 'correspondencia') DEFAULT 'sede',
    cep VARCHAR(8),
    street VARCHAR(255),
    number VARCHAR(20),
    complement VARCHAR(255),
    neighborhood VARCHAR(100),
    city VARCHAR(100),
    state CHAR(2),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_client_addresses_uuid (uuid),
    FOREIGN KEY fk_client_addresses_client (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    INDEX idx_client_addresses_client_id (client_id),
    INDEX idx_client_addresses_primary (is_primary)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Usuários associados aos clientes
CREATE TABLE client_users (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    client_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    is_manager BOOLEAN DEFAULT FALSE,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_client_users_uuid (uuid),
    UNIQUE KEY uk_client_users_unique (client_id, user_id),
    FOREIGN KEY fk_client_users_client (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY fk_client_users_user (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_client_users_manager (is_manager),
    INDEX idx_client_users_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Instituições de saúde
CREATE TABLE health_institutions (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    cnpj VARCHAR(14) NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    ans_code VARCHAR(100),
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_health_institutions_uuid (uuid),
    UNIQUE KEY uk_health_institutions_cnpj (cnpj),
    INDEX idx_health_institutions_active (active),
    INDEX idx_health_institutions_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Endereços das instituições de saúde
CREATE TABLE health_institution_addresses (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    health_institution_id BIGINT UNSIGNED NOT NULL,
    type ENUM('sede', 'filial', 'correspondencia') DEFAULT 'sede',
    cep VARCHAR(8),
    street VARCHAR(255),
    number VARCHAR(20),
    complement VARCHAR(255),
    neighborhood VARCHAR(100),
    city VARCHAR(100),
    state CHAR(2),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_health_institution_addresses_uuid (uuid),
    FOREIGN KEY fk_health_institution_addresses_institution (health_institution_id) REFERENCES health_institutions(id) ON DELETE CASCADE,
    INDEX idx_health_institution_addresses_institution_id (health_institution_id),
    INDEX idx_health_institution_addresses_primary (is_primary)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Locais de atendimento das instituições
CREATE TABLE service_locations (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    health_institution_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    cep VARCHAR(8),
    street VARCHAR(255),
    number VARCHAR(20),
    complement VARCHAR(255),
    neighborhood VARCHAR(100),
    city VARCHAR(100),
    state CHAR(2),
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_service_locations_uuid (uuid),
    UNIQUE KEY uk_service_locations_institution_name (health_institution_id, name),
    FOREIGN KEY fk_service_locations_institution (health_institution_id) REFERENCES health_institutions(id) ON DELETE CASCADE,
    INDEX idx_service_locations_active (active),
    INDEX idx_service_locations_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Especialidades disponíveis por local de atendimento
CREATE TABLE service_location_specialties (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    service_location_id BIGINT UNSIGNED NOT NULL,
    specialty_id BIGINT UNSIGNED NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_service_location_specialties_uuid (uuid),
    UNIQUE KEY uk_service_location_specialties_unique (service_location_id, specialty_id),
    FOREIGN KEY fk_service_location_specialties_location (service_location_id) REFERENCES service_locations(id) ON DELETE CASCADE,
    FOREIGN KEY fk_service_location_specialties_specialty (specialty_id) REFERENCES specialties(id),
    INDEX idx_service_location_specialties_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Usuários associados às instituições de saúde
CREATE TABLE health_institution_users (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    health_institution_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_health_institution_users_uuid (uuid),
    UNIQUE KEY uk_health_institution_users_unique (health_institution_id, user_id),
    FOREIGN KEY fk_health_institution_users_institution (health_institution_id) REFERENCES health_institutions(id) ON DELETE CASCADE,
    FOREIGN KEY fk_health_institution_users_user (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_health_institution_users_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Profissionais associados aos clientes
CREATE TABLE health_professional_clients (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    health_professional_id BIGINT UNSIGNED NOT NULL,
    client_id BIGINT UNSIGNED NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_health_professional_clients_uuid (uuid),
    UNIQUE KEY uk_health_professional_clients_unique (health_professional_id, client_id),
    FOREIGN KEY fk_health_professional_clients_professional (health_professional_id) REFERENCES health_professionals(id) ON DELETE CASCADE,
    FOREIGN KEY fk_health_professional_clients_client (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    INDEX idx_health_professional_clients_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 5. MÓDULO DE CONTRATOS
-- =====================================================

-- Contratos entre instituições e clientes
CREATE TABLE contracts (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    contract_number VARCHAR(50) NOT NULL,
    health_institution_id BIGINT UNSIGNED NOT NULL,
    client_id BIGINT UNSIGNED NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NULL,
    fixed_value DECIMAL(15,2),
    variable_value DECIMAL(15,2),
    unit_value DECIMAL(15,2),
    status ENUM('ativo', 'suspenso', 'cancelado', 'finalizado') DEFAULT 'ativo',
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_contracts_uuid (uuid),
    UNIQUE KEY uk_contracts_number (contract_number),
    FOREIGN KEY fk_contracts_institution (health_institution_id) REFERENCES health_institutions(id),
    FOREIGN KEY fk_contracts_client (client_id) REFERENCES clients(id),
    INDEX idx_contracts_status (status),
    INDEX idx_contracts_active (active),
    INDEX idx_contracts_dates (start_date, end_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Especialidades por contrato com valores específicos
CREATE TABLE contract_specialties (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    contract_id BIGINT UNSIGNED NOT NULL,
    service_location_id BIGINT UNSIGNED NOT NULL,
    specialty_id BIGINT UNSIGNED NOT NULL,
    payment_type_id BIGINT UNSIGNED NOT NULL,
    hourly_rate DECIMAL(10,2),
    monthly_hours DECIMAL(8,2),
    fixed_professional_value DECIMAL(10,2),
    hourly_professional_rate DECIMAL(10,2),
    unit_professional_value DECIMAL(10,2),
    discount_rate DECIMAL(5,4), -- Taxa de deságio
    payment_day INT, -- Dia do mês para recebimento
    minimum_days_to_receive INT, -- Dias mínimos para recebimento
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_contract_specialties_uuid (uuid),
    UNIQUE KEY uk_contract_specialties_unique (contract_id, service_location_id, specialty_id, payment_type_id),
    FOREIGN KEY fk_contract_specialties_contract (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
    FOREIGN KEY fk_contract_specialties_location (service_location_id) REFERENCES service_locations(id),
    FOREIGN KEY fk_contract_specialties_specialty (specialty_id) REFERENCES specialties(id),
    FOREIGN KEY fk_contract_specialties_payment_type (payment_type_id) REFERENCES payment_types(id),
    INDEX idx_contract_specialties_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 6. MÓDULO DE PLANTÕES
-- =====================================================

-- Plantões
CREATE TABLE shifts (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    shift_number INT NOT NULL,
    contract_specialty_id BIGINT UNSIGNED NOT NULL,
    health_professional_id BIGINT UNSIGNED NULL, -- NULL quando não atribuído
    approver_user_id BIGINT UNSIGNED NULL,
    publication_date TIMESTAMP NULL,
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    required_hours INT NOT NULL,
    worked_hours TIME NULL,
    fixed_value DECIMAL(10,2),
    hourly_rate DECIMAL(10,2),
    unit_value DECIMAL(10,2),
    extra_value_1 DECIMAL(10,2), -- Valor extra 1
    extra_value_2 DECIMAL(10,2), -- Valor extra 2
    access_key VARCHAR(100),
    closing_type ENUM('manual', 'diario', 'semanal', 'mensal'),
    closing_date TIMESTAMP NULL,
    closing_day INT NULL, -- Dia do mês para fechamento
    receive_date DATE NULL,
    advance_date DATE NULL,
    status ENUM('aberto', 'solicitado', 'aprovado', 'executado', 'aprovado_execucao', 'antecipado', 'fechado') DEFAULT 'aberto',
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_shifts_uuid (uuid),
    UNIQUE KEY uk_shifts_number (shift_number),
    FOREIGN KEY fk_shifts_contract_specialty (contract_specialty_id) REFERENCES contract_specialties(id),
    FOREIGN KEY fk_shifts_professional (health_professional_id) REFERENCES health_professionals(id) ON DELETE SET NULL,
    FOREIGN KEY fk_shifts_approver (approver_user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_shifts_status (status),
    INDEX idx_shifts_active (active),
    INDEX idx_shifts_period (period_start, period_end),
    INDEX idx_shifts_professional (health_professional_id),
    INDEX idx_shifts_closing_date (closing_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Agenda dos plantões
CREATE TABLE shift_schedules (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    shift_id BIGINT UNSIGNED NOT NULL,
    schedule_date DATE NOT NULL,
    day_of_week TINYINT, -- 1=Segunda, 7=Domingo
    start_time TIME,
    end_time TIME,
    break_time TIME,
    start_datetime DATETIME NOT NULL,
    end_datetime DATETIME NOT NULL,
    value_type ENUM('normal', 'extra1', 'extra2') DEFAULT 'normal', -- Tipo de valor a ser aplicado
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_shift_schedules_uuid (uuid),
    UNIQUE KEY uk_shift_schedules_shift_date (shift_id, schedule_date),
    FOREIGN KEY fk_shift_schedules_shift (shift_id) REFERENCES shifts(id) ON DELETE CASCADE,
    INDEX idx_shift_schedules_date (schedule_date),
    INDEX idx_shift_schedules_active (active),
    INDEX idx_shift_schedules_datetime (start_datetime, end_datetime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Solicitações de plantão
CREATE TABLE shift_requests (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    shift_id BIGINT UNSIGNED NOT NULL,
    health_professional_id BIGINT UNSIGNED NOT NULL,
    sequence_number INT NOT NULL,
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('solicitado', 'rejeitado', 'aprovado', 'desistente') DEFAULT 'solicitado',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_shift_requests_uuid (uuid),
    UNIQUE KEY uk_shift_requests_unique (shift_id, health_professional_id, sequence_number),
    FOREIGN KEY fk_shift_requests_shift (shift_id) REFERENCES shifts(id) ON DELETE CASCADE,
    FOREIGN KEY fk_shift_requests_professional (health_professional_id) REFERENCES health_professionals(id) ON DELETE CASCADE,
    INDEX idx_shift_requests_status (status),
    INDEX idx_shift_requests_date (request_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Histórico de aprovações de solicitações
CREATE TABLE shift_request_approvals (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    shift_request_id BIGINT UNSIGNED NOT NULL,
    previous_status ENUM('solicitado', 'rejeitado', 'aprovado', 'desistente'),
    new_status ENUM('solicitado', 'rejeitado', 'aprovado', 'desistente') NOT NULL,
    event_date TIMESTAMP NOT NULL,
    event_user_id BIGINT UNSIGNED,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_shift_request_approvals_uuid (uuid),
    FOREIGN KEY fk_shift_request_approvals_request (shift_request_id) REFERENCES shift_requests(id) ON DELETE CASCADE,
    FOREIGN KEY fk_shift_request_approvals_user (event_user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_shift_request_approvals_date (event_date),
    INDEX idx_shift_request_approvals_status (new_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 7. MÓDULO DE CHECK-IN/CHECK-OUT
-- =====================================================

-- Justificativas para check-in/check-out
CREATE TABLE check_justifications (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_check_justifications_uuid (uuid),
    UNIQUE KEY uk_check_justifications_name (name),
    INDEX idx_check_justifications_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Registros de check-in/check-out
CREATE TABLE shift_checks (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    shift_schedule_id BIGINT UNSIGNED NOT NULL,
    check_in_time TIMESTAMP NOT NULL,
    break_time TIME,
    check_out_time TIMESTAMP NULL,
    worked_hours TIME,
    approved_hours TIME,
    rejected_hours TIME,
    justification_id BIGINT UNSIGNED NULL,
    closing_code INT NULL, -- Código de fechamento
    is_approved BOOLEAN DEFAULT FALSE,
    approver_user_id BIGINT UNSIGNED NULL,
    approval_date TIMESTAMP NULL,
    status ENUM('em_andamento', 'enviado_aprovacao', 'aprovado', 'fechado') DEFAULT 'em_andamento',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_shift_checks_uuid (uuid),
    UNIQUE KEY uk_shift_checks_schedule_checkin (shift_schedule_id, check_in_time),
    FOREIGN KEY fk_shift_checks_schedule (shift_schedule_id) REFERENCES shift_schedules(id) ON DELETE CASCADE,
    FOREIGN KEY fk_shift_checks_justification (justification_id) REFERENCES check_justifications(id) ON DELETE SET NULL,
    FOREIGN KEY fk_shift_checks_approver (approver_user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_shift_checks_status (status),
    INDEX idx_shift_checks_approved (is_approved),
    INDEX idx_shift_checks_check_in (check_in_time),
    INDEX idx_shift_checks_closing_code (closing_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 8. MÓDULO DE FECHAMENTOS
-- =====================================================

-- Códigos de fechamento sequenciais
CREATE TABLE closing_codes (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    closing_code INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_closing_codes_uuid (uuid),
    UNIQUE KEY uk_closing_codes_code (closing_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Aditivos de profissionais (valores extras/bônus)
CREATE TABLE professional_additives (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    shift_id BIGINT UNSIGNED NOT NULL,
    request_date TIMESTAMP NOT NULL,
    approval_date TIMESTAMP NULL,
    approver_user_id BIGINT UNSIGNED NULL,
    fixed_value DECIMAL(10,2),
    variable_value DECIMAL(10,2),
    net_value DECIMAL(10,2),
    gross_value DECIMAL(10,2),
    due_date DATE,
    status ENUM('solicitado', 'aprovado', 'rejeitado', 'pago') DEFAULT 'solicitado',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_professional_additives_uuid (uuid),
    FOREIGN KEY fk_professional_additives_shift (shift_id) REFERENCES shifts(id) ON DELETE CASCADE,
    FOREIGN KEY fk_professional_additives_approver (approver_user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_professional_additives_status (status),
    INDEX idx_professional_additives_request_date (request_date),
    INDEX idx_professional_additives_due_date (due_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Fechamentos de atendimento
CREATE TABLE service_closings (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    professional_additive_id BIGINT UNSIGNED NOT NULL,
    service_name VARCHAR(100) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    closing_code INT,
    value DECIMAL(10,2),
    status ENUM('pendente', 'aprovado', 'fechado') DEFAULT 'pendente',
    approver_user_id BIGINT UNSIGNED NULL,
    approval_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_service_closings_uuid (uuid),
    FOREIGN KEY fk_service_closings_additive (professional_additive_id) REFERENCES professional_additives(id) ON DELETE CASCADE,
    FOREIGN KEY fk_service_closings_approver (approver_user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_service_closings_status (status),
    INDEX idx_service_closings_closing_code (closing_code),
    INDEX idx_service_closings_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 9. MÓDULO DE INSTITUIÇÕES FINANCEIRAS E ANTECIPAÇÕES
-- =====================================================

-- Instituições financeiras
CREATE TABLE financial_institutions (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    cnpj VARCHAR(14) NOT NULL,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    cep VARCHAR(8),
    street VARCHAR(255) NOT NULL,
    number VARCHAR(20) NOT NULL,
    complement VARCHAR(255),
    neighborhood VARCHAR(100) NOT NULL,
    city VARCHAR(100) NOT NULL,
    state CHAR(2) NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_financial_institutions_uuid (uuid),
    UNIQUE KEY uk_financial_institutions_cnpj (cnpj),
    INDEX idx_financial_institutions_active (active),
    INDEX idx_financial_institutions_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Contatos das instituições financeiras
CREATE TABLE financial_institution_contacts (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    financial_institution_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_financial_institution_contacts_uuid (uuid),
    UNIQUE KEY uk_financial_institution_contacts_unique (financial_institution_id, user_id),
    FOREIGN KEY fk_financial_institution_contacts_institution (financial_institution_id) REFERENCES financial_institutions(id) ON DELETE CASCADE,
    FOREIGN KEY fk_financial_institution_contacts_user (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_financial_institution_contacts_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Relacionamento entre clientes e instituições financeiras
CREATE TABLE client_financial_institutions (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    client_id BIGINT UNSIGNED NOT NULL,
    financial_institution_id BIGINT UNSIGNED NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_client_financial_institutions_uuid (uuid),
    UNIQUE KEY uk_client_financial_institutions_unique (client_id, financial_institution_id),
    FOREIGN KEY fk_client_financial_institutions_client (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY fk_client_financial_institutions_institution (financial_institution_id) REFERENCES financial_institutions(id) ON DELETE CASCADE,
    INDEX idx_client_financial_institutions_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Antecipações de recebíveis
CREATE TABLE advances (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    advance_number INT NOT NULL,
    health_professional_id BIGINT UNSIGNED NOT NULL,
    financial_institution_id BIGINT UNSIGNED NOT NULL,
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approval_date TIMESTAMP NULL,
    approver_name VARCHAR(100),
    approver_user_id BIGINT UNSIGNED NULL,
    requested_amount DECIMAL(15,2) NOT NULL,
    approved_amount DECIMAL(15,2),
    net_amount DECIMAL(15,2),
    gross_amount DECIMAL(15,2),
    advance_rate DECIMAL(8,4), -- Taxa de antecipação
    receive_date DATE,
    kuara_id VARCHAR(100), -- ID no sistema Kuara
    kuara_status VARCHAR(100),
    kuara_payment_date DATE,
    approval_token VARCHAR(200),
    envelope_id VARCHAR(200), -- ID do envelope de assinatura
    status ENUM('solicitado', 'aprovado', 'rejeitado', 'pago', 'cancelado') DEFAULT 'solicitado',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_advances_uuid (uuid),
    UNIQUE KEY uk_advances_number (advance_number),
    FOREIGN KEY fk_advances_professional (health_professional_id) REFERENCES health_professionals(id) ON DELETE CASCADE,
    FOREIGN KEY fk_advances_institution (financial_institution_id) REFERENCES financial_institutions(id),
    FOREIGN KEY fk_advances_approver (approver_user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_advances_status (status),
    INDEX idx_advances_request_date (request_date),
    INDEX idx_advances_kuara_status (kuara_status),
    INDEX idx_advances_professional (health_professional_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Histórico de status das antecipações
CREATE TABLE advance_status_history (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    advance_id BIGINT UNSIGNED NOT NULL,
    previous_status VARCHAR(100),
    new_status VARCHAR(100) NOT NULL,
    change_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_advance_status_history_uuid (uuid),
    FOREIGN KEY fk_advance_status_history_advance (advance_id) REFERENCES advances(id) ON DELETE CASCADE,
    INDEX idx_advance_status_history_advance (advance_id),
    INDEX idx_advance_status_history_date (change_date),
    INDEX idx_advance_status_history_status (new_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Relacionamento entre antecipações e aditivos
CREATE TABLE advance_professional_additives (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    advance_id BIGINT UNSIGNED NOT NULL,
    professional_additive_id BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_advance_professional_additives_uuid (uuid),
    UNIQUE KEY uk_advance_professional_additives_unique (advance_id, professional_additive_id),
    FOREIGN KEY fk_advance_professional_additives_advance (advance_id) REFERENCES advances(id) ON DELETE CASCADE,
    FOREIGN KEY fk_advance_professional_additives_additive (professional_additive_id) REFERENCES professional_additives(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 10. MÓDULO DE MENSAGERIA E COMUNICAÇÃO
-- =====================================================

-- Templates de mensagens
CREATE TABLE message_templates (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    code VARCHAR(100) NOT NULL,
    meta_model VARCHAR(100),
    title VARCHAR(200),
    event_type VARCHAR(200),
    message_content TEXT,
    frequency ENUM('minuto', 'hora', 'dia', 'unico') DEFAULT 'unico',
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_message_templates_uuid (uuid),
    UNIQUE KEY uk_message_templates_code (code),
    INDEX idx_message_templates_frequency (frequency),
    INDEX idx_message_templates_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Mensagens do WhatsApp
CREATE TABLE whatsapp_messages (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    phone_number VARCHAR(27) NOT NULL,
    message_date TIMESTAMP NOT NULL,
    direction ENUM('enviada', 'recebida') NOT NULL,
    phone_from VARCHAR(27),
    message_type VARCHAR(50),
    payload VARCHAR(100),
    message_text LONGTEXT,
    payload_sent JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_whatsapp_messages_uuid (uuid),
    UNIQUE KEY uk_whatsapp_messages_unique (phone_number, message_date, direction),
    INDEX idx_whatsapp_messages_phone (phone_number),
    INDEX idx_whatsapp_messages_date (message_date),
    INDEX idx_whatsapp_messages_direction (direction),
    INDEX idx_whatsapp_messages_type (message_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Mensagens de agenda de plantão
CREATE TABLE shift_schedule_messages (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    shift_schedule_id BIGINT UNSIGNED NOT NULL,
    message_template_id BIGINT UNSIGNED NOT NULL,
    incidence_type VARCHAR(100) NOT NULL,
    sent_date TIMESTAMP NULL,
    status ENUM('pendente', 'enviada', 'erro') DEFAULT 'pendente',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_shift_schedule_messages_uuid (uuid),
    FOREIGN KEY fk_shift_schedule_messages_schedule (shift_schedule_id) REFERENCES shift_schedules(id) ON DELETE CASCADE,
    FOREIGN KEY fk_shift_schedule_messages_template (message_template_id) REFERENCES message_templates(id),
    INDEX idx_shift_schedule_messages_status (status),
    INDEX idx_shift_schedule_messages_sent_date (sent_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Mensagens por cliente
CREATE TABLE client_messages (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    client_id BIGINT UNSIGNED NOT NULL,
    message_template_id BIGINT UNSIGNED NOT NULL,
    incidence_type VARCHAR(100) NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_client_messages_uuid (uuid),
    UNIQUE KEY uk_client_messages_unique (client_id, message_template_id, incidence_type),
    FOREIGN KEY fk_client_messages_client (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY fk_client_messages_template (message_template_id) REFERENCES message_templates(id),
    INDEX idx_client_messages_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 11. MÓDULO DE CONFIGURAÇÕES E SISTEMA
-- =====================================================

-- Configurações do sistema
CREATE TABLE system_configurations (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    config_key VARCHAR(255) NOT NULL,
    config_value TEXT NOT NULL,
    description TEXT,
    data_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    is_public BOOLEAN DEFAULT FALSE, -- Se pode ser acessada publicamente
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_system_configurations_uuid (uuid),
    UNIQUE KEY uk_system_configurations_key (config_key),
    INDEX idx_system_configurations_public (is_public),
    INDEX idx_system_configurations_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Protocolos sequenciais
CREATE TABLE protocol_numbers (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    protocol_number INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_protocol_numbers_uuid (uuid),
    UNIQUE KEY uk_protocol_numbers_number (protocol_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Ouvidoria
CREATE TABLE ombudsman_requests (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    name VARCHAR(200) NOT NULL,
    email VARCHAR(200),
    phone VARCHAR(50),
    subject LONGTEXT,
    message LONGTEXT NOT NULL,
    protocol_number VARCHAR(100),
    status ENUM('aberto', 'em_andamento', 'resolvido', 'fechado') DEFAULT 'aberto',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_ombudsman_requests_uuid (uuid),
    INDEX idx_ombudsman_requests_status (status),
    INDEX idx_ombudsman_requests_protocol (protocol_number),
    INDEX idx_ombudsman_requests_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Solicitações DPO (Data Protection Officer)
CREATE TABLE dpo_requests (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    name VARCHAR(200) NOT NULL,
    email VARCHAR(200),
    phone VARCHAR(50),
    subject LONGTEXT,
    message LONGTEXT NOT NULL,
    protocol_number VARCHAR(100),
    request_type ENUM('acesso', 'retificacao', 'exclusao', 'portabilidade', 'oposicao') NOT NULL,
    status ENUM('aberto', 'em_andamento', 'resolvido', 'fechado') DEFAULT 'aberto',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_dpo_requests_uuid (uuid),
    INDEX idx_dpo_requests_status (status),
    INDEX idx_dpo_requests_type (request_type),
    INDEX idx_dpo_requests_protocol (protocol_number),
    INDEX idx_dpo_requests_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 12. MÓDULO DE AUDITORIA
-- =====================================================

-- Log de auditoria para todas as operações importantes
CREATE TABLE audit_logs (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    table_name VARCHAR(100) NOT NULL,
    record_id BIGINT UNSIGNED NOT NULL,
    record_uuid CHAR(36),
    operation ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
    user_id BIGINT UNSIGNED NULL,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uk_audit_logs_uuid (uuid),
    FOREIGN KEY fk_audit_logs_user (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_audit_logs_table_record (table_name, record_id),
    INDEX idx_audit_logs_record_uuid (record_uuid),
    INDEX idx_audit_logs_operation (operation),
    INDEX idx_audit_logs_user (user_id),
    INDEX idx_audit_logs_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Reativar verificações de foreign key
SET FOREIGN_KEY_CHECKS = 1;
