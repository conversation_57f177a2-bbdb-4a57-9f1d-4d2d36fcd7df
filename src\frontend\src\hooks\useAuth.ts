// =====================================================
// USE AUTH HOOK - HOOK DE AUTENTICAÇÃO
// Hook personalizado para gerenciar autenticação
// =====================================================

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import toast from 'react-hot-toast'
import { useAuthStore } from '@/stores/authStore'
import { modernAuthService } from '@/services/modernAuthService'
import type { LoginCredentials, User } from '@/types'

// Hook principal de autenticação
export function useAuth() {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    login: loginStore,
    logout: logoutStore,
    updateUser,
    clearError
  } = useAuthStore()

  // Mutation para login
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginCredentials) => {
      const success = await loginStore(credentials.cpf, credentials.password)
      if (!success) {
        throw new Error(error || t('auth.loginError'))
      }
      return success
    },
    onSuccess: () => {
      toast.success(t('auth.loginSuccess'))
      navigate('/dashboard')
      queryClient.invalidateQueries({ queryKey: ['user'] })
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  // Mutation para logout
  const logoutMutation = useMutation({
    mutationFn: async () => {
      await modernAuthService.logout()
      logoutStore()
    },
    onSuccess: () => {
      toast.success(t('auth.logoutSuccess'))
      navigate('/login')
      queryClient.clear()
    },
    onError: () => {
      // Mesmo com erro, fazer logout local
      logoutStore()
      navigate('/login')
      queryClient.clear()
    }
  })

  // Query para verificar token
  const { data: tokenValid } = useQuery({
    queryKey: ['auth', 'verify'],
    queryFn: () => modernAuthService.verifyToken(),
    enabled: !!token && isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: false,
    refetchInterval: 10 * 60 * 1000, // Verificar a cada 10 minutos
  })

  // Query para obter perfil do usuário
  const { data: profile, isLoading: isLoadingProfile } = useQuery({
    queryKey: ['user', 'profile'],
    queryFn: () => modernAuthService.getProfile(),
    enabled: isAuthenticated,
    staleTime: 10 * 60 * 1000, // 10 minutos
    onSuccess: (data) => {
      if (data && data !== user) {
        updateUser(data)
      }
    }
  })

  // Mutation para alterar senha
  const changePasswordMutation = useMutation({
    mutationFn: async ({ currentPassword, newPassword }: {
      currentPassword: string
      newPassword: string
    }) => {
      const success = await modernAuthService.changePassword(currentPassword, newPassword)
      if (!success) {
        throw new Error(t('auth.passwordChangeError'))
      }
      return success
    },
    onSuccess: () => {
      toast.success(t('auth.passwordChanged'))
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  // Mutation para atualizar perfil
  const updateProfileMutation = useMutation({
    mutationFn: async (data: Partial<User>) => {
      const success = await modernAuthService.updateProfile(data)
      if (!success) {
        throw new Error(t('common.error'))
      }
      return data
    },
    onSuccess: (data) => {
      updateUser(data)
      toast.success(t('common.success'))
      queryClient.invalidateQueries({ queryKey: ['user', 'profile'] })
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  // Função para fazer login
  const login = (credentials: LoginCredentials) => {
    clearError()
    return loginMutation.mutate(credentials)
  }

  // Função para fazer logout
  const logout = () => {
    return logoutMutation.mutate()
  }

  // Função para alterar senha
  const changePassword = (currentPassword: string, newPassword: string) => {
    return changePasswordMutation.mutate({ currentPassword, newPassword })
  }

  // Função para atualizar perfil
  const updateProfile = (data: Partial<User>) => {
    return updateProfileMutation.mutate(data)
  }

  return {
    // Estado
    user,
    token,
    isAuthenticated,
    isLoading: isLoading || loginMutation.isPending || logoutMutation.isPending,
    isLoadingProfile,
    error,
    
    // Ações
    login,
    logout,
    changePassword,
    updateProfile,
    clearError,
    
    // Status das mutations
    isLoggingIn: loginMutation.isPending,
    isLoggingOut: logoutMutation.isPending,
    isChangingPassword: changePasswordMutation.isPending,
    isUpdatingProfile: updateProfileMutation.isPending,
    
    // Verificação de token
    tokenValid: tokenValid?.valid ?? false
  }
}

// Hook para verificar permissões
export function usePermissions() {
  const { hasPermission, hasGroup, hasAnyGroup } = useAuthStore()
  
  return {
    hasPermission,
    hasGroup,
    hasAnyGroup,
    
    // Permissões específicas
    isMaster: hasGroup('Master'),
    isAdmin: hasAnyGroup(['Master', 'Admin']),
    isProfessional: hasGroup('Profissional'),
    isFinancial: hasAnyGroup(['Master', 'Admin', 'Financeiro']),
    isAuditor: hasAnyGroup(['Master', 'Admin', 'Auditor']),
    
    // Verificações de funcionalidades
    canManageUsers: hasPermission('users.write'),
    canManageProfessionals: hasPermission('professionals.write'),
    canManageClients: hasPermission('clients.write'),
    canManageShifts: hasPermission('shifts.write'),
    canManageAdvances: hasPermission('advances.write'),
    canViewAudit: hasPermission('audit.read'),
    canExportData: hasPermission('data.export'),
    canManageSettings: hasPermission('settings.write')
  }
}

// Hook para recuperação de senha
export function usePasswordRecovery() {
  const { t } = useTranslation()
  
  // Mutation para solicitar recuperação
  const requestResetMutation = useMutation({
    mutationFn: async (email: string) => {
      const success = await modernAuthService.requestPasswordReset(email)
      if (!success) {
        throw new Error(t('auth.resetRequestError'))
      }
      return success
    },
    onSuccess: () => {
      toast.success(t('auth.resetLinkSent'))
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  // Mutation para resetar senha
  const resetPasswordMutation = useMutation({
    mutationFn: async ({ token, newPassword }: {
      token: string
      newPassword: string
    }) => {
      const success = await modernAuthService.resetPassword(token, newPassword)
      if (!success) {
        throw new Error(t('auth.resetPasswordError'))
      }
      return success
    },
    onSuccess: () => {
      toast.success(t('auth.resetPasswordSuccess'))
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  return {
    requestReset: (email: string) => requestResetMutation.mutate(email),
    resetPassword: (token: string, newPassword: string) => 
      resetPasswordMutation.mutate({ token, newPassword }),
    
    isRequestingReset: requestResetMutation.isPending,
    isResettingPassword: resetPasswordMutation.isPending
  }
}
