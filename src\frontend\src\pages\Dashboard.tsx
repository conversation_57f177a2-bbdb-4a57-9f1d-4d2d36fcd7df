// =====================================================
// DASHBOARD PAGE - PÁGINA DO DASHBOARD
// Página principal com visão geral do sistema
// =====================================================

import React from 'react'
import { useTranslation } from 'react-i18next'
import { useQuery } from '@tanstack/react-query'
import { motion } from 'framer-motion'
import {
  Users,
  UserCheck,
  Building2,
  Calendar,
  CreditCard,
  TrendingUp,
  TrendingDown,
  Activity,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { LoadingSpinner, SectionLoading } from '@/components/ui/LoadingSpinner'
import { Button } from '@/components/ui/Button'
import { modernApi } from '@/services/modernApi'
import { formatCurrency, formatNumber } from '@/utils'
import type { DashboardStats } from '@/types'

export function Dashboard() {
  const { t } = useTranslation()

  // Query para estatísticas do dashboard
  const { data: stats, isLoading, error, refetch } = useQuery({
    queryKey: ['dashboard', 'stats'],
    queryFn: () => modernApi.getDashboardStats(),
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchInterval: 30 * 1000, // Atualizar a cada 30 segundos
  })

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-heading">
              {t('dashboard.title')}
            </h1>
            <p className="text-muted mt-1">
              {t('dashboard.welcome')}
            </p>
          </div>
        </div>
        <SectionLoading />
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-heading">
              {t('dashboard.title')}
            </h1>
            <p className="text-muted mt-1">
              {t('dashboard.welcome')}
            </p>
          </div>
          <Button onClick={() => refetch()} variant="outline">
            {t('dashboard.refreshStats')}
          </Button>
        </div>
        
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 text-error-500 mx-auto mb-4" />
              <p className="text-error-600 font-medium">
                {t('dashboard.errorLoadingStats')}
              </p>
              <Button onClick={() => refetch()} className="mt-4" size="sm">
                Tentar Novamente
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const statsData = stats

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-heading">
            {t('dashboard.title')}
          </h1>
          <p className="text-muted mt-1">
            {t('dashboard.welcome')}
          </p>
        </div>
        
        <Button onClick={() => refetch()} variant="outline">
          {t('dashboard.refreshStats')}
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Usuários */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <StatsCard
            title={t('dashboard.totalUsers')}
            value={statsData?.users.total || 0}
            subtitle={`${statsData?.users.active || 0} ${t('dashboard.activeUsers').toLowerCase()}`}
            icon={Users}
            color="blue"
            trend={statsData?.users.new_last_30_days}
          />
        </motion.div>

        {/* Profissionais */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <StatsCard
            title={t('dashboard.totalProfessionals')}
            value={statsData?.professionals.total || 0}
            subtitle={`${statsData?.professionals.active || 0} ${t('dashboard.activeProfessionals').toLowerCase()}`}
            icon={UserCheck}
            color="green"
            trend={statsData?.professionals.new_last_30_days}
          />
        </motion.div>

        {/* Clientes */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <StatsCard
            title={t('dashboard.totalClients')}
            value={statsData?.clients.total || 0}
            subtitle={`${statsData?.clients.active || 0} ${t('dashboard.activeClients').toLowerCase()}`}
            icon={Building2}
            color="purple"
            trend={statsData?.clients.new_last_30_days}
          />
        </motion.div>

        {/* Plantões */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <StatsCard
            title={t('dashboard.totalShifts')}
            value={statsData?.shifts.total || 0}
            subtitle={`${statsData?.shifts.open || 0} ${t('dashboard.openShifts').toLowerCase()}`}
            icon={Calendar}
            color="orange"
            trend={statsData?.shifts.approved}
          />
        </motion.div>
      </div>

      {/* Antecipações e Atividade */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Antecipações */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CreditCard className="w-5 h-5 text-primary-600" />
                <span>{t('dashboard.totalAdvances')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-muted">Total de Antecipações</span>
                  <span className="font-semibold text-heading">
                    {formatNumber(statsData?.advances.total || 0)}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-muted">Pendentes</span>
                  <span className="font-semibold text-warning-600">
                    {formatNumber(statsData?.advances.pending || 0)}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-muted">Aprovadas</span>
                  <span className="font-semibold text-success-600">
                    {formatNumber(statsData?.advances.approved || 0)}
                  </span>
                </div>
                
                <div className="pt-4 border-t border-secondary-200 dark:border-secondary-700">
                  <div className="flex items-center justify-between">
                    <span className="text-muted">Valor Total</span>
                    <span className="font-bold text-lg text-primary-600">
                      {formatCurrency(statsData?.advances.total_amount || 0)}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Atividade Recente */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="w-5 h-5 text-primary-600" />
                <span>{t('dashboard.recentActivity')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-success-500 rounded-full" />
                  <span className="text-sm text-muted">
                    Plantão aprovado há 5 minutos
                  </span>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary-500 rounded-full" />
                  <span className="text-sm text-muted">
                    Novo profissional cadastrado há 15 minutos
                  </span>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-warning-500 rounded-full" />
                  <span className="text-sm text-muted">
                    Antecipação solicitada há 30 minutos
                  </span>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-success-500 rounded-full" />
                  <span className="text-sm text-muted">
                    Cliente ativado há 1 hora
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

// Componente de card de estatísticas
interface StatsCardProps {
  title: string
  value: number
  subtitle: string
  icon: React.ComponentType<any>
  color: 'blue' | 'green' | 'purple' | 'orange'
  trend?: number
}

function StatsCard({ title, value, subtitle, icon: Icon, color, trend }: StatsCardProps) {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-100 dark:bg-blue-900/20',
    green: 'text-green-600 bg-green-100 dark:bg-green-900/20',
    purple: 'text-purple-600 bg-purple-100 dark:bg-purple-900/20',
    orange: 'text-orange-600 bg-orange-100 dark:bg-orange-900/20'
  }

  return (
    <Card className="hover:shadow-medium transition-shadow duration-200">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-muted mb-1">
              {title}
            </p>
            <p className="text-2xl font-bold text-heading">
              {formatNumber(value)}
            </p>
            <p className="text-xs text-muted mt-1">
              {subtitle}
            </p>
          </div>
          
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            <Icon className="w-6 h-6" />
          </div>
        </div>
        
        {trend !== undefined && (
          <div className="flex items-center mt-4 pt-4 border-t border-secondary-200 dark:border-secondary-700">
            {trend > 0 ? (
              <TrendingUp className="w-4 h-4 text-success-500 mr-1" />
            ) : (
              <TrendingDown className="w-4 h-4 text-error-500 mr-1" />
            )}
            <span className={`text-sm font-medium ${trend > 0 ? 'text-success-600' : 'text-error-600'}`}>
              {trend > 0 ? '+' : ''}{trend}
            </span>
            <span className="text-xs text-muted ml-1">
              últimos 30 dias
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
