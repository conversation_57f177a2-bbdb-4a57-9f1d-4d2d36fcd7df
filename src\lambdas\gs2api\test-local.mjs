import { handler } from './index.mjs';

// Simular evento do Lambda Function URL
const testEvent = {
  version: "2.0",
  routeKey: "$default",
  rawPath: "/login",
  rawQueryString: "",
  headers: {
    "content-type": "application/json",
    "user-agent": "Test Client",
    "x-forwarded-for": "127.0.0.1"
  },
  requestContext: {
    http: {
      method: "POST",
      path: "/login",
      protocol: "HTTP/1.1",
      sourceIp: "127.0.0.1"
    }
  },
  body: JSON.stringify({
    username: "12345678901",
    password: "123456"
  }),
  isBase64Encoded: false
};

// Testar o handler
async function test() {
  console.log('Testing login endpoint...');
  console.log('Request:', JSON.stringify(testEvent, null, 2));
  
  try {
    const result = await handler(testEvent);
    console.log('\nResponse:', JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('Error:', error);
  }
}

test();
