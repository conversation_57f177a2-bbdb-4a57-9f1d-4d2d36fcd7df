import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';
import { validateEmail, sanitizeHtml } from './validationUtils.mjs';

// Configuration constants
const DEFAULT_REGION = process.env.AWS_REGION || 'us-east-2';
const DEFAULT_SOURCE = process.env.EMAIL_SOURCE || '<EMAIL>';
const LOGO_URL = process.env.LOGO_URL || 'https://gs2group.com/images/GS2_Logo.png';
const BRAND_LOGO_URL =
  process.env.BRAND_LOGO_URL ||
  'https://capitale-imagens.s3.us-east-2.amazonaws.com/logo/Capitale-logo.jpg';
const SUPPORT_EMAIL = process.env.SUPPORT_EMAIL || '<EMAIL>';
const SUPPORT_PHONE = process.env.SUPPORT_PHONE || '+55 (11) 99999-9999';
const WEBSITE_URL = process.env.WEBSITE_URL || 'https://capitaleholding.com';

// Create SES client with configuration
const ses = new SESClient({ region: 'us-east-2' });

// Email template components
const getHeaderTemplate = () => `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Capitale Holding</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      font-size: 14px;
      color: #333;
      margin: 0;
      padding: 0;
      line-height: 1.6;
    }
    .container {}
    .content {
      margin-bottom: 30px;
    }
    .signature {
      display: table;
      width: 100%;
      border-collapse: collapse;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }
    .logo-cell {
      width: 120px;
      padding-right: 15px;
      vertical-align: top;
    }
    .logo {
      width: 100px;
    }
    .info-cell {
      padding: 0;
      vertical-align: top;
    }
    .info {
      line-height: 1.4;
    }
    .name {
      font-size: 16px;
      font-weight: bold;
      color: #2c3e50;
      margin: 0;
    }
    .position {
      font-size: 14px;
      color: #7f8c8d;
      margin: 5px 0;
    }
    .contact {
      margin: 10px 0 0 0;
    }
    .contact a {
      color: #3498db;
      text-decoration: none;
      margin-right: 15px;
    }
    .contact a:hover {
      text-decoration: underline;
    }
    .icon {
      margin-right: 5px;
      vertical-align: middle;
    }
    .footer {
      text-align: center;
      font-size: 12px;
      color: #7f8c8d;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="content">
</body>
</html>`;

const getFooterTemplate = () => `
    </div>
    <table class="signature">
      <tr>
        <td class="logo-cell">
          <img src="${BRAND_LOGO_URL}" alt="Logo da Capitale Holding" class="logo">
        </td>
        <td class="info-cell">
          <div class="info">
            <h3 class="name">Capitale Holding</h3>
            <div class="position">Central de Atendimento</div>
            <div class="contact">
              <span class="icon">📧</span>
              <a href="mailto:${SUPPORT_EMAIL}">${SUPPORT_EMAIL}</a>
              <span class="icon">📞</span>
              <a href="tel:${SUPPORT_PHONE.replace(/\D/g, '')}">${SUPPORT_PHONE}</a>
              <span class="icon">🌐</span>
              <a href="${WEBSITE_URL}" target="_blank">${WEBSITE_URL.replace(/^https?:\/\//,'')}</a>
            </div>
          </div>
        </td>
      </tr>
    </table>
    <div class="footer">
      &copy; ${new Date().getFullYear()} Capitale Holding. Todos os direitos reservados.
    </div>
  </div>
</body>
</html>`;

/**
 * Builds the complete HTML email with header, content, and footer
 * @param {string} content - The main email content
 * @returns {string} Complete HTML email
 */
const buildEmailHtml = (content) => {
  const sanitizedContent = sanitizeHtml(content);
  return getHeaderTemplate() + sanitizedContent + getFooterTemplate();
};

/**
 * Validates email input parameters
 * @param {Object} params - Email parameters
 * @returns {Object} Validation result with success flag and optional error message
 */
const validateEmailParams = (params) => {
  const { to, subject, body } = params;

  if (!to) {
    return { success: false, message: "Destinatário 'to' é obrigatório." };
  }

  if (Array.isArray(to)) {
    const invalidEmails = to.filter((email) => !validateEmail(email));
    if (invalidEmails.length > 0) {
      return {
        success: false,
        message: `Os seguintes endereços de e-mail são inválidos: ${invalidEmails.join(', ')}`,
      };
    }
  } else if (!validateEmail(to)) {
    return { success: false, message: `Endereço de e-mail inválido: ${to}` };
  }

  if (!subject || subject.trim().length === 0) {
    return { success: false, message: "Assunto 'subject' é obrigatório." };
  }

  if (!body || body.trim().length === 0) {
    return { success: false, message: "Corpo 'body' do e-mail é obrigatório." };
  }

  return { success: true };
};

/**
 * Processes CC and BCC recipients
 * @param {string|Array} recipients - Recipient(s)
 * @returns {Array} Array of valid email addresses
 */
const processRecipients = (recipients) => {
  if (!recipients) return [];

  const recipientList = Array.isArray(recipients) ? recipients : [recipients];
  return recipientList.filter((email) => validateEmail(email));
};

/**
 * Main handler function for sending emails via SES
 * @param {Object} event - Lambda event containing email parameters
 * @returns {Object} Response with status code and message
 */
export const handler = async (event) => {
  // Extract and normalize input
  const { to, subject, body, cc, bcc, replyTo } = event || {};

  // Validate input parameters
  const validation = validateEmailParams({ to, subject, body });
  if (!validation.success) {
    return {
      statusCode: 400,
      body: JSON.stringify({
        message: validation.message,
      }),
    };
  }

  try {
    // Build email HTML content
    const emailHtml = buildEmailHtml(body);

    // Process recipients
    const toAddresses = Array.isArray(to) ? to : [to];
    const ccAddresses = processRecipients(cc);
    const bccAddresses = processRecipients(bcc);

    // Prepare SES parameters
    const params = {
      Destination: {
        ToAddresses: toAddresses,
      },
      Message: {
        Subject: {
          Data: subject,
          Charset: 'UTF-8',
        },
        Body: {
          Html: {
            Data: emailHtml,
            Charset: 'UTF-8',
          },
        },
      },
      Source: DEFAULT_SOURCE,
    };

    // Add optional parameters if present
    if (ccAddresses.length > 0) {
      params.Destination.CcAddresses = ccAddresses;
    }

    if (bccAddresses.length > 0) {
      params.Destination.BccAddresses = bccAddresses;
    }

    if (replyTo && validateEmail(replyTo)) {
      params.ReplyToAddresses = [replyTo];
    }

    // Send email
    await ses.send(new SendEmailCommand(params));

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'E-mail enviado com sucesso!',
        recipients: {
          to: toAddresses,
          cc: ccAddresses,
          bcc: bccAddresses.length, // Don't expose BCC recipients in response
        },
      }),
    };
  } catch (error) {
    console.error('Erro ao enviar e-mail:', error);

    // Return generic error message to avoid exposing sensitive information
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: 'Falha ao enviar e-mail. Por favor, tente novamente mais tarde.',
        errorCode: 'EMAIL_SEND_FAILED',
      }),
    };
  }
};
