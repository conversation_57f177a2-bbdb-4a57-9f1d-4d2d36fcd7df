// =====================================================
// USE API HOOKS - HOOKS PARA CONSUMIR A API
// Hooks personalizados para cada funcionalidade
// =====================================================

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useTranslation } from 'react-i18next'
import toast from 'react-hot-toast'
import { modernApi } from '@/services/modernApi'

// Hook para Dashboard
export function useDashboard() {
  const { t } = useTranslation()

  const { data: stats, isLoading: isLoadingStats } = useQuery({
    queryKey: ['dashboard', 'stats'],
    queryFn: () => modernApi.getDashboardStats(),
    staleTime: 5 * 60 * 1000, // 5 minutos
  })

  const { data: activity, isLoading: isLoadingActivity } = useQuery({
    queryKey: ['dashboard', 'activity'],
    queryFn: () => modernApi.getRecentActivity(10),
    staleTime: 2 * 60 * 1000, // 2 minutos
  })

  return {
    stats,
    activity,
    isLoading: isLoadingStats || isLoadingActivity
  }
}

// Hook para Usuários
export function useUsers(filters?: any) {
  const { t } = useTranslation()
  const queryClient = useQueryClient()

  const { data: users, isLoading, error } = useQuery({
    queryKey: ['users', filters],
    queryFn: () => modernApi.getUsers(filters),
    staleTime: 5 * 60 * 1000,
  })

  const createUserMutation = useMutation({
    mutationFn: (userData: any) => modernApi.createUser(userData),
    onSuccess: () => {
      toast.success(t('users.createSuccess'))
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    onError: (error: any) => {
      toast.error(error.message || t('users.createError'))
    }
  })

  const updateUserMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      modernApi.updateUser(id, data),
    onSuccess: () => {
      toast.success(t('users.updateSuccess'))
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    onError: (error: any) => {
      toast.error(error.message || t('users.updateError'))
    }
  })

  return {
    users,
    isLoading,
    error,
    createUser: createUserMutation.mutate,
    updateUser: updateUserMutation.mutate,
    isCreating: createUserMutation.isPending,
    isUpdating: updateUserMutation.isPending
  }
}

// Hook para Profissionais
export function useProfessionals(filters?: any) {
  const { t } = useTranslation()
  const queryClient = useQueryClient()

  const { data: professionals, isLoading, error } = useQuery({
    queryKey: ['professionals', filters],
    queryFn: () => modernApi.getProfessionals(filters),
    staleTime: 5 * 60 * 1000,
  })

  const createProfessionalMutation = useMutation({
    mutationFn: (data: any) => modernApi.createProfessional(data),
    onSuccess: () => {
      toast.success(t('professionals.createSuccess'))
      queryClient.invalidateQueries({ queryKey: ['professionals'] })
    },
    onError: (error: any) => {
      toast.error(error.message || t('professionals.createError'))
    }
  })

  const updateProfessionalMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      modernApi.updateProfessional(id, data),
    onSuccess: () => {
      toast.success(t('professionals.updateSuccess'))
      queryClient.invalidateQueries({ queryKey: ['professionals'] })
    },
    onError: (error: any) => {
      toast.error(error.message || t('professionals.updateError'))
    }
  })

  return {
    professionals,
    isLoading,
    error,
    createProfessional: createProfessionalMutation.mutate,
    updateProfessional: updateProfessionalMutation.mutate,
    isCreating: createProfessionalMutation.isPending,
    isUpdating: updateProfessionalMutation.isPending
  }
}

// Hook para Clientes
export function useClients(filters?: any) {
  const { t } = useTranslation()
  const queryClient = useQueryClient()

  const { data: clients, isLoading, error } = useQuery({
    queryKey: ['clients', filters],
    queryFn: () => modernApi.getClients(filters),
    staleTime: 5 * 60 * 1000,
  })

  const createClientMutation = useMutation({
    mutationFn: (data: any) => modernApi.createClient(data),
    onSuccess: () => {
      toast.success(t('clients.createSuccess'))
      queryClient.invalidateQueries({ queryKey: ['clients'] })
    },
    onError: (error: any) => {
      toast.error(error.message || t('clients.createError'))
    }
  })

  const updateClientMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      modernApi.updateClient(id, data),
    onSuccess: () => {
      toast.success(t('clients.updateSuccess'))
      queryClient.invalidateQueries({ queryKey: ['clients'] })
    },
    onError: (error: any) => {
      toast.error(error.message || t('clients.updateError'))
    }
  })

  return {
    clients,
    isLoading,
    error,
    createClient: createClientMutation.mutate,
    updateClient: updateClientMutation.mutate,
    isCreating: createClientMutation.isPending,
    isUpdating: updateClientMutation.isPending
  }
}

// Hook para Auditoria
export function useAudit(filters?: any) {
  const { data: auditLogs, isLoading, error } = useQuery({
    queryKey: ['audit', filters],
    queryFn: () => modernApi.getAuditLogs(filters),
    staleTime: 2 * 60 * 1000, // 2 minutos
  })

  return {
    auditLogs,
    isLoading,
    error
  }
}

// Hook genérico para queries
export function useApiQuery<T>(
  queryKey: string[],
  queryFn: () => Promise<T>,
  options?: any
) {
  return useQuery({
    queryKey,
    queryFn,
    staleTime: 5 * 60 * 1000,
    ...options
  })
}

// Hook genérico para mutations
export function useApiMutation<T, V>(
  mutationFn: (variables: V) => Promise<T>,
  options?: any
) {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn,
    onSuccess: (data, variables, context) => {
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context)
      }
      if (options?.invalidateQueries) {
        queryClient.invalidateQueries({ queryKey: options.invalidateQueries })
      }
    },
    onError: (error: any) => {
      if (options?.onError) {
        options.onError(error)
      } else {
        toast.error(error.message || 'Erro na operação')
      }
    },
    ...options
  })
}
