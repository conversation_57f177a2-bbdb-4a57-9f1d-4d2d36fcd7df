-- =====================================================
-- SCRIPT DE MIGRAÇÃO - SCHEMA ANTIGO PARA NOVO
-- Sistema GS2 - Migração de dados
-- =====================================================

-- ATENÇÃO: Execute este script apenas após backup completo do banco!
-- Recomenda-se testar em ambiente de desenvolvimento primeiro.

SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_VALUE_ON_ZERO,NO_ENGINE_SUBSTITUTION';

-- =====================================================
-- 1. MIGRAÇÃO DE USUÁRIOS
-- =====================================================

-- Migrar usuários básicos
INSERT INTO users (
    uuid, cpf, email, password_hash, name, phone, birth_date, mother_name,
    gender, marital_status, nationality, rg, active, lgpd_accepted, 
    password_reset_required, created_at, updated_at
)
SELECT 
    UUID(),
    usCPFUsuario,
    COALESCE(usEmail, CONCAT(usCPFUsuario, '@temp.com')),
    COALESCE(usSenha, '$2a$10$defaulthash'),
    usNome,
    usTelefone,
    usDatNasc,
    usNomeMae,
    CASE 
        WHEN usGenero = 'M' THEN 'masculino'
        WHEN usGenero = 'F' THEN 'feminino'
        ELSE 'outro'
    END,
    CASE 
        WHEN usEstadoCivil = 'Solteiro' THEN 'solteiro'
        WHEN usEstadoCivil = 'Casado' THEN 'casado'
        WHEN usEstadoCivil = 'Divorciado' THEN 'divorciado'
        WHEN usEstadoCivil = 'Viúvo' THEN 'viuvo'
        ELSE 'solteiro'
    END,
    COALESCE(usNacionalidade, 'brasileira'),
    NULL, -- RG não existe na tabela antiga
    COALESCE(usAtivo, 1) = 1,
    COALESCE(usAceiteLGPD, 0) = 1,
    COALESCE(usSenhaNova, 0) = 1,
    COALESCE(dtInclusao, NOW()),
    COALESCE(dtModificacao, NOW())
FROM capUsuario
WHERE usCPFUsuario IS NOT NULL AND usNome IS NOT NULL;

-- Migrar endereços dos usuários
INSERT INTO user_addresses (
    uuid, user_id, type, cep, street, number, complement, 
    neighborhood, city, state, is_primary, created_at, updated_at
)
SELECT 
    UUID(),
    u.id,
    'residencial',
    REPLACE(REPLACE(cu.usCEP, '-', ''), '.', ''),
    cu.usEndereco,
    cu.usNrEnd,
    cu.usComplEnd,
    cu.usBairro,
    cu.usCidade,
    cu.usUF,
    TRUE,
    NOW(),
    NOW()
FROM capUsuario cu
JOIN users u ON u.cpf = cu.usCPFUsuario
WHERE cu.usEndereco IS NOT NULL;

-- =====================================================
-- 2. MIGRAÇÃO DE PROFISSIONAIS DE SAÚDE
-- =====================================================

-- Migrar profissionais de saúde
INSERT INTO health_professionals (
    uuid, user_id, secondary_contact, cnes, cns, professional_council_id,
    council_registration, council_registration_city, council_registration_state,
    council_issuing_body, active, created_at, updated_at
)
SELECT 
    UUID(),
    u.id,
    ps.psContatoSecundario,
    ps.psCNES,
    ps.psCNS,
    pc.id,
    ps.psCCNrReg,
    ps.psCCCidadeReg,
    ps.psCCUFReg,
    ps.psCCOrgEmissor,
    COALESCE(ps.psAtivo, 1) = 1,
    COALESCE(ps.dtInclusao, NOW()),
    COALESCE(ps.dtModificacao, NOW())
FROM capProfissionalSaude ps
JOIN users u ON u.cpf = ps.psCPF
JOIN professional_councils pc ON pc.code = ps.ccConselhoClasse;

-- Migrar especialidades dos profissionais
INSERT INTO health_professional_specialties (
    uuid, health_professional_id, specialty_id, active, created_at, updated_at
)
SELECT 
    UUID(),
    hp.id,
    s.id,
    TRUE,
    NOW(),
    NOW()
FROM capProfSaudeEspecialidade pse
JOIN health_professionals hp ON hp.user_id = (SELECT id FROM users WHERE cpf = pse.psCPF)
JOIN specialties s ON s.name = pse.esEspecialidade;

-- Migrar dados bancários dos profissionais
INSERT INTO health_professional_bank_accounts (
    uuid, health_professional_id, bank_id, agency, agency_digit, 
    account, account_digit, account_type, account_holder_document,
    is_primary, active, created_at, updated_at
)
SELECT 
    UUID(),
    hp.id,
    b.id,
    ps.psAgenciaBanco,
    ps.psAgenciaDigito,
    ps.psContaCorrente,
    ps.psContaCorrenteDigito,
    'corrente',
    ps.psContaCorrenteCNPJ,
    TRUE,
    TRUE,
    NOW(),
    NOW()
FROM capProfissionalSaude ps
JOIN health_professionals hp ON hp.user_id = (SELECT id FROM users WHERE cpf = ps.psCPF)
LEFT JOIN banks b ON b.code = ps.bcBancoNR
WHERE ps.psContaCorrente IS NOT NULL;

-- Migrar chaves PIX dos profissionais
INSERT INTO health_professional_pix_keys (
    uuid, health_professional_id, pix_type_id, pix_key, is_primary, active, created_at, updated_at
)
SELECT 
    UUID(),
    hp.id,
    pt.id,
    ps.psPIXChave,
    TRUE,
    TRUE,
    NOW(),
    NOW()
FROM capProfissionalSaude ps
JOIN health_professionals hp ON hp.user_id = (SELECT id FROM users WHERE cpf = ps.psCPF)
LEFT JOIN pix_types pt ON pt.code = ps.pxPIXTipo
WHERE ps.psPIXChave IS NOT NULL;

-- =====================================================
-- 3. MIGRAÇÃO DE CLIENTES
-- =====================================================

-- Migrar clientes
INSERT INTO clients (
    uuid, cnpj, company_name, trade_name, contact_name, phone, email, active, created_at, updated_at
)
SELECT 
    UUID(),
    clCliente, -- Assumindo que clCliente é o CNPJ
    clNomeCliente,
    clNomeCliente, -- Usando o mesmo nome como razão social
    clNomeContato,
    clTelefone,
    clEmail,
    COALESCE(clAtivo, 1) = 1,
    COALESCE(dtInclusao, NOW()),
    COALESCE(dtModificacao, NOW())
FROM capCliente
WHERE clCliente IS NOT NULL;

-- Migrar endereços dos clientes
INSERT INTO client_addresses (
    uuid, client_id, type, cep, street, number, complement, 
    neighborhood, city, state, is_primary, created_at, updated_at
)
SELECT 
    UUID(),
    c.id,
    'sede',
    REPLACE(REPLACE(cc.clCEP, '-', ''), '.', ''),
    cc.clEndereco,
    cc.clNrEnd,
    cc.clComplEnd,
    cc.clBairro,
    cc.clCidade,
    cc.clUF,
    TRUE,
    NOW(),
    NOW()
FROM capCliente cc
JOIN clients c ON c.cnpj = cc.clCliente
WHERE cc.clEndereco IS NOT NULL;

-- Migrar usuários dos clientes
INSERT INTO client_users (
    uuid, client_id, user_id, is_manager, active, created_at, updated_at
)
SELECT 
    UUID(),
    c.id,
    u.id,
    COALESCE(cu.ucGestor, 0) = 1,
    TRUE,
    COALESCE(cu.dtinclusao, NOW()),
    NOW()
FROM capClienteUsuario cu
JOIN clients c ON c.cnpj = cu.clCliente
JOIN users u ON u.cpf = cu.usCPFUsuario;

-- =====================================================
-- 4. MIGRAÇÃO DE INSTITUIÇÕES DE SAÚDE
-- =====================================================

-- Migrar instituições de saúde
INSERT INTO health_institutions (
    uuid, cnpj, name, email, ans_code, active, created_at, updated_at
)
SELECT 
    UUID(),
    isInstSaude,
    isNome,
    isEmail,
    isCodANS,
    COALESCE(isAtivo, 1) = 1,
    COALESCE(dtInclusao, NOW()),
    COALESCE(dtModificacao, NOW())
FROM capInstSaude
WHERE isInstSaude IS NOT NULL;

-- Migrar endereços das instituições
INSERT INTO health_institution_addresses (
    uuid, health_institution_id, type, cep, street, number, complement,
    neighborhood, city, state, is_primary, created_at, updated_at
)
SELECT 
    UUID(),
    hi.id,
    'sede',
    REPLACE(REPLACE(cis.isCEP, '-', ''), '.', ''),
    cis.isEndereco,
    cis.isNrEnd,
    cis.isComplEnd,
    cis.isBairro,
    cis.isCidade,
    cis.isUF,
    TRUE,
    NOW(),
    NOW()
FROM capInstSaude cis
JOIN health_institutions hi ON hi.cnpj = cis.isInstSaude
WHERE cis.isEndereco IS NOT NULL;

-- Migrar locais de atendimento
INSERT INTO service_locations (
    uuid, health_institution_id, name, description, cep, street, number,
    complement, neighborhood, city, state, active, created_at, updated_at
)
SELECT 
    UUID(),
    hi.id,
    la.laNome,
    la.laDescricao,
    REPLACE(REPLACE(la.laCEP, '-', ''), '.', ''),
    la.laEndereco,
    la.laNrEnd,
    la.laComplEnd,
    la.laBairro,
    la.laCidade,
    la.laUF,
    TRUE,
    COALESCE(la.dtInclusao, NOW()),
    COALESCE(la.dtModificacao, NOW())
FROM capInstSaudeLocalAtendimento la
JOIN health_institutions hi ON hi.cnpj = la.isInstSaude;

-- =====================================================
-- 5. MIGRAÇÃO DE CONTRATOS
-- =====================================================

-- Migrar contratos
INSERT INTO contracts (
    uuid, contract_number, health_institution_id, client_id, start_date, end_date,
    fixed_value, variable_value, unit_value, status, active, created_at, updated_at
)
SELECT
    UUID(),
    c.ocNrContrato,
    hi.id,
    cl.id,
    c.ocDataContrato,
    c.acDataFimContrato,
    c.ocValorFixo,
    c.ocValorVariavel,
    c.ocValorUnitario,
    'ativo',
    TRUE,
    COALESCE(c.dtInclusao, NOW()),
    COALESCE(c.dtModificacao, NOW())
FROM capInstSaudeContrato c
JOIN health_institutions hi ON hi.cnpj = c.isInstSaude
JOIN clients cl ON cl.cnpj = c.clCliente;

-- Migrar especialidades por contrato
INSERT INTO contract_specialties (
    uuid, contract_id, service_location_id, specialty_id, payment_type_id,
    hourly_rate, monthly_hours, fixed_professional_value, hourly_professional_rate,
    unit_professional_value, discount_rate, payment_day, minimum_days_to_receive,
    active, created_at, updated_at
)
SELECT
    UUID(),
    co.id,
    sl.id,
    sp.id,
    pt.id,
    ce.ceValorHora,
    ce.ceQtdHoraMes,
    ce.ceValorFixoProf,
    ce.ceValorHoraProf,
    ce.ceValorUnitProf,
    ce.ceTaxaDesagio,
    ce.cdDiaRecebimento,
    ce.ceQtdDiasMinRecebim,
    TRUE,
    COALESCE(ce.dtInclusao, NOW()),
    COALESCE(ce.dtModificacao, NOW())
FROM capInstSaudeContratoEspec ce
JOIN contracts co ON co.contract_number = ce.ocNrContrato
    AND co.health_institution_id = (SELECT id FROM health_institutions WHERE cnpj = ce.isInstSaude)
    AND co.client_id = (SELECT id FROM clients WHERE cnpj = ce.clCliente)
JOIN service_locations sl ON sl.health_institution_id = co.health_institution_id AND sl.name = ce.laNome
JOIN specialties sp ON sp.name = ce.esEspecialidade
JOIN payment_types pt ON pt.name = CASE
    WHEN ce.ceTipoPagamento = 'Hora' THEN 'Pagamento por Hora'
    WHEN ce.ceTipoPagamento = 'Fixo' THEN 'Pagamento Fixo'
    WHEN ce.ceTipoPagamento = 'Unitario' THEN 'Pagamento Unitário'
    ELSE 'Pagamento por Hora'
END;

-- =====================================================
-- 6. MIGRAÇÃO DE PLANTÕES
-- =====================================================

-- Migrar plantões
INSERT INTO shifts (
    uuid, shift_number, contract_specialty_id, health_professional_id, approver_user_id,
    publication_date, period_start, period_end, required_hours, worked_hours,
    fixed_value, hourly_rate, unit_value, extra_value_1, extra_value_2,
    access_key, closing_type, closing_date, closing_day, receive_date, advance_date,
    status, active, created_at, updated_at
)
SELECT
    UUID(),
    p.opNrPlantao,
    cs.id,
    hp.id,
    au.id,
    p.opDataDivulgacao,
    p.opPeriodoIni,
    p.opPeriodoFim,
    p.opQtdHorasRequisitada,
    p.opQtdHorasRealizadas,
    p.opValorFixo,
    p.opValorHora,
    p.opValorUnit,
    p.opValorExtra1,
    p.opValorExtra2,
    p.opChaveAcesso,
    CASE
        WHEN p.opTipoFechamento = 'manual' THEN 'manual'
        WHEN p.opTipoFechamento = 'diario' THEN 'diario'
        WHEN p.opTipoFechamento = 'semanal' THEN 'semanal'
        WHEN p.opTipoFechamento = 'mensal' THEN 'mensal'
        ELSE 'manual'
    END,
    p.opDataFechamento,
    p.opDiaFechamento,
    p.opDatarecebimento,
    p.opDataAntecipacao,
    CASE
        WHEN p.opSituacao = 'Aberto' THEN 'aberto'
        WHEN p.opSituacao = 'Solicitado' THEN 'solicitado'
        WHEN p.opSituacao = 'Aprovado' THEN 'aprovado'
        WHEN p.opSituacao = 'Executado' THEN 'executado'
        WHEN p.opSituacao = 'AprovExecucao' THEN 'aprovado_execucao'
        WHEN p.opSituacao = 'Antecipado' THEN 'antecipado'
        ELSE 'aberto'
    END,
    COALESCE(p.opAtivo, 1) = 1,
    COALESCE(p.dtInclusao, NOW()),
    COALESCE(p.dtModificacao, NOW())
FROM capInstSaudePlantao p
JOIN contract_specialties cs ON cs.contract_id = (
    SELECT co.id FROM contracts co
    JOIN health_institutions hi ON hi.id = co.health_institution_id
    JOIN clients cl ON cl.id = co.client_id
    WHERE hi.cnpj = p.isInstSaude AND cl.cnpj = p.clCliente AND co.contract_number = p.ocNrContrato
) AND cs.service_location_id = (
    SELECT sl.id FROM service_locations sl
    JOIN health_institutions hi ON hi.id = sl.health_institution_id
    WHERE hi.cnpj = p.isInstSaude AND sl.name = p.laNome
) AND cs.specialty_id = (
    SELECT sp.id FROM specialties sp WHERE sp.name = p.esEspecialidade
) AND cs.payment_type_id = (
    SELECT pt.id FROM payment_types pt WHERE pt.name = CASE
        WHEN p.ceTipoPagamento = 'Hora' THEN 'Pagamento por Hora'
        WHEN p.ceTipoPagamento = 'Fixo' THEN 'Pagamento Fixo'
        WHEN p.ceTipoPagamento = 'Unitario' THEN 'Pagamento Unitário'
        ELSE 'Pagamento por Hora'
    END
)
LEFT JOIN health_professionals hp ON hp.user_id = (SELECT id FROM users WHERE cpf = p.psCPF)
LEFT JOIN users au ON au.cpf = p.usCPFUsuarioAprovador;

-- Migrar agenda dos plantões
INSERT INTO shift_schedules (
    uuid, shift_id, schedule_date, day_of_week, start_time, end_time, break_time,
    start_datetime, end_datetime, value_type, active, created_at, updated_at
)
SELECT
    UUID(),
    s.id,
    a.agData,
    a.agDiaSem,
    a.agHoraIni,
    a.agHoraFim,
    a.agIntervalo,
    a.agDataIni,
    a.agDataFim,
    CASE
        WHEN a.agTipoValor = 0 THEN 'normal'
        WHEN a.agTipoValor = 1 THEN 'extra1'
        WHEN a.agTipoValor = 2 THEN 'extra2'
        ELSE 'normal'
    END,
    COALESCE(a.agAtivo, 1) = 1,
    COALESCE(a.dtInclusao, NOW()),
    COALESCE(a.dtModificacao, NOW())
FROM capInstSaudePlantaoAgenda a
JOIN shifts s ON s.shift_number = a.opNrPlantao
    AND s.contract_specialty_id IN (
        SELECT cs.id FROM contract_specialties cs
        JOIN contracts co ON co.id = cs.contract_id
        JOIN health_institutions hi ON hi.id = co.health_institution_id
        JOIN clients cl ON cl.id = co.client_id
        JOIN service_locations sl ON sl.id = cs.service_location_id
        JOIN specialties sp ON sp.id = cs.specialty_id
        JOIN payment_types pt ON pt.id = cs.payment_type_id
        WHERE hi.cnpj = a.isInstSaude
        AND cl.cnpj = a.clCliente
        AND co.contract_number = a.ocNrContrato
        AND sl.name = a.laNome
        AND sp.name = a.esEspecialidade
        AND pt.name = CASE
            WHEN a.ceTipoPagamento = 'Hora' THEN 'Pagamento por Hora'
            WHEN a.ceTipoPagamento = 'Fixo' THEN 'Pagamento Fixo'
            WHEN a.ceTipoPagamento = 'Unitario' THEN 'Pagamento Unitário'
            ELSE 'Pagamento por Hora'
        END
    );

-- =====================================================
-- 7. MIGRAÇÃO DE SOLICITAÇÕES DE PLANTÃO
-- =====================================================

-- Migrar solicitações de plantão
INSERT INTO shift_requests (
    uuid, shift_id, health_professional_id, sequence_number, request_date, status, created_at, updated_at
)
SELECT
    UUID(),
    s.id,
    hp.id,
    ps.psSequenciaSolicitacao,
    ps.psDataSolicitacao,
    CASE
        WHEN ps.psSituacao = 'Solicitado' THEN 'solicitado'
        WHEN ps.psSituacao = 'Rejeitado' THEN 'rejeitado'
        WHEN ps.psSituacao = 'Aprovado' THEN 'aprovado'
        WHEN ps.psSituacao = 'Desistente' THEN 'desistente'
        ELSE 'solicitado'
    END,
    ps.psDataSolicitacao,
    NOW()
FROM capInstSaudePlantaoSolicitacao ps
JOIN shifts s ON s.shift_number = ps.opNrPlantao
    AND s.contract_specialty_id IN (
        SELECT cs.id FROM contract_specialties cs
        JOIN contracts co ON co.id = cs.contract_id
        JOIN health_institutions hi ON hi.id = co.health_institution_id
        JOIN clients cl ON cl.id = co.client_id
        JOIN service_locations sl ON sl.id = cs.service_location_id
        JOIN specialties sp ON sp.id = cs.specialty_id
        JOIN payment_types pt ON pt.id = cs.payment_type_id
        WHERE hi.cnpj = ps.isInstSaude
        AND cl.cnpj = ps.clCliente
        AND co.contract_number = ps.ocNrContrato
        AND sl.name = ps.laNome
        AND sp.name = ps.esEspecialidade
        AND pt.name = CASE
            WHEN ps.ceTipoPagamento = 'Hora' THEN 'Pagamento por Hora'
            WHEN ps.ceTipoPagamento = 'Fixo' THEN 'Pagamento Fixo'
            WHEN ps.ceTipoPagamento = 'Unitario' THEN 'Pagamento Unitário'
            ELSE 'Pagamento por Hora'
        END
    )
JOIN health_professionals hp ON hp.user_id = (SELECT id FROM users WHERE cpf = ps.psCPF);

-- =====================================================
-- 8. MIGRAÇÃO DE ANTECIPAÇÕES
-- =====================================================

-- Migrar instituições financeiras
INSERT INTO financial_institutions (
    uuid, cnpj, name, phone, cep, street, number, complement, neighborhood, city, state, active, created_at, updated_at
)
SELECT
    UUID(),
    ifInstFinanceira,
    ifNome,
    ifTelefone,
    REPLACE(REPLACE(ifCEP, '-', ''), '.', ''),
    ifEndereco,
    ifNrEnd,
    ifComplEnd,
    ifBairro,
    ifCidade,
    ifUF,
    COALESCE(isAtivo, 1) = 1,
    COALESCE(dtInclusao, NOW()),
    COALESCE(dtModificacao, NOW())
FROM capInstFinanceira;

-- Migrar antecipações
INSERT INTO advances (
    uuid, advance_number, health_professional_id, financial_institution_id, request_date,
    approval_date, approver_name, approver_user_id, requested_amount, approved_amount,
    net_amount, gross_amount, advance_rate, receive_date, kuara_id, kuara_status,
    kuara_payment_date, approval_token, envelope_id, status, created_at, updated_at
)
SELECT
    UUID(),
    a.anNrAntecipacao,
    hp.id,
    fi.id,
    a.anDataSolicitacao,
    a.anDataAprovacao,
    a.icNomeAprovador,
    au.id,
    a.anValorSolicitado,
    a.anValorAprovado,
    a.anValorLiquido,
    a.anValorBruto,
    a.anTaxaAntecipacao,
    a.opDataRecebimento,
    a.anKuaraId,
    a.anKuaraStatus,
    a.anKuaraDataPagamento,
    a.anTokenAprovacao,
    a.idEnvelope,
    CASE
        WHEN a.anDataAprovacao IS NOT NULL AND a.anKuaraStatus = 'paid' THEN 'pago'
        WHEN a.anDataAprovacao IS NOT NULL THEN 'aprovado'
        ELSE 'solicitado'
    END,
    COALESCE(a.dtInclusao, NOW()),
    COALESCE(a.dtModificacao, NOW())
FROM capAntecipacao a
JOIN health_professionals hp ON hp.user_id = (SELECT id FROM users WHERE cpf = a.psCPF)
JOIN financial_institutions fi ON fi.cnpj = a.ifInstFinanceira
LEFT JOIN users au ON au.cpf = a.usCPFUsuarioAprovador;

-- Migrar histórico de antecipações
INSERT INTO advance_status_history (
    uuid, advance_id, new_status, change_date, created_at
)
SELECT
    UUID(),
    ad.id,
    ah.anKuaraStatus,
    ah.dtInclusao,
    ah.dtInclusao
FROM capAntecipacaoHistorico ah
JOIN advances ad ON ad.advance_number = ah.anNrAntecipacao;

-- =====================================================
-- 9. FINALIZAÇÃO
-- =====================================================

-- Reativar verificações de foreign key
SET FOREIGN_KEY_CHECKS = 1;

-- Atualizar sequências para próximos valores
UPDATE protocol_numbers SET protocol_number = (
    SELECT COALESCE(MAX(CAST(dsProtocolo AS UNSIGNED)), 0) + 1
    FROM capOuvidoria
    WHERE dsProtocolo REGEXP '^[0-9]+$'
);

-- Mensagem de conclusão
SELECT 'Migração concluída com sucesso!' as status;
