// =====================================================
// SETTINGS PAGE - PÁGINA DE CONFIGURAÇÕES
// Configurações do sistema e preferências
// =====================================================

import React from 'react'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'
import { Settings as SettingsIcon, Save, RefreshCw } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

export function Settings() {
  const { t } = useTranslation()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-heading flex items-center space-x-3">
            <SettingsIcon className="w-8 h-8 text-primary-600" />
            <span>{t('settings.title')}</span>
          </h1>
          <p className="text-muted mt-1">
            {t('settings.subtitle')}
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            icon={<RefreshCw className="w-5 h-5" />}
          >
            Restaurar Padrões
          </Button>
          <Button
            variant="primary"
            icon={<Save className="w-5 h-5" />}
          >
            Salvar Alterações
          </Button>
        </div>
      </div>

      {/* Coming Soon */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-12"
      >
        <Card className="max-w-md mx-auto">
          <CardContent className="py-12">
            <SettingsIcon className="w-16 h-16 text-primary-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-heading mb-2">
              Em Desenvolvimento
            </h3>
            <p className="text-muted">
              A página de configurações está sendo desenvolvida e estará disponível em breve.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
