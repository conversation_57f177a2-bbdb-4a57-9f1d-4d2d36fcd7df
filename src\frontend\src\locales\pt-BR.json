{"common": {"loading": "Carregando...", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Excluir", "edit": "<PERSON><PERSON>", "view": "Visualizar", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON><PERSON>", "filter": "Filtrar", "clear": "Limpar", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "yes": "<PERSON>m", "no": "Não", "ok": "OK", "back": "Voltar", "next": "Próximo", "previous": "Anterior", "first": "<PERSON><PERSON>", "last": "Último", "page": "<PERSON><PERSON><PERSON><PERSON>", "of": "de", "items": "itens", "total": "Total", "actions": "Ações", "status": "Status", "active": "Ativo", "inactive": "Inativo", "enabled": "Habilitado", "disabled": "Desabilitado", "required": "Obrigatório", "optional": "Opcional", "select": "Selecionar", "selectAll": "Selecionar Todos", "none": "<PERSON><PERSON><PERSON>", "all": "Todos", "today": "Hoje", "yesterday": "Ontem", "tomorrow": "Amanhã", "thisWeek": "<PERSON><PERSON>", "thisMonth": "<PERSON><PERSON>", "thisYear": "<PERSON><PERSON>", "export": "Exportar", "import": "Importar", "download": "Baixar", "upload": "Enviar", "print": "Imprimir", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "reset": "<PERSON><PERSON><PERSON>", "copy": "Copiar", "paste": "Colar", "cut": "Recortar", "undo": "<PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON>", "help": "<PERSON><PERSON><PERSON>", "about": "Sobre", "settings": "Configurações", "profile": "Perfil", "logout": "<PERSON><PERSON>", "login": "Entrar", "register": "Cadastrar", "forgotPassword": "<PERSON><PERSON><PERSON> a Senha", "changePassword": "<PERSON><PERSON><PERSON>", "newPassword": "Nova Senha", "confirmPassword": "Confirmar <PERSON>", "currentPassword": "<PERSON><PERSON>", "email": "E-mail", "password": "<PERSON><PERSON>", "name": "Nome", "phone": "Telefone", "address": "Endereço", "city": "Cidade", "state": "Estado", "zipCode": "CEP", "country": "<PERSON><PERSON>", "date": "Data", "time": "<PERSON><PERSON>", "dateTime": "Data e Hora", "startDate": "Data Inicial", "endDate": "Data Final", "createdAt": "C<PERSON><PERSON> em", "updatedAt": "Atualizado em", "description": "Descrição", "notes": "Observações", "comments": "Comentários", "tags": "Tags", "category": "Categoria", "type": "Tipo", "priority": "Prioridade", "high": "Alta", "medium": "Média", "low": "Baixa", "urgent": "Urgente", "normal": "Normal", "success": "Sucesso", "error": "Erro", "warning": "Aviso", "info": "Informação", "notification": "Notificação", "message": "Mensagem", "title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Subtítulo", "content": "<PERSON><PERSON><PERSON><PERSON>", "image": "Imagem", "file": "Arquivo", "document": "Documento", "link": "Link", "url": "URL", "website": "Site", "version": "Vers<PERSON>", "size": "<PERSON><PERSON><PERSON>", "format": "Formato", "language": "Idioma", "theme": "<PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON>", "dark": "Escuro", "system": "Sistema", "auto": "Automático", "manual": "Manual"}, "navigation": {"dashboard": "Dashboard", "users": "Usuários", "professionals": "Profissionais", "clients": "Clientes", "shifts": "Plantões", "advances": "Antecipações", "whatsapp": "WhatsApp", "audit": "Auditoria", "reports": "Relatórios", "settings": "Configurações", "administration": "Administração", "financial": "Financeiro", "medical": "Médico", "communication": "Comunicação", "security": "Segurança", "compliance": "Compliance"}, "auth": {"login": "Entrar", "logout": "<PERSON><PERSON>", "loginTitle": "Acesse sua conta", "loginSubtitle": "Entre com suas credenciais para acessar o sistema", "cpf": "CPF", "password": "<PERSON><PERSON>", "rememberMe": "<PERSON><PERSON><PERSON> de mim", "forgotPassword": "Esqueceu sua senha?", "loginButton": "Entrar", "loginSuccess": "Login realizado com sucesso!", "loginError": "CPF ou senha incorretos", "logoutSuccess": "Logout realizado com sucesso!", "sessionExpired": "Sessão expirada. Faça login novamente.", "unauthorized": "Você não tem permissão para acessar esta página", "invalidCredentials": "Credenciais inválidas", "accountLocked": "Conta bloqueada. Entre em contato com o administrador.", "passwordExpired": "Sua senha expirou. É necessário alterá-la.", "changePassword": "<PERSON><PERSON><PERSON>", "currentPassword": "<PERSON><PERSON>", "newPassword": "Nova Senha", "confirmNewPassword": "Confirmar <PERSON>", "passwordChanged": "Senha alterada com sucesso!", "passwordMismatch": "As senhas não coincidem", "weakPassword": "A senha deve ter pelo menos 8 caracteres", "forgotPasswordTitle": "<PERSON><PERSON><PERSON><PERSON>", "forgotPasswordSubtitle": "Digite seu e-mail para receber as instruções", "sendResetLink": "Enviar Link de Recuperação", "resetLinkSent": "Link de recuperação enviado para seu e-mail", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "resetPasswordSuccess": "Senha redefinida com sucesso!", "invalidResetToken": "Token de recuperação inválido ou expirado"}, "dashboard": {"title": "Dashboard", "welcome": "Bem-vindo ao Sistema GS2", "overview": "Visão Geral", "statistics": "Estatísticas", "recentActivity": "Atividade Recente", "quickActions": "Ações <PERSON>", "totalUsers": "Total de Usuários", "activeUsers": "Usuários Ativos", "totalClients": "Total de Clientes", "activeClients": "Clientes Ativos", "totalProfessionals": "Total de Profissionais", "activeProfessionals": "Profissionais Ativos", "totalShifts": "Total de Plantões", "openShifts": "<PERSON><PERSON><PERSON>", "approvedShifts": "<PERSON><PERSON><PERSON>", "executedShifts": "Plantões Executados", "totalAdvances": "Total de Antecipações", "pendingAdvances": "Antecipações Pendentes", "approvedAdvances": "Antecipaç<PERSON><PERSON>", "totalAdvanceAmount": "Valor Total Antecipado", "monthlyGrowth": "Crescimento Mensal", "weeklyGrowth": "Crescimento Semanal", "dailyGrowth": "Crescimento Diário", "noData": "Nenhum dado disponível", "loadingStats": "Carregando estatísticas...", "errorLoadingStats": "Erro ao carregar estatísticas", "refreshStats": "Atualizar Estatísticas"}, "users": {"title": "Usuários", "subtitle": "Gerenciar usuários do sistema", "createUser": "<PERSON><PERSON><PERSON>", "editUser": "<PERSON><PERSON>", "deleteUser": "Excluir <PERSON>", "userDetails": "Detalhes do Usuário", "userCreated": "Usuário criado com sucesso!", "userUpdated": "Usuário atualizado com sucesso!", "userDeleted": "Usuário excluído com sucesso!", "confirmDelete": "Tem certeza que deseja excluir este usuário?", "deleteWarning": "Esta ação não pode ser desfeita.", "cpf": "CPF", "name": "Nome", "email": "E-mail", "phone": "Telefone", "active": "Ativo", "groups": "Grupos", "permissions": "Permissões", "createdAt": "C<PERSON><PERSON> em", "updatedAt": "Atualizado em", "lastLogin": "<PERSON><PERSON><PERSON>", "status": "Status", "actions": "Ações", "searchUsers": "Pesquisar usuá<PERSON>...", "filterByGroup": "Filtrar por grupo", "filterByStatus": "Filtrar por status", "allGroups": "Todos os Grupos", "allStatuses": "Todos os Status", "noUsersFound": "Nenhum usuário encontrado", "loadingUsers": "Carregando usuários...", "errorLoadingUsers": "Erro ao carregar usuários", "invalidCpf": "CPF inválido", "invalidEmail": "E-mail inválido", "cpfAlreadyExists": "CPF já cadastrado", "emailAlreadyExists": "E-mail já cadastrado"}, "professionals": {"title": "Profissionais de Saúde", "subtitle": "Gerenciar profissionais de saúde", "createProfessional": "Criar Profissional", "editProfessional": "Editar Profissional", "deleteProfessional": "Excluir Profissional", "professionalDetails": "Detalhes do Profissional", "professionalCreated": "Profissional criado com sucesso!", "professionalUpdated": "Profissional atualizado com sucesso!", "professionalDeleted": "Profissional excluído com sucesso!", "crm": "CRM", "specialty": "Especialidade", "specialties": "Especialidades", "pixKey": "Chave PIX", "bankAccount": "Conta Bancária", "bankCode": "Código do Banco", "bankName": "Nome do Banco", "agency": "Agência", "account": "Conta", "accountType": "Tipo de Conta", "accountHolder": "Titular da Conta", "checking": "Conta Corrente", "savings": "Poupança", "searchProfessionals": "Pesquisar profissionais...", "filterBySpecialty": "Filtrar por especialidade", "allSpecialties": "<PERSON><PERSON> as Especial<PERSON><PERSON>", "noProfessionalsFound": "Nenhum profissional encontrado", "loadingProfessionals": "Carregando profissionais...", "errorLoadingProfessionals": "Erro ao carregar profissionais"}, "clients": {"title": "Clientes", "subtitle": "Gerenciar clientes e instituições", "createClient": "<PERSON><PERSON><PERSON>", "editClient": "<PERSON><PERSON>", "deleteClient": "Excluir Cliente", "clientDetails": "Detalhes do Cliente", "clientCreated": "Cliente criado com sucesso!", "clientUpdated": "Cliente atualizado com sucesso!", "clientDeleted": "Cliente excluído com sucesso!", "cnpj": "CNPJ", "companyName": "Razão Social", "tradeName": "Nome Fantasia", "contactName": "Nome do Contato", "phone": "Telefone", "email": "E-mail", "address": "Endereço", "contracts": "Contratos", "activeContracts": "Contratos Ativos", "searchClients": "Pesquisar clientes...", "noClientsFound": "Nenhum cliente encontrado", "loadingClients": "Carregando clientes...", "errorLoadingClients": "Erro ao carregar clientes", "invalidCnpj": "CNPJ inválido", "cnpjAlreadyExists": "CNPJ j<PERSON> cadastrado"}, "shifts": {"title": "Plantões", "subtitle": "Gerenciar plantões e escalas", "createShift": "<PERSON><PERSON><PERSON>", "editShift": "<PERSON><PERSON>", "deleteShift": "Excluir Plantão", "shiftDetails": "Detalhes do Plantão", "shiftCreated": "Plantão criado com sucesso!", "shiftUpdated": "Plantão atualizado com sucesso!", "shiftDeleted": "Plantão excluído com sucesso!", "shiftNumber": "Número do Plantão", "professional": "Profissional", "client": "Cliente", "specialty": "Especialidade", "publicationDate": "Data de Publicação", "periodStart": "Início do Período", "periodEnd": "Fim do Período", "requiredHours": "<PERSON><PERSON>", "workedHours": "<PERSON><PERSON> Trabalhadas", "fixedValue": "Valor Fixo", "hourlyRate": "Valor por Hora", "unitValue": "Valor <PERSON>", "extraValue": "Valor Extra", "accessKey": "<PERSON><PERSON>", "closingType": "Tipo de Fechamento", "closingDate": "Data de Fechamento", "receiveDate": "Data de Recebimento", "status": "Status", "open": "Abe<PERSON>o", "requested": "Solicitado", "approved": "<PERSON><PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "executed": "Executado", "closed": "<PERSON><PERSON><PERSON>", "manual": "Manual", "automatic": "Automático", "requestShift": "Solicitar Plantão", "approveShift": "<PERSON><PERSON><PERSON>", "rejectShift": "Rejeitar <PERSON>", "executeShift": "Executar Plantão", "closeShift": "<PERSON><PERSON><PERSON>", "searchShifts": "Pesquisar plantões...", "filterByStatus": "Filtrar por status", "filterByProfessional": "Filtrar por profissional", "filterByClient": "Filtrar por cliente", "allStatuses": "Todos os Status", "allProfessionals": "Todos os Profissionais", "allClients": "Todos os Clientes", "noShiftsFound": "Nenhum plantão encontrado", "loadingShifts": "Carregando plantões...", "errorLoadingShifts": "Erro ao carregar plantões"}, "advances": {"title": "Antecipações", "subtitle": "Gerenciar antecipações de recebíveis", "createAdvance": "Solicitar Antecipação", "editAdvance": "Editar <PERSON>", "deleteAdvance": "Excluir Antecipação", "advanceDetails": "Detalhes da Antecipação", "advanceCreated": "Antecipação solicitada com sucesso!", "advanceUpdated": "Antecipação atualizada com sucesso!", "advanceDeleted": "Antecipação excluída com sucesso!", "advanceNumber": "Número da Antecipação", "professional": "Profissional", "financialInstitution": "Instituição Financeira", "requestedAmount": "<PERSON>or <PERSON>", "approvedAmount": "<PERSON><PERSON>", "netAmount": "<PERSON><PERSON>", "advanceRate": "Taxa de Antecipação", "requestDate": "Data da Solicitação", "approvalDate": "Data da Aprovação", "receiveDate": "Data de Recebimento", "approver": "<PERSON><PERSON><PERSON>", "notes": "Observações", "status": "Status", "requested": "Solicitado", "analyzing": "<PERSON>", "approved": "<PERSON><PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "processing": "Processando", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancelled": "Cancelado", "approveAdvance": "<PERSON><PERSON><PERSON>", "rejectAdvance": "Reje<PERSON>r <PERSON>", "calculateAvailable": "Calcular Disponível", "availableAmount": "<PERSON>or <PERSON>", "searchAdvances": "Pesquisar antecipaç<PERSON>es...", "filterByStatus": "Filtrar por status", "filterByProfessional": "Filtrar por profissional", "filterByInstitution": "Filtrar por instituição", "allStatuses": "Todos os Status", "allProfessionals": "Todos os Profissionais", "allInstitutions": "<PERSON><PERSON> as Institui<PERSON><PERSON><PERSON>", "noAdvancesFound": "Nenhuma antecipação encontrada", "loadingAdvances": "Carregando antecipações...", "errorLoadingAdvances": "Erro ao carregar antecipações"}, "whatsapp": {"title": "WhatsApp", "subtitle": "Gerenciar mensagens e comunicação", "messages": "Mensagens", "conversations": "Conversas", "templates": "Templates", "sendMessage": "Enviar Mensagem", "sendTemplate": "Enviar Template", "messageDetails": "<PERSON><PERSON><PERSON> da Mensagem", "conversationHistory": "Histórico da Conversa", "phoneNumber": "Número de Telefone", "messageDate": "Data da Mensagem", "direction": "Direção", "messageType": "Tipo de Mensagem", "messageText": "Texto da Mensagem", "payload": "Payload", "sent": "Enviada", "received": "<PERSON><PERSON>bid<PERSON>", "text": "Texto", "image": "Imagem", "document": "Documento", "audio": "<PERSON><PERSON><PERSON>", "video": "Vídeo", "searchMessages": "Pesquisar mensagens...", "filterByDirection": "Filtrar por direção", "filterByType": "Filtrar por tipo", "allDirections": "<PERSON><PERSON> as <PERSON>reç<PERSON><PERSON>", "allTypes": "Todos os Tipos", "noMessagesFound": "Nenhuma mensagem encontrada", "loadingMessages": "Carregando mensagens...", "errorLoadingMessages": "Erro ao carregar mensagens", "messageSent": "Mensagem enviada com sucesso!", "errorSendingMessage": "Erro ao enviar mensagem"}, "audit": {"title": "Auditoria", "subtitle": "Logs de auditoria e compliance", "auditLogs": "Logs de Auditoria", "complianceReports": "Relatórios de Compliance", "securityEvents": "Eventos de Segurança", "userActivity": "Atividade do Usuário", "resourceTrail": "Trilha do Recurso", "generateReport": "<PERSON><PERSON><PERSON>", "exportLogs": "Exportar Logs", "operationType": "Tipo de Operação", "resourceType": "Tipo de Recurso", "user": "<PERSON><PERSON><PERSON><PERSON>", "ipAddress": "Endereço IP", "userAgent": "User Agent", "success": "Sucesso", "complianceLevel": "Nível de Compliance", "actionDescription": "Descrição da Ação", "oldValues": "Valores <PERSON>", "newValues": "Valores Novos", "timestamp": "Data/Hora", "standard": "Padrão", "high": "Alto", "critical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "read": "<PERSON>r", "update": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Excluir", "login": "<PERSON><PERSON>", "logout": "Logout", "approve": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON><PERSON>", "export": "Exportar", "searchLogs": "Pesquisar logs...", "filterByOperation": "Filtrar por operação", "filterByResource": "Filtrar por recurso", "filterByUser": "Filtrar por usuário", "filterByLevel": "Filtrar por nível", "allOperations": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>", "allResources": "Todos os Recursos", "allUsers": "Todos os Usuários", "allLevels": "Todos os Níveis", "noLogsFound": "Nenhum log encontrado", "loadingLogs": "Carregando logs...", "errorLoadingLogs": "Erro ao carregar logs", "lgpdReport": "Relatório LGPD", "soxReport": "Relatório SOX", "hipaaReport": "Relatório HIPAA", "reportGenerated": "Relatório gerado com sucesso!", "errorGeneratingReport": "Erro ao gerar relatório"}, "settings": {"title": "Configurações", "subtitle": "Configurações do sistema e preferências", "general": "G<PERSON>", "appearance": "Aparência", "notifications": "Notificações", "security": "Segurança", "integrations": "Integrações", "backup": "Backup", "theme": "<PERSON><PERSON>", "language": "Idioma", "timezone": "<PERSON><PERSON>", "dateFormat": "Formato de Data", "timeFormat": "Formato de Hora", "currency": "<PERSON><PERSON>", "lightTheme": "<PERSON><PERSON>", "darkTheme": "<PERSON><PERSON>", "systemTheme": "Tema do Sistema", "portuguese": "Português", "english": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spanish": "Espanhol", "emailNotifications": "Notificações por E-mail", "pushNotifications": "Notificaçõ<PERSON>", "smsNotifications": "Notificações por SMS", "twoFactorAuth": "Autenticação de Dois Fatores", "sessionTimeout": "Timeout <PERSON>ão", "passwordPolicy": "Política de Senhas", "whatsappIntegration": "Integração WhatsApp", "kuaraIntegration": "Integração <PERSON>", "backupFrequency": "Frequência de Backup", "backupRetention": "Retenção de Backup", "settingsSaved": "Configurações salvas com sucesso!", "errorSavingSettings": "Erro ao salvar configurações"}, "errors": {"generic": "Ocorreu um erro inesperado", "network": "Erro de conexão. Verifique sua internet.", "server": "Erro interno do servidor", "notFound": "Página não encontrada", "unauthorized": "Acesso não autorizado", "forbidden": "<PERSON><PERSON>", "validation": "Erro de validação", "required": "Este campo é obrigatório", "invalid": "Valor <PERSON>", "tooShort": "<PERSON><PERSON> curto", "tooLong": "<PERSON><PERSON> longo", "invalidFormat": "Formato inválido", "invalidEmail": "E-mail inválido", "invalidCpf": "CPF inválido", "invalidCnpj": "CNPJ inválido", "invalidPhone": "Telefone inválido", "invalidDate": "Data inválida", "invalidTime": "<PERSON><PERSON>", "invalidUrl": "URL inválida", "passwordTooWeak": "Senha muito fraca", "passwordMismatch": "<PERSON><PERSON> n<PERSON>m", "fileTooBig": "Arquivo muito grande", "fileTypeNotAllowed": "Tipo de arquivo não permitido", "uploadFailed": "Falha no upload", "downloadFailed": "Falha no download", "copyFailed": "Falha ao copiar", "deleteFailed": "Falha ao excluir", "saveFailed": "<PERSON>alha ao salvar", "loadFailed": "Falha ao carregar", "timeout": "Tempo limite excedido", "offline": "Você está offline", "maintenance": "Sistema em manutenção"}, "success": {"saved": "Salvo com sucesso!", "created": "Criado com sucesso!", "updated": "Atualizado com sucesso!", "deleted": "Excluído com sucesso!", "uploaded": "Enviado com sucesso!", "downloaded": "Baixado com sucesso!", "copied": "Copiado com sucesso!", "sent": "Enviado com sucesso!", "imported": "Importado com sucesso!", "exported": "Exportado com sucesso!", "synchronized": "Sincronizado com sucesso!", "backup": "Backup realizado com sucesso!", "restore": "Restauração realizada com sucesso!"}}