$ curl -X POST \
  https://gtem2bzgonubuszlpbdsqzmw3y0walks.lambda-url.us-east-1.on.aws \
  -H 'Content-Type: application/json' \
  -H 'Apollo-Require-Preflight: true' \
  -d '{
    "query": "mutation Login($email: String!, $password: String!) { login(email: $email, password: $password) { token user { id email name } } }",
    "variables": {
      "email": "<EMAIL>",
      "password": "123123123"
    }
  }'
{"data":{"login":{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.o9Dj8pSf_x5YLF7q1ZFTrqScyRhHuWWIde8jWmfDEvg","user":{"id":"550e8400-e29b-41d4-a716-446655440000","email":"<EMAIL>","name":"Administrador do Sistema"}}}}



 curl -X POST \
    https://gtem2bzgonubuszlpbdsqzmw3y0walks.lambda-url.us-east-1.on.aws \
    -H 'Content-Type: application/json' \
    -d '{
      "query": "mutation Login($email: String!, $password: String!) { login(email: $email, password: $password) { token user { id email name } } }",
      "variables": {
        "email": "<EMAIL>",
        "password": "123123123"
      }
    }'