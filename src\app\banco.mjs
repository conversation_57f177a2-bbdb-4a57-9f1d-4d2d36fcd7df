import express from 'express';
import { dbQuery } from 'capfunctions';

const router = express.Router();
const LIMIT = 100;

// Helper: List tables using SHOW TABLES query.
async function listTables() {
  const result = await dbQuery('SHOW TABLES');
  const key = Object.keys(result[0])[0];
  return result.map((row) => row[key]);
}

// Helper: List data from a given table with optional search and pagination.
async function listTableData(table, page = 0, search = '', customWhere = '') {
  const offset = page * LIMIT;
  let whereClause = '';
  const params = [];

  if (customWhere && customWhere.trim() !== '') {
    whereClause = 'WHERE ' + customWhere;
  }
  
  if (search) {
    const cols = await dbQuery(
      `SELECT COLUMN_NAME 
       FROM INFORMATION_SCHEMA.COLUMNS 
       WHERE TABLE_SCHEMA = DATABASE() 
       AND TABLE_NAME = ? 
       AND DATA_TYPE IN ('varchar','char','text')`,
      [table]
    );
    if (cols.length > 0) {
      const conditions = cols.map((row) => {
        params.push(`%${search}%`);
        return `${row.COLUMN_NAME} LIKE ?`;
      });
      whereClause = 'WHERE ' + conditions.join(' OR ');
    }
  }
  params.push(offset);
  const data = await dbQuery(
    `SELECT * FROM \`${table}\` ${whereClause} LIMIT ${LIMIT} OFFSET ?`,
    params
  );
  return data;
}

// Updated renderHtml function to improve JSON display.
function renderHtml(tables) {
  return `
  <!DOCTYPE html>
  <html>
  <head>
    <meta charset="utf-8">
    <title>Banco - Lista de Tabelas (Dracula Theme)</title>
    <style>
      body { background: #282a36; color: #f8f8f2; font-family: Arial, sans-serif; margin: 20px; }
      .container { display: flex; }
      .sidebar { width: 20%; border-right: 1px solid #6272a4; padding-right: 10px; }
      .content { width: 80%; padding-left: 10px; }
      ul { list-style: none; padding: 0; }
      li { padding: 10px; border-bottom: 1px solid #6272a4; cursor: pointer; }
      li:hover { background-color: #44475a; }
      .search { margin-bottom: 10px; }
      .table-filter { margin: 10px 0; }
      input { background: #44475a; border: 1px solid #6272a4; color: #f8f8f2; padding: 8px; border-radius: 3px; }
      button { background: #6272a4; border: none; color: #f8f8f2; padding: 8px 12px; border-radius: 3px; cursor: pointer; }
      button:hover { background: #50fa7b; }
      .pagination button { margin: 5px; }
      ul#tables { max-height: 80vh; overflow-y: auto; }
      #tableResults { max-height: 80vh; overflow-y: auto; }
      pre {
        font-family: 'Cascadia Code', monospace;
        font-size: 16px;
        line-height: 1.4;
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }
      /* Syntax highlighting for JSON */
      .json-key { color: #8be9fd; }
      .json-string { color: #f1fa8c; }
      .json-number { color: #bd93f9; }
      .json-boolean { color: #ff79c6; }
      .json-null { color: #6272a4; }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="sidebar">
        <h1>Tabelas</h1>
        <div class="table-filter">
          <input id="tableFilterInput" placeholder="Filtrar tabelas..." onkeyup="filterTables()" />
        </div>
        <ul id="tables">
          ${tables.map((t) => `<li onclick="loadTableData('${t}', 0, '')">${t}</li>`).join('')}
        </ul>
      </div>
      <div class="content">
        <div class="search">
          <input id="searchInput" placeholder="Pesquisar..." />
          <button onclick="reloadCurrentTable()">Pesquisar</button>
          <button onclick="toggleViewMode()">Alternar Vista (Atual: <span id='viewModeLabel'>JSON</span>)</button>
        </div>
        <div id="tableData">
          <h2>Selecione uma tabela para ver os dados</h2>
        </div>
      </div>
    </div>
    <script>
      let viewMode = "json"; // default view mode

      // Function to update URL parameters (always sets view=html)
      function updateUrl(table, page, search) {
        const params = new URLSearchParams();
        if (table) params.set("table", table);
        if (page) params.set("page", page);
        if (search) params.set("search", search);
        // Ensure full interface view on reload
        params.set("view", "html");
        window.history.pushState({}, "", "?" + params.toString());
      }
      
      // Function to highlight JSON syntax
      function jsonHighlight(json) {
        if (typeof json != 'string') {
          json = JSON.stringify(json, null, 2);
        }
        json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
        return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\\s*:)?|\b(true|false|null)\b|\b-?\\d+(\\.\\d+)?\b)/g, function (match) {
          let cls = 'json-number';
          if (/^"/.test(match)) {
            cls = /:$/.test(match) ? 'json-key' : 'json-string';
          } else if (/true|false/.test(match)) {
            cls = 'json-boolean';
          } else if (/null/.test(match)) {
            cls = 'json-null';
          }
          return '<span class="'+cls+'">'+match+'</span>';
        });
      }
      
      function toggleViewMode() {
        viewMode = viewMode === "json" ? "table" : "json";
        document.getElementById("viewModeLabel").textContent = viewMode.toUpperCase();
        // Reload current table with new view mode
        if(currentTable) {
          loadTableData(currentTable, currentPage, document.getElementById('searchInput').value);
        }
      }

      let currentTable = '';
      let currentPage = 0;
      function filterTables() {
        const filter = document.getElementById('tableFilterInput').value.toLowerCase();
        const nodes = document.querySelectorAll('#tables li');
        nodes.forEach(node => {
          const text = node.textContent.toLowerCase();
          node.style.display = text.includes(filter) ? "block" : "none";
        });
      }
      function reloadCurrentTable() {
        loadTableData(currentTable, 0, document.getElementById('searchInput').value);
      }
      async function loadTableData(table, page = 0, search = '') {
        currentTable = table;
        currentPage = page;
        updateUrl(table, page, search); // update URL with view=html
        
        // Call the AJAX endpoint by adding ajax=true so that the router returns JSON
        const res = await fetch(\`/banco?ajax=true\&table=\${table}&page=\${page}&search=\${encodeURIComponent(search)}\`);
        const json = await res.json();
        let content = '<h2>Dados da Tabela: ' + table + '</h2>';
        
        if(viewMode === "json") {
          const highlighted = jsonHighlight(json.data);
          content += '<div id="tableResults"><pre>' + highlighted + '</pre></div>';
        } else {
          // Generate a simple HTML table from the JSON array
          if(json.data.length > 0) {
            // Get table headers from keys of first object
            const headers = Object.keys(json.data[0]);
            let tableHtml = '<table border="1" cellpadding="5" cellspacing="0" style="width:100%; border-collapse: collapse;"><thead><tr>';
            headers.forEach(header => {
              tableHtml += \`<th>\${header}</th>\`;
            });
            tableHtml += '</tr></thead><tbody>';
            json.data.forEach(row => {
              tableHtml += '<tr>';
              headers.forEach(header => {
                tableHtml += \`<td>\${row[header] ?? ""}</td>\`;
              });
              tableHtml += '</tr>';
            });
            tableHtml += '</tbody></table>';
            content += '<div id="tableResults">' + tableHtml + '</div>';
          } else {
            content += '<div id="tableResults"><p>Nenhum dado encontrado.</p></div>';
          }
        }
        
        content += '<div class="pagination">';
        if (page > 0) {
          content += '<button onclick="loadTableData(\\''+table+'\\', '+(page-1)+', document.getElementById(\\'searchInput\\').value)">Anterior</button>';
        }
        if (json.data.length === ${LIMIT}) {
          content += '<button onclick="loadTableData(\\''+table+'\\', '+(page+1)+', document.getElementById(\\'searchInput\\').value)">Próxima</button>';
        }
        content += '</div>';
        document.getElementById('tableData').innerHTML = content;
      }
      
      // On page load, check URL parameters and load table if present
      window.addEventListener("load", () => {
        const params = new URLSearchParams(window.location.search);
        const table = params.get("table");
        const page = Number(params.get("page")) || 0;
        const search = params.get("search") || "";
        if (table) {
          // Set search input value if available
          document.getElementById('searchInput').value = search;
          loadTableData(table, page, search);
        }
      });
      
    </script>
  </body>
  </html>
  `;
}

// GET /banco route handler.
router.get('/', async (req, res) => {
  try {
    const query = req.query;
    // JSON API: if a table name is provided, list its data.
    if (query.table && query.ajax) {
      const page = Number(query.page) || 0;
      const search = query.search || '';
      const customWhere = query.whereScript || '';
      const data = await listTableData(query.table, page, search, customWhere);
      return res.json({ data, page, limit: LIMIT });
    }
    // HTML view if view=html or Accept header includes text/html.
    if (query.view === 'html' || (req.headers.accept && req.headers.accept.includes('text/html'))) {
      const tables = await listTables();
      return res.send(renderHtml(tables));
    }
    // Otherwise, return JSON list of tables.
    const tables = await listTables();
    return res.json({ tables });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Erro ao processar a requisição' });
  }
});

export default router;
