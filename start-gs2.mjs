#!/usr/bin/env node

// =====================================================
// START GS2 - SCRIPT PRINCIPAL DO SISTEMA
// Script para iniciar frontend e API em desenvolvimento
// =====================================================

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import net from 'net';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log(`
🏥 Sistema GS2 - Gestão de Saúde e Plantões
==========================================
🚀 Iniciando ambiente de desenvolvimento...
`);

// Função para verificar se porta está em uso
function isPortInUse(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.listen(port, () => {
      server.once('close', () => resolve(false));
      server.close();
    });
    
    server.on('error', () => resolve(true));
  });
}

// Função para executar comando
function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`📦 Executando: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with code ${code}`));
      }
    });

    child.on('error', reject);
  });
}

async function setupEnvironment() {
  console.log('🔧 Configurando ambiente...\n');

  // Configurar API
  const apiDir = join(__dirname, 'src/lambdas/gs2api');
  const apiEnvPath = join(apiDir, '.env');
  
  if (!fs.existsSync(apiEnvPath)) {
    console.log('📝 Criando .env da API...');
    fs.copyFileSync(join(apiDir, '.env.example'), apiEnvPath);
    console.log('✅ Arquivo .env da API criado!\n');
  }

  // Configurar Frontend
  const frontendDir = join(__dirname, 'src/frontend');
  const frontendEnvPath = join(frontendDir, '.env');
  
  if (fs.existsSync(frontendDir) && !fs.existsSync(frontendEnvPath)) {
    console.log('📝 Criando .env do Frontend...');
    fs.copyFileSync(join(frontendDir, '.env.example'), frontendEnvPath);
    console.log('✅ Arquivo .env do Frontend criado!\n');
  }

  // Instalar dependências da API
  const apiNodeModules = join(apiDir, 'node_modules');
  if (!fs.existsSync(apiNodeModules)) {
    console.log('📦 Instalando dependências da API...');
    await runCommand('npm', ['install'], { cwd: apiDir });
    console.log('✅ Dependências da API instaladas!\n');
  }

  // Instalar dependências do Frontend
  const frontendNodeModules = join(frontendDir, 'node_modules');
  if (fs.existsSync(frontendDir) && !fs.existsSync(frontendNodeModules)) {
    console.log('📦 Instalando dependências do Frontend...');
    await runCommand('npm', ['install'], { cwd: frontendDir });
    console.log('✅ Dependências do Frontend instaladas!\n');
  }
}

async function startServices() {
  console.log('🚀 Iniciando serviços...\n');

  const apiPort = 3001;
  const frontendPort = 3000;
  const apiDir = join(__dirname, 'src/lambdas/gs2api');
  const frontendDir = join(__dirname, 'src/frontend');

  // Verificar portas
  if (await isPortInUse(apiPort)) {
    console.log(`⚠️  Porta ${apiPort} já está em uso!`);
  }

  if (await isPortInUse(frontendPort)) {
    console.log(`⚠️  Porta ${frontendPort} já está em uso!`);
  }

  // Iniciar API
  console.log('🔧 Iniciando API moderna na porta 3001...');
  const apiProcess = spawn('npm', ['run', 'dev'], {
    cwd: apiDir,
    stdio: ['inherit', 'pipe', 'pipe'],
    shell: true
  });

  apiProcess.stdout.on('data', (data) => {
    const output = data.toString().trim();
    if (output) {
      console.log(`[API] ${output}`);
    }
  });

  apiProcess.stderr.on('data', (data) => {
    const output = data.toString().trim();
    if (output && !output.includes('ExperimentalWarning')) {
      console.error(`[API ERROR] ${output}`);
    }
  });

  // Aguardar API iniciar
  console.log('⏳ Aguardando API inicializar...');
  await new Promise(resolve => setTimeout(resolve, 5000));

  // Iniciar Frontend se existir
  let frontendProcess = null;
  if (fs.existsSync(frontendDir)) {
    console.log('🎨 Iniciando Frontend na porta 3000...');
    frontendProcess = spawn('npm', ['run', 'dev'], {
      cwd: frontendDir,
      stdio: ['inherit', 'pipe', 'pipe'],
      shell: true
    });

    frontendProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        console.log(`[FRONTEND] ${output}`);
      }
    });

    frontendProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output && !output.includes('ExperimentalWarning')) {
        console.error(`[FRONTEND ERROR] ${output}`);
      }
    });

    // Aguardar Frontend iniciar
    await new Promise(resolve => setTimeout(resolve, 3000));
  }

  console.log(`
✅ Sistema GS2 iniciado com sucesso!

🌐 Serviços disponíveis:
┌─────────────────────────────────────────────┐
│  🔧 API Moderna: http://localhost:3001      │
│     • Health: /health                       │
│     • API Info: /api                        │
│     • Auth: /api/auth/*                     │
│     • Users: /api/users/*                   │
│     • Dashboard: /api/dashboard/*           │
│                                             │
│  🎨 Frontend: http://localhost:3000         │
│     • Interface moderna e responsiva        │
│     • Autenticação completa                 │
│     • Dashboard com estatísticas            │
│                                             │
│  📚 Documentação:                           │
│     • API: src/lambdas/gs2api/README.md     │
│     • Frontend: src/frontend/README.md      │
└─────────────────────────────────────────────┘

🔐 Login de teste:
   CPF: 12345678901
   Senha: 123456

🛑 Para parar os serviços: Ctrl+C
  `);

  // Manter processo vivo e capturar Ctrl+C
  process.on('SIGINT', () => {
    console.log('\n🛑 Parando serviços...');
    
    if (apiProcess) {
      apiProcess.kill('SIGTERM');
    }
    
    if (frontendProcess) {
      frontendProcess.kill('SIGTERM');
    }
    
    setTimeout(() => {
      console.log('✅ Serviços parados com sucesso!');
      process.exit(0);
    }, 1000);
  });

  // Manter processo vivo
  return new Promise(() => {});
}

async function main() {
  try {
    await setupEnvironment();
    await startServices();
  } catch (error) {
    console.error('❌ Erro ao iniciar Sistema GS2:', error.message);
    process.exit(1);
  }
}

// Executar se chamado diretamente
// if (import.meta.url === `file://${process.argv[1]}`) {
//   main();
// }

main();