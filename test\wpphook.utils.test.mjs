import { describe, test, expect, beforeEach } from '@jest/globals';

// Import utilities to test
const Validators = (await import('../src/lambdas/gs2api/wpphook/utils/validators.mjs')).default;
const MessageBuilder = (await import('../src/lambdas/gs2api/wpphook/utils/messageBuilder.mjs')).default;

describe('wpphook Utilities', () => {
    
    describe('Validators', () => {
        let validators;

        beforeEach(() => {
            validators = new Validators();
        });

        describe('Input Sanitization', () => {
            test('should sanitize basic input', () => {
                expect(Validators.sanitizeInput('  Hello World  ')).toBe('Hello World');
                expect(Validators.sanitizeInput('Test\n\rMessage')).toBe('Test Message');
                expect(Validators.sanitizeInput('')).toBe('');
            });

            test('should handle special characters', () => {
                expect(Validators.sanitizeInput('Test<script>alert("xss")</script>')).not.toContain('<script>');
                expect(Validators.sanitizeInput('Normal text')).toBe('Normal text');
            });

            test('should handle null and undefined', () => {
                expect(Validators.sanitizeInput(null)).toBe('');
                expect(Validators.sanitizeInput(undefined)).toBe('');
            });
        });

        describe('Command Validation', () => {
            test('should identify exit commands', () => {
                expect(Validators.isExitCommand('sair')).toBe(true);
                expect(Validators.isExitCommand('SAIR')).toBe(true);
                expect(Validators.isExitCommand('exit')).toBe(true);
                expect(Validators.isExitCommand('parar')).toBe(true);
                expect(Validators.isExitCommand('cancelar')).toBe(true);
                
                expect(Validators.isExitCommand('continuar')).toBe(false);
                expect(Validators.isExitCommand('GS2')).toBe(false);
                expect(Validators.isExitCommand('')).toBe(false);
            });

            test('should identify GS2 trigger', () => {
                expect(Validators.isGS2Trigger('GS2')).toBe(true);
                expect(Validators.isGS2Trigger('gs2')).toBe(true);
                expect(Validators.isGS2Trigger('Gs2')).toBe(true);
                
                expect(Validators.isGS2Trigger('GSS')).toBe(false);
                expect(Validators.isGS2Trigger('GS22')).toBe(false);
                expect(Validators.isGS2Trigger('hello')).toBe(false);
            });

            test('should validate user responses', () => {
                const validOptions = ['sim', 'claro', 'ok'];
                
                expect(Validators.validateUserResponse('sim', validOptions)).toBe(true);
                expect(Validators.validateUserResponse('SIM', validOptions)).toBe(true);
                expect(Validators.validateUserResponse('Claro', validOptions)).toBe(true);
                expect(Validators.validateUserResponse('✅ Claro', validOptions)).toBe(true);
                
                expect(Validators.validateUserResponse('não', validOptions)).toBe(false);
                expect(Validators.validateUserResponse('', validOptions)).toBe(false);
                expect(Validators.validateUserResponse('maybe', validOptions)).toBe(false);
            });
        });

        describe('Personal Data Validation', () => {
            test('should validate names correctly', () => {
                expect(Validators.validateName('João da Silva')).toBe(true);
                expect(Validators.validateName('Maria Santos')).toBe(true);
                expect(Validators.validateName('José')).toBe(true);
                expect(Validators.validateName('Ana Beatriz de Souza')).toBe(true);
                
                expect(Validators.validateName('J')).toBe(false); // Too short
                expect(Validators.validateName('João123')).toBe(false); // Contains numbers
                expect(Validators.validateName('')).toBe(false); // Empty
                expect(Validators.validateName('João@Silva')).toBe(false); // Special characters
            });

            test('should validate CPF correctly', () => {
                expect(Validators.validateCPF('12345678901')).toBe(true); // Valid format
                expect(Validators.validateCPF('98765432100')).toBe(true);
                
                expect(Validators.validateCPF('123456789012')).toBe(false); // Too long
                expect(Validators.validateCPF('1234567890')).toBe(false); // Too short
                expect(Validators.validateCPF('12345678abc')).toBe(false); // Contains letters
                expect(Validators.validateCPF('')).toBe(false); // Empty
                expect(Validators.validateCPF('00000000000')).toBe(false); // All zeros
                expect(Validators.validateCPF('11111111111')).toBe(false); // All same digit
            });

            test('should sanitize CPF correctly', () => {
                expect(Validators.sanitizeCPF('123.456.789-01')).toBe('12345678901');
                expect(Validators.sanitizeCPF('123 456 789 01')).toBe('12345678901');
                expect(Validators.sanitizeCPF('12345678901')).toBe('12345678901');
                expect(Validators.sanitizeCPF('123-456-789.01')).toBe('12345678901');
            });

            test('should format CPF correctly', () => {
                expect(Validators.formatCPF('12345678901')).toBe('123.456.789-01');
                expect(Validators.formatCPF('98765432100')).toBe('987.654.321-00');
            });

            test('should validate email correctly', () => {
                expect(Validators.validateEmail('<EMAIL>')).toBe(true);
                expect(Validators.validateEmail('<EMAIL>')).toBe(true);
                expect(Validators.validateEmail('<EMAIL>')).toBe(true);
                
                expect(Validators.validateEmail('invalid-email')).toBe(false);
                expect(Validators.validateEmail('test@')).toBe(false);
                expect(Validators.validateEmail('@example.com')).toBe(false);
                expect(Validators.validateEmail('<EMAIL>')).toBe(false);
                expect(Validators.validateEmail('')).toBe(false);
            });

            test('should validate clinic name correctly', () => {
                expect(Validators.validateClinicName('Hospital São Paulo')).toBe(true);
                expect(Validators.validateClinicName('Clínica Dr. Santos')).toBe(true);
                expect(Validators.validateClinicName('UBS Central')).toBe(true);
                
                expect(Validators.validateClinicName('H')).toBe(false); // Too short
                expect(Validators.validateClinicName('')).toBe(false); // Empty
                expect(Validators.validateClinicName('   ')).toBe(false); // Only spaces
            });
        });

        describe('Data Formatting', () => {
            test('should format CNPJ correctly', () => {
                expect(Validators.formatCNPJ('12345678000199')).toBe('12.345.678/0001-99');
                expect(Validators.formatCNPJ('98765432000111')).toBe('98.765.432/0001-11');
            });

            test('should handle invalid CNPJ formatting', () => {
                expect(Validators.formatCNPJ('123')).toBe('123'); // Too short
                expect(Validators.formatCNPJ('')).toBe(''); // Empty
            });
        });

        describe('Edge Cases', () => {
            test('should handle null and undefined inputs', () => {
                expect(Validators.validateName(null)).toBe(false);
                expect(Validators.validateName(undefined)).toBe(false);
                expect(Validators.validateCPF(null)).toBe(false);
                expect(Validators.validateCPF(undefined)).toBe(false);
                expect(Validators.validateEmail(null)).toBe(false);
                expect(Validators.validateEmail(undefined)).toBe(false);
            });

            test('should handle non-string inputs', () => {
                expect(Validators.validateName(123)).toBe(false);
                expect(Validators.validateCPF(12345678901)).toBe(false);
                expect(Validators.validateEmail({})).toBe(false);
            });
        });
    });

    describe('MessageBuilder', () => {
        let messageBuilder;

        beforeEach(() => {
            messageBuilder = new MessageBuilder();
        });

        describe('Initial and Greeting Messages', () => {
            test('should build initial help message', () => {
                const message = messageBuilder.buildInitialHelp();
                expect(message).toContain('GS2');
                expect(message).toContain('iniciar');
            });

            test('should build greeting message', () => {
                const message = messageBuilder.buildGreeting();
                expect(message).toContain('Olá');
                expect(message).toContain('Kuará');
                expect(message).toContain('Capitale Holding');
                expect(message).toContain('GS2');
            });

            test('should build greeting repeat message', () => {
                const message = messageBuilder.buildGreetingRepeat();
                expect(message).toContain('✅ Claro');
                expect(message).toContain('⏳ Não');
            });
        });

        describe('Clinic-related Messages', () => {
            test('should build clinic request message', () => {
                const message = messageBuilder.buildClinicRequest();
                expect(message).toContain('clínica');
                expect(message).toContain('hospital');
                expect(message).toContain('sair');
            });

            test('should build clinic confirmation message', () => {
                const mockClinic = {
                    razao_social: 'Hospital Teste Ltda',
                    cnpj: '12345678000199'
                };

                const message = messageBuilder.buildClinicConfirmation(mockClinic);
                expect(message).toContain('Hospital Teste Ltda');
                expect(message).toContain('12.345.678/0001-99');
                expect(message).toContain('Razão Social');
                expect(message).toContain('CNPJ');
            });

            test('should build clinic not found message', () => {
                const message = messageBuilder.buildClinicNotFound();
                expect(message).toContain('não encontramos');
                expect(message).toContain('sair');
            });

            test('should build invalid clinic name message', () => {
                const message = messageBuilder.buildInvalidClinicName();
                expect(message).toContain('nome válido');
                expect(message).toContain('clínica');
            });
        });

        describe('Personal Data Collection Messages', () => {
            test('should build name request message', () => {
                const message = messageBuilder.buildNameRequest();
                expect(message).toContain('nome completo');
                expect(message).toContain('dados pessoais');
            });

            test('should build CPF request message', () => {
                const message = messageBuilder.buildCPFRequest();
                expect(message).toContain('CPF');
                expect(message).toContain('números');
            });

            test('should build email request message', () => {
                const message = messageBuilder.buildEmailRequest();
                expect(message).toContain('e-mail');
                expect(message).toContain('contato');
            });

            test('should build invalid data messages', () => {
                expect(messageBuilder.buildInvalidName()).toContain('nome válido');
                expect(messageBuilder.buildInvalidCPF()).toContain('CPF inválido');
                expect(messageBuilder.buildInvalidEmail()).toContain('Email inválido');
            });
        });

        describe('Data Confirmation Messages', () => {
            test('should build data confirmation message', () => {
                const userData = {
                    fullName: 'João da Silva',
                    cpf: '12345678901',
                    email: '<EMAIL>'
                };

                const message = messageBuilder.buildDataConfirmation(userData);
                expect(message).toContain('João da Silva');
                expect(message).toContain('123.456.789-01');
                expect(message).toContain('<EMAIL>');
                expect(message).toContain('Confirme');
            });

            test('should build data confirmation repeat message', () => {
                const userData = {
                    fullName: 'Maria Santos',
                    cpf: '98765432100',
                    email: '<EMAIL>'
                };

                const message = messageBuilder.buildDataConfirmationRepeat(userData);
                expect(message).toContain('Maria Santos');
                expect(message).toContain('👍 Está certo');
                expect(message).toContain('✏️ Preciso corrigir');
            });

            test('should build data correction menu', () => {
                const message = messageBuilder.buildDataCorrectionMenu();
                expect(message).toContain('👤 Nome');
                expect(message).toContain('📄 CPF');
                expect(message).toContain('📧 Email');
            });
        });

        describe('Terms and FAQ Messages', () => {
            test('should build terms presentation message', () => {
                const message = messageBuilder.buildTermsPresentation();
                expect(message).toContain('Termos e Condições');
                expect(message).toContain('opcional');
                expect(message).toContain('✅ Sim, de acordo');
                expect(message).toContain('ℹ️ Quero saber mais');
            });

            test('should build FAQ menu', () => {
                const message = messageBuilder.buildFAQMenu();
                expect(message).toContain('❓ Como funciona?');
                expect(message).toContain('💸 Existem custos?');
                expect(message).toContain('🔒 E segurança de dados?');
                expect(message).toContain('💰 Como contrato?');
                expect(message).toContain('📆 Quando recebo?');
                expect(message).toContain('↩️ Voltar para assinar');
            });

            test('should build FAQ responses', () => {
                expect(messageBuilder.buildFAQResponse('como_funciona')).toContain('parceria');
                expect(messageBuilder.buildFAQResponse('custos')).toContain('encargos');
                expect(messageBuilder.buildFAQResponse('seguranca')).toContain('LGPD');
                expect(messageBuilder.buildFAQResponse('contrato')).toContain('WhatsApp');
                expect(messageBuilder.buildFAQResponse('prazo')).toContain('48h');
                expect(messageBuilder.buildFAQResponse('invalid')).toBe('Informação não encontrada.');
            });

            test('should build FAQ follow-up message', () => {
                const message = messageBuilder.buildFAQFollowUp();
                expect(message).toContain('ℹ️ Saber mais');
                expect(message).toContain('✅ Aceitar termos');
            });
        });

        describe('Completion and Error Messages', () => {
            test('should build completion message', () => {
                const message = messageBuilder.buildCompletion();
                expect(message).toContain('Obrigado');
                expect(message).toContain('entraremos em contato');
                expect(message).toContain('próximos passos');
            });

            test('should build exit message', () => {
                const message = messageBuilder.buildExitMessage();
                expect(message).toContain('GS2');
                expect(message).toContain('reiniciar');
                expect(message).toContain('Obrigado');
            });

            test('should build error messages', () => {
                expect(messageBuilder.buildErrorMessage()).toContain('erro interno');
                expect(messageBuilder.buildInvalidResponse()).toContain('Não entendi');
            });
        });

        describe('Message Personalization', () => {
            test('should personalize messages with user name', () => {
                const message = 'Por favor, confirme os dados.';
                const personalizedMessage = messageBuilder.personalizeMessage(message, 'João');
                
                expect(personalizedMessage).toContain('João, por favor');
            });

            test('should handle message without name', () => {
                const message = 'Por favor, confirme os dados.';
                const personalizedMessage = messageBuilder.personalizeMessage(message, null);
                
                expect(personalizedMessage).toBe(message);
            });

            test('should handle empty message', () => {
                const personalizedMessage = messageBuilder.personalizeMessage('', 'João');
                expect(personalizedMessage).toBe('');
            });
        });

        describe('Edge Cases and Error Handling', () => {
            test('should handle undefined clinic data', () => {
                const message = messageBuilder.buildClinicConfirmation(undefined);
                expect(typeof message).toBe('string');
            });

            test('should handle undefined user data', () => {
                const message = messageBuilder.buildDataConfirmation(undefined);
                expect(typeof message).toBe('string');
            });

            test('should handle empty user data', () => {
                const userData = {};
                const message = messageBuilder.buildDataConfirmation(userData);
                expect(typeof message).toBe('string');
            });
        });
    });
});