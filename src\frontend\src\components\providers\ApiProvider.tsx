// =====================================================
// API PROVIDER - PROVEDOR DE API
// Configuração global de interceptors e tratamento de erros
// =====================================================

import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuthStore } from '@/stores/authStore'
import { modernApi } from '@/services/modernApi'
import toast from 'react-hot-toast'

interface ApiProviderProps {
  children: React.ReactNode
}

export function ApiProvider({ children }: ApiProviderProps) {
  const navigate = useNavigate()
  const { logout, token } = useAuthStore()

  useEffect(() => {
    // Interceptor para adicionar token automaticamente
    const requestInterceptor = modernApi.client.interceptors.request.use(
      (config) => {
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Interceptor para tratar respostas e erros
    const responseInterceptor = modernApi.client.interceptors.response.use(
      (response) => {
        return response
      },
      async (error) => {
        const originalRequest = error.config

        // Se erro 401 (não autorizado)
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true

          // Fazer logout e redirecionar
          logout()
          navigate('/login')
          toast.error('Sessão expirada. Faça login novamente.')
          
          return Promise.reject(error)
        }

        // Se erro 403 (sem permissão)
        if (error.response?.status === 403) {
          toast.error('Você não tem permissão para realizar esta ação.')
          return Promise.reject(error)
        }

        // Se erro 404 (não encontrado)
        if (error.response?.status === 404) {
          toast.error('Recurso não encontrado.')
          return Promise.reject(error)
        }

        // Se erro 500 (erro interno)
        if (error.response?.status >= 500) {
          toast.error('Erro interno do servidor. Tente novamente.')
          return Promise.reject(error)
        }

        // Outros erros
        const message = error.response?.data?.message || error.message || 'Erro na requisição'
        toast.error(message)
        
        return Promise.reject(error)
      }
    )

    // Cleanup dos interceptors
    return () => {
      modernApi.client.interceptors.request.eject(requestInterceptor)
      modernApi.client.interceptors.response.eject(responseInterceptor)
    }
  }, [token, logout, navigate])

  return <>{children}</>
}

// Hook para usar o contexto da API
export function useApiContext() {
  // Aqui podemos adicionar funcionalidades específicas do contexto da API
  // Por exemplo, estado de loading global, cache, etc.
  
  return {
    // Funcionalidades futuras
  }
}
