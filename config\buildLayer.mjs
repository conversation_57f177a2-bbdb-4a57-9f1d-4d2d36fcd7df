import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs/promises';
import AdmZip from 'adm-zip';
import { getParams } from './functions.mjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const buildLayer = async () => {
    try {
        const { layerName } = await getParams(['profile', 'layerName'], true);

        const layerDIR = path.resolve(__dirname, '..', 'src', 'layers', layerName);
        const nodeModulesDir = path.join(layerDIR, 'node_modules');
        const nodejsDir = path.join(layerDIR, 'nodejs');
        const zipFile = path.join(layerDIR, `${layerName}.zip`);

        await fs.mkdir(nodeModulesDir, { recursive: true });

        const files = await fs.readdir(layerDIR);
        for (const file of files) {
            if (file !== 'node_modules' && file !== 'nodejs') {
                await fs.copyFile(path.join(layerDIR, file), path.join(nodeModulesDir, file));
            }
        }

        await fs.mkdir(nodejsDir, { recursive: true });
        await fs.rename(nodeModulesDir, path.join(nodejsDir, 'node_modules'));

        const nodejsFiles = await fs.readdir(nodejsDir);
        if (nodejsFiles.length > 0) {
            const zip = new AdmZip();
            zip.addLocalFolder(nodejsDir, 'nodejs');
            zip.writeZip(zipFile);
        } else {
            throw new Error('O diretório nodejs está vazio. Nada para zipar.');
        }

        await fs.rm(nodejsDir, { recursive: true, force: true });

        console.log('\nFinalizado!\n');
    } catch (error) {
        console.log('\n');
        console.log('\x1b[31m%s\x1b[0m', 'Catch no buildLayer():');
        console.log(error);
        console.log(error.message);
        console.log('\n');
    } finally {
        process.exit();
    }
};

buildLayer();
