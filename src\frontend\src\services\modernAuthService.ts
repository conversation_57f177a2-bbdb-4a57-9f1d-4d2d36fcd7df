// =====================================================
// MODERN AUTH SERVICE - SERVIÇO DE AUTENTICAÇÃO MODERNO
// Gerenciamento de autenticação com API moderna
// =====================================================

import { modernApi } from './modernApi'
import type { LoginCredentials, User } from '@/types'

/**
 * Interface para resposta de autenticação
 */
interface AuthResponse {
  user: User
  token: string
  expires_at: string
}

/**
 * Interface para verificação de token
 */
interface TokenVerification {
  valid: boolean
  user?: User
}

/**
 * Classe de serviço de autenticação moderna
 */
class ModernAuthService {
  /**
   * Fazer login
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      console.log('ModernAuthService: fazendo login', credentials)
      const response = await modernApi.login({
        cpf: credentials.cpf,
        password: credentials.password
      })

      console.log('ModernAuthService: resposta da API', response)

      // Processar resposta do lambda
      if (!response.success) {
        console.error('ModernAuthService: login falhou', response.message)
        throw new Error(response.message || 'Erro no login')
      }

      const authResponse = {
        user: response.data.user,
        token: response.data.token,
        expires_at: response.data.expires_at
      }

      console.log('ModernAuthService: login bem-sucedido', authResponse)
      return authResponse
    } catch (error: any) {
      console.error('ModernAuthService: erro no login', error)
      throw new Error(error.message || 'Erro no login')
    }
  }

  /**
   * Fazer logout
   */
  async logout(): Promise<boolean> {
    try {
      await modernApi.logout()
      return true
    } catch (error) {
      // Mesmo com erro, considerar logout bem-sucedido
      return true
    }
  }

  /**
   * Renovar token
   */
  async refreshToken(): Promise<{ token: string; expires_at: string }> {
    try {
      const response = await modernApi.refreshToken()
      return response
    } catch (error: any) {
      throw new Error(error.message || 'Erro ao renovar token')
    }
  }

  /**
   * Verificar token
   */
  async verifyToken(): Promise<TokenVerification> {
    try {
      const response = await modernApi.verifyToken()
      return response as TokenVerification
    } catch (error) {
      return { valid: false }
    }
  }

  /**
   * Obter perfil do usuário
   */
  async getProfile(): Promise<User> {
    try {
      const response = await modernApi.getProfile()
      return response as User
    } catch (error: any) {
      throw new Error(error.message || 'Erro ao obter perfil')
    }
  }

  /**
   * Atualizar perfil
   */
  async updateProfile(data: Partial<User>): Promise<boolean> {
    try {
      await modernApi.updateProfile(data)
      return true
    } catch (error: any) {
      throw new Error(error.message || 'Erro ao atualizar perfil')
    }
  }

  /**
   * Alterar senha
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<boolean> {
    try {
      await modernApi.changePassword({
        currentPassword,
        newPassword
      })
      return true
    } catch (error: any) {
      throw new Error(error.message || 'Erro ao alterar senha')
    }
  }

  /**
   * Solicitar recuperação de senha
   */
  async requestPasswordReset(email: string): Promise<boolean> {
    try {
      await modernApi.forgotPassword(email)
      return true
    } catch (error: any) {
      throw new Error(error.message || 'Erro ao solicitar recuperação')
    }
  }

  /**
   * Resetar senha
   */
  async resetPassword(token: string, newPassword: string): Promise<boolean> {
    try {
      await modernApi.resetPassword({
        token,
        newPassword
      })
      return true
    } catch (error: any) {
      throw new Error(error.message || 'Erro ao resetar senha')
    }
  }

  /**
   * Verificar se usuário tem permissão
   */
  hasPermission(user: User | null, permission: string): boolean {
    if (!user || !user.permissions) return false
    return user.permissions[permission] === true
  }

  /**
   * Verificar se usuário pertence a grupo
   */
  hasGroup(user: User | null, groupName: string): boolean {
    if (!user || !user.groups) return false
    return user.groups.some(group => group.name === groupName)
  }

  /**
   * Verificar se usuário pertence a pelo menos um dos grupos
   */
  hasAnyGroup(user: User | null, groupNames: string[]): boolean {
    if (!user || !user.groups) return false
    return user.groups.some(group => groupNames.includes(group.name))
  }

  /**
   * Verificar se é master
   */
  isMaster(user: User | null): boolean {
    return this.hasGroup(user, 'Master')
  }

  /**
   * Verificar se é admin
   */
  isAdmin(user: User | null): boolean {
    return this.hasAnyGroup(user, ['Master', 'Admin'])
  }

  /**
   * Verificar se é profissional
   */
  isProfessional(user: User | null): boolean {
    return this.hasGroup(user, 'Profissional')
  }

  /**
   * Verificar se tem acesso financeiro
   */
  hasFinancialAccess(user: User | null): boolean {
    return this.hasAnyGroup(user, ['Master', 'Admin', 'Financeiro'])
  }

  /**
   * Verificar se é auditor
   */
  isAuditor(user: User | null): boolean {
    return this.hasAnyGroup(user, ['Master', 'Admin', 'Auditor'])
  }
}

// Instância singleton
export const modernAuthService = new ModernAuthService()

// Export da classe para testes
export { ModernAuthService }
export type { AuthResponse, TokenVerification }
