import dotenv from 'dotenv';
import authService from './auth.mjs';
import auditLogger from './audit.mjs';

// Carregar variáveis de ambiente
dotenv.config();

// Roteador universal que funciona com Lambda e Express
class UniversalRouter {
  constructor() {
    this.routes = new Map();
    this.middlewares = [];
  }

  // Adicionar middleware
  use(middleware) {
    this.middlewares.push(middleware);
  }

  // Registrar rota
  route(method, path, handler) {
    const key = `${method.toUpperCase()}:${path}`;
    this.routes.set(key, handler);
    console.log(`Route registered: ${key}`);
  }

  // Métodos HTTP convenientes
  get(path, handler) { this.route('GET', path, handler); }
  post(path, handler) { this.route('POST', path, handler); }
  put(path, handler) { this.route('PUT', path, handler); }
  delete(path, handler) { this.route('DELETE', path, handler); }

  // Normalizar request para formato padrão
  normalizeRequest(event) {
    // Se for Lambda Function URL
    if (event.requestContext && event.requestContext.http) {
      return {
        method: event.requestContext.http.method,
        path: event.requestContext.http.path,
        headers: event.headers || {},
        body: event.body ? (typeof event.body === 'string' ? JSON.parse(event.body) : event.body) : {},
        query: event.queryStringParameters || {},
        params: {}
      };
    }

    // Se for Express (req, res)
    if (event.method && event.url) {
      return {
        method: event.method,
        path: event.url.split('?')[0],
        headers: event.headers || {},
        body: event.body || {},
        query: event.query || {},
        params: event.params || {}
      };
    }

    // Formato customizado
    return {
      method: event.method || 'GET',
      path: event.path || '/',
      headers: event.headers || {},
      body: event.body || {},
      query: event.query || {},
      params: event.params || {}
    };
  }

  // Normalizar response para formato padrão
  normalizeResponse(result, isLambda = true) {
    // Se já é uma resposta formatada
    if (result && typeof result === 'object' && 'statusCode' in result) {
      if (isLambda) {
        return {
          statusCode: result.statusCode,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          },
          body: JSON.stringify(result)
        };
      }
      return result;
    }

    // Resposta padrão
    const response = {
      statusCode: 200,
      success: true,
      data: result
    };

    if (isLambda) {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        },
        body: JSON.stringify(response)
      };
    }

    return response;
  }

  // Executar middlewares
  async executeMiddlewares(request) {
    for (const middleware of this.middlewares) {
      await middleware(request);
    }
  }

  // Processar requisição
  async handleRequest(event, isLambda = true) {
    try {
      const request = this.normalizeRequest(event);
      console.log(`Processing request: ${request.method} ${request.path}`);

      // Executar middlewares
      await this.executeMiddlewares(request);

      // Buscar rota
      const key = `${request.method}:${request.path}`;
      const handler = this.routes.get(key);

      if (!handler) {
        console.log(`Route not found: ${key}`);
        console.log('Available routes:', Array.from(this.routes.keys()));

        const errorResponse = {
          statusCode: 404,
          success: false,
          message: 'Route not found',
          data: { requestedRoute: key, availableRoutes: Array.from(this.routes.keys()) }
        };

        return this.normalizeResponse(errorResponse, isLambda);
      }

      // Executar handler
      console.log(`Executing handler for: ${key}`);
      const result = await handler(request);

      return this.normalizeResponse(result, isLambda);

    } catch (error) {
      console.error('Router error:', error);

      const errorResponse = {
        statusCode: 500,
        success: false,
        message: 'Internal server error',
        data: { error: error.message }
      };

      return this.normalizeResponse(errorResponse, isLambda);
    }
  }

  // Método para Express
  expressHandler() {
    return async (req, res) => {
      try {
        const result = await this.handleRequest(req, false);
        res.status(result.statusCode || 200).json(result);
      } catch (error) {
        res.status(500).json({
          statusCode: 500,
          success: false,
          message: 'Internal server error',
          data: { error: error.message }
        });
      }
    };
  }
}

// Criar instância do roteador
const router = new UniversalRouter();

// Registrar rotas
router.post('/login', authService.login.bind(authService));
router.post('/brwlogin', authService.login.bind(authService)); // Compatibilidade
router.get('/verify', authService.verifyAuth.bind(authService));
router.get('/health', async () => ({ statusCode: 200, success: true, message: 'API is healthy' }));

// Handler principal do Lambda
export const handler = async (event) => {
  console.log('Lambda handler started');
  console.log('Event received:', JSON.stringify(event, null, 2));

  try {
    const result = await router.handleRequest(event, true);
    console.log('Handler result:', result);
    return result;
  } catch (error) {
    console.error('Main handler error:', error);
    console.error('Error stack:', error.stack);

    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      },
      body: JSON.stringify({
        statusCode: 500,
        success: false,
        message: 'Internal server error',
        data: { error: error.message }
      })
    };
  }
};

// Export do roteador para uso com Express
export { router };
