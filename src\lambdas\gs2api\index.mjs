// Lazy loading handlers - carrega apenas quando necessário
const handlerLoaders = {
  login: {
    route: 'login',
    handler: () => import('./login/index.mjs'),
  },
};

class Controller {
  constructor() {}

  buildResponse(statusCode, data) {
    return {
      statusCode,
      body: JSON.stringify(data),
    };
  }

  buildErrorResponse(error, message = 'Erro interno do servidor') {
    console.log('Erro:', error);
    return this.buildResponse(500, { message });
  }
}

class Router {
  constructor() {
    this.routes = new Map();
  }

  addRoute(method, path, routeName) {
    const key = `${method}:${path}`;
    this.routes.set(key, routeName);
  }

  async loadHandler(routeName) {
    console.log(`Loading handler for route: ${routeName}`);

    // Verifica se existe um loader para esta rota
    if (!handlerLoaders[routeName]) {
      throw new Error(`No handler loader found for route: ${routeName}`);
    }

    try {
      console.log(`Importing module for route: ${routeName}`);

      const module = await handlerLoaders[routeName]();
      console.log(`Module loaded successfully for ${routeName}`, Object.keys(module));

      const handler = module.handler;
      if (!handler) {
        throw new Error(
          `Handler function not found in module for route: ${routeName}. Available exports: ${Object.keys(
            module
          )}`
        );
      }

      if (typeof handler !== 'function') {
        throw new Error(
          `Handler is not a function for route: ${routeName}. Type: ${typeof handler}`
        );
      }

      console.log(`Handler cached successfully: ${routeName}`);

      return handler;
    } catch (error) {
      console.error(`Failed to load handler for route ${routeName}:`, error);
      throw error;
    }
  }

  async handleRequest(event) {
    try {
      const method = event.requestContext.http.method;
      const path = event.requestContext.http.path;
      const key = `${method}:${path}`;

      console.log(`Looking for route: ${key}`);

      if (this.routes.has(key)) {
        const routeName = this.routes.get(key);

        console.log(`Executing handler for route: ${routeName}`);

        // Carrega o handler apenas quando necessário
        const handlerFunction = await this.loadHandler(routeName);

        const result = await handlerFunction(event);
        console.log(`Handler result for ${routeName}:`, result);

        if (key === 'GET:/wpphook') {
          return result;
        }

        const ctrl = new Controller();
        return ctrl.buildResponse(200, result);
      }

      console.log(`Route not found: ${key}. Available routes:`, Array.from(this.routes.keys()));
      return {
        statusCode: 404,
        body: JSON.stringify({
          message: 'Route not found',
          requestedRoute: key,
        }),
      };
    } catch (error) {
      console.error('Router error:', error);
      const ctrl = new Controller();
      return ctrl.buildErrorResponse(error);
    }
  }
}

const router = new Router();

export const handler = async (event) => {
  console.log('Lambda handler started');
  console.log('Event received:', JSON.stringify(event, null, 2));

  try {
    event.headers.Authorization = event?.headers?.authorization ?? '';

    // Register all routes
    for (const routeName of Object.keys(handlerLoaders)) {
      router.addRoute('POST', `/${routeName}`, routeName);

      // Special case: wpphook also supports GET for health check
      if (routeName === 'wpphook') {
        router.addRoute('GET', `/${routeName}`, routeName);
        router.addRoute('GET', `/${routeName}/health`, routeName);
      }
    }

    // Parse body if it's a string
    if (typeof event.body === 'string') {
      try {
        event.body = JSON.parse(event.body);
      } catch (parseError) {
        console.error('Failed to parse event.body:', parseError);
        const ctrl = new Controller();
        return ctrl.buildErrorResponse(parseError, 'Invalid JSON in request body');
      }
    }

    console.log('Parsed event body:', event.body);

    const result = await router.handleRequest({ ...event.body, ...event });
    console.log('Handler result:', result);

    return result;
  } catch (error) {
    console.error('Main handler error:', error);
    console.error('Error stack:', error.stack);

    const ctrl = new Controller();
    return ctrl.buildErrorResponse(error, `Main handler error: ${error.message}`);
  }
};

// yarn updateFunction region=us-east-1 profile=capitale lambdaName=gs2api
