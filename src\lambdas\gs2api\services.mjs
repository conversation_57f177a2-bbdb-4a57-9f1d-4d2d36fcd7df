import db from './database.mjs';
import auditLogger from './audit.mjs';

/**
 * Serviço de Usuários
 */
class UserService {
  async getUsers(filters = {}) {
    try {
      let query = db('usuarios as u')
        .leftJoin('usuario_grupos as ug', 'u.id', 'ug.usuario_id')
        .leftJoin('grupos as g', 'ug.grupo_id', 'g.id')
        .select(
          'u.id',
          'u.cpf',
          'u.nome',
          'u.email',
          'u.ativo',
          'u.aceite_lgpd',
          'u.onboarding_pendente',
          'u.ultimo_login',
          'u.created_at',
          db.raw('GROUP_CONCAT(g.nome) as grupos')
        )
        .groupBy('u.id');

      if (filters.search) {
        query = query.where(function() {
          this.where('u.nome', 'like', `%${filters.search}%`)
              .orWhere('u.email', 'like', `%${filters.search}%`)
              .orWhere('u.cpf', 'like', `%${filters.search}%`);
        });
      }

      if (filters.ativo !== undefined) {
        query = query.where('u.ativo', filters.ativo);
      }

      const users = await query
        .limit(filters.limit || 50)
        .offset(filters.offset || 0)
        .orderBy('u.nome');

      return {
        statusCode: 200,
        success: true,
        data: users
      };
    } catch (error) {
      console.error('Erro ao buscar usuários:', error);
      return {
        statusCode: 500,
        success: false,
        message: 'Erro interno do servidor'
      };
    }
  }

  async createUser(userData, requestUser) {
    try {
      const [userId] = await db('usuarios').insert({
        cpf: userData.cpf,
        nome: userData.nome,
        email: userData.email,
        senha: userData.senha, // Já deve vir hasheada
        ativo: userData.ativo !== undefined ? userData.ativo : true,
        aceite_lgpd: userData.aceite_lgpd || false,
        onboarding_pendente: userData.onboarding_pendente !== undefined ? userData.onboarding_pendente : true
      });

      // Log de auditoria
      await auditLogger.logCreate(
        requestUser.cpf,
        requestUser.nome,
        'usuario',
        userId,
        userData,
        null,
        null
      );

      return {
        statusCode: 201,
        success: true,
        message: 'Usuário criado com sucesso',
        data: { id: userId }
      };
    } catch (error) {
      console.error('Erro ao criar usuário:', error);
      return {
        statusCode: 500,
        success: false,
        message: 'Erro interno do servidor'
      };
    }
  }
}

/**
 * Serviço de Dashboard
 */
class DashboardService {
  async getStats() {
    try {
      const [
        totalUsuarios,
        usuariosAtivos,
        totalClientes,
        clientesAtivos,
        totalProfissionais,
        profissionaisAtivos,
        totalPlantoes,
        plantoesAbertos,
        totalAntecipacoes,
        antecipacoesAprovadas
      ] = await Promise.all([
        db('usuarios').count('* as count').first(),
        db('usuarios').where('ativo', true).count('* as count').first(),
        db('clientes').count('* as count').first(),
        db('clientes').where('ativo', true).count('* as count').first(),
        db('profissionais').count('* as count').first(),
        db('profissionais').where('ativo', true).count('* as count').first(),
        db('plantoes').count('* as count').first(),
        db('plantoes').where('status', 'aberto').count('* as count').first(),
        db('antecipacoes').count('* as count').first(),
        db('antecipacoes').where('status', 'aprovado').count('* as count').first()
      ]);

      const stats = {
        usuarios: {
          total: parseInt(totalUsuarios.count),
          ativos: parseInt(usuariosAtivos.count)
        },
        clientes: {
          total: parseInt(totalClientes.count),
          ativos: parseInt(clientesAtivos.count)
        },
        profissionais: {
          total: parseInt(totalProfissionais.count),
          ativos: parseInt(profissionaisAtivos.count)
        },
        plantoes: {
          total: parseInt(totalPlantoes.count),
          abertos: parseInt(plantoesAbertos.count)
        },
        antecipacoes: {
          total: parseInt(totalAntecipacoes.count),
          aprovadas: parseInt(antecipacoesAprovadas.count)
        }
      };

      return {
        statusCode: 200,
        success: true,
        data: stats
      };
    } catch (error) {
      console.error('Erro ao buscar estatísticas:', error);
      return {
        statusCode: 500,
        success: false,
        message: 'Erro interno do servidor'
      };
    }
  }

  async getRecentActivity(limit = 10) {
    try {
      const activities = await db('audit_logs')
        .select('operation_type', 'resource_type', 'user_name', 'action_description', 'created_at')
        .where('success', true)
        .orderBy('created_at', 'desc')
        .limit(limit);

      return {
        statusCode: 200,
        success: true,
        data: activities
      };
    } catch (error) {
      console.error('Erro ao buscar atividades recentes:', error);
      return {
        statusCode: 500,
        success: false,
        message: 'Erro interno do servidor'
      };
    }
  }
}

/**
 * Serviço de Profissionais
 */
class ProfessionalService {
  async getProfessionals(filters = {}) {
    try {
      let query = db('profissionais as p')
        .join('usuarios as u', 'p.usuario_id', 'u.id')
        .select(
          'p.id',
          'p.usuario_id',
          'u.nome',
          'u.email',
          'u.cpf',
          'p.crm',
          'p.especialidade',
          'p.ativo',
          'p.created_at'
        );

      if (filters.search) {
        query = query.where(function() {
          this.where('u.nome', 'like', `%${filters.search}%`)
              .orWhere('p.crm', 'like', `%${filters.search}%`)
              .orWhere('p.especialidade', 'like', `%${filters.search}%`);
        });
      }

      if (filters.ativo !== undefined) {
        query = query.where('p.ativo', filters.ativo);
      }

      const professionals = await query
        .limit(filters.limit || 50)
        .offset(filters.offset || 0)
        .orderBy('u.nome');

      return {
        statusCode: 200,
        success: true,
        data: professionals
      };
    } catch (error) {
      console.error('Erro ao buscar profissionais:', error);
      return {
        statusCode: 500,
        success: false,
        message: 'Erro interno do servidor'
      };
    }
  }
}

/**
 * Serviço de Clientes
 */
class ClientService {
  async getClients(filters = {}) {
    try {
      let query = db('clientes')
        .select('id', 'cnpj', 'razao_social', 'nome_fantasia', 'email', 'telefone', 'ativo', 'created_at');

      if (filters.search) {
        query = query.where(function() {
          this.where('razao_social', 'like', `%${filters.search}%`)
              .orWhere('nome_fantasia', 'like', `%${filters.search}%`)
              .orWhere('cnpj', 'like', `%${filters.search}%`);
        });
      }

      if (filters.ativo !== undefined) {
        query = query.where('ativo', filters.ativo);
      }

      const clients = await query
        .limit(filters.limit || 50)
        .offset(filters.offset || 0)
        .orderBy('razao_social');

      return {
        statusCode: 200,
        success: true,
        data: clients
      };
    } catch (error) {
      console.error('Erro ao buscar clientes:', error);
      return {
        statusCode: 500,
        success: false,
        message: 'Erro interno do servidor'
      };
    }
  }
}

// Exportar instâncias dos serviços
export const userService = new UserService();
export const dashboardService = new DashboardService();
export const professionalService = new ProfessionalService();
export const clientService = new ClientService();
