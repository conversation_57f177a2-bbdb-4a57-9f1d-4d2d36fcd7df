// =====================================================
// CLIENTS PAGE - PÁGINA DE CLIENTES
// Gerenciamento de clientes e instituições
// =====================================================

import React from 'react'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'
import { Building2, Plus, Search, Filter } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

export function Clients() {
  const { t } = useTranslation()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-heading flex items-center space-x-3">
            <Building2 className="w-8 h-8 text-primary-600" />
            <span>{t('clients.title')}</span>
          </h1>
          <p className="text-muted mt-1">
            {t('clients.subtitle')}
          </p>
        </div>
        
        <Button
          variant="primary"
          icon={<Plus className="w-5 h-5" />}
        >
          {t('clients.createClient')}
        </Button>
      </div>

      {/* Coming Soon */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-12"
      >
        <Card className="max-w-md mx-auto">
          <CardContent className="py-12">
            <Building2 className="w-16 h-16 text-primary-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-heading mb-2">
              Em Desenvolvimento
            </h3>
            <p className="text-muted">
              A página de gerenciamento de clientes está sendo desenvolvida e estará disponível em breve.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
