# 🏥 Sistema GS2 - Frontend

Sistema de Gestão de Saúde e Plantões - Interface Web Moderna

## 🚀 Tecnologias

- **React 18** - Biblioteca para interfaces de usuário
- **TypeScript** - Tipagem estática para JavaScript
- **Vite** - Build tool moderna e rápida
- **Tailwind CSS** - Framework CSS utilitário
- **React Query** - Gerenciamento de estado servidor
- **Zustand** - Gerenciamento de estado cliente
- **React Router** - Roteamento SPA
- **React Hook Form** - Gerenciamento de formulários
- **Framer Motion** - Animações fluidas
- **React i18next** - Internacionalização
- **Lucide React** - Ícones modernos

## 📦 Instalação

```bash
# Instalar dependências
npm install

# Copiar arquivo de ambiente
cp .env.example .env

# Iniciar servidor de desenvolvimento
npm run dev
```

## 🛠️ Scripts Disponíveis

```bash
# Desenvolvimento
npm run dev          # Iniciar servidor de desenvolvimento
npm run build        # Build de produção
npm run preview      # Preview do build de produção

# Qualidade de Código
npm run lint         # Executar ESLint
npm run lint:fix     # Corrigir problemas do ESLint
npm run type-check   # Verificar tipos TypeScript

# Testes
npm run test         # Executar testes
npm run test:ui      # Interface dos testes
npm run test:coverage # Cobertura de testes
```

## 🏗️ Estrutura do Projeto

```
src/
├── components/          # Componentes reutilizáveis
│   ├── ui/             # Componentes base (Button, Input, etc.)
│   ├── auth/           # Componentes de autenticação
│   └── layout/         # Componentes de layout
├── pages/              # Páginas da aplicação
├── hooks/              # Hooks customizados
├── services/           # Serviços de API
├── stores/             # Stores Zustand
├── utils/              # Funções utilitárias
├── types/              # Definições de tipos
├── locales/            # Arquivos de tradução
└── styles/             # Estilos globais
```

## 🎨 Sistema de Design

### Cores
- **Primary**: Azul (#3b82f6)
- **Secondary**: Cinza neutro
- **Success**: Verde (#22c55e)
- **Warning**: Amarelo (#f59e0b)
- **Error**: Vermelho (#ef4444)

### Tipografia
- **Font Family**: Inter (sans-serif)
- **Font Mono**: JetBrains Mono

### Componentes
Todos os componentes seguem o padrão de design system com:
- Variantes consistentes
- Tamanhos padronizados
- Estados visuais claros
- Acessibilidade integrada

## 🌐 Internacionalização

O sistema suporta múltiplos idiomas:
- **Português (Brasil)** - Padrão
- **Inglês (EUA)**
- **Espanhol (Espanha)**

### Adicionar Nova Tradução

1. Criar arquivo em `src/locales/{locale}.json`
2. Adicionar configuração em `src/locales/i18n.ts`
3. Atualizar `getAvailableLanguages()`

## 🎭 Temas

Sistema de temas com suporte a:
- **Light** - Tema claro
- **Dark** - Tema escuro
- **System** - Segue preferência do sistema

## 🔐 Autenticação

Sistema completo de autenticação com:
- Login com CPF e senha
- Recuperação de senha
- Gerenciamento de sessão
- Controle de permissões
- Rotas protegidas

## 📊 Gerenciamento de Estado

### Cliente (Zustand)
- `authStore` - Autenticação e usuário
- `themeStore` - Tema e preferências

### Servidor (React Query)
- Cache inteligente
- Sincronização automática
- Otimistic updates
- Background refetch

## 🛡️ Segurança

- Validação de formulários com Zod
- Sanitização de dados
- Controle de acesso baseado em roles
- Headers de segurança
- CSP (Content Security Policy)

## 📱 Responsividade

Design mobile-first com breakpoints:
- **sm**: 640px
- **md**: 768px
- **lg**: 1024px
- **xl**: 1280px
- **2xl**: 1536px

## ⚡ Performance

- Code splitting automático
- Lazy loading de rotas
- Otimização de imagens
- Bundle analysis
- Tree shaking

## 🧪 Testes

Framework de testes com:
- **Vitest** - Test runner
- **Testing Library** - Testes de componentes
- **MSW** - Mock de APIs
- **Coverage** - Cobertura de código

## 🚀 Deploy

### Build de Produção
```bash
npm run build
```

### Variáveis de Ambiente
Configurar as seguintes variáveis:
- `VITE_API_BASE_URL` - URL da API
- `VITE_APP_NAME` - Nome da aplicação
- `VITE_NODE_ENV` - Ambiente (production)

## 📈 Monitoramento

- **Sentry** - Tracking de erros
- **Google Analytics** - Analytics
- **Hotjar** - Heatmaps e sessões

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch (`git checkout -b feature/nova-funcionalidade`)
3. Commit suas mudanças (`git commit -m 'Adiciona nova funcionalidade'`)
4. Push para a branch (`git push origin feature/nova-funcionalidade`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para detalhes.

## 👥 Equipe

- **Frontend**: React + TypeScript
- **Backend**: Node.js + Lambda
- **Database**: MySQL
- **Infrastructure**: AWS

---

**Sistema GS2** - Desenvolvido com ❤️ para profissionais de saúde
