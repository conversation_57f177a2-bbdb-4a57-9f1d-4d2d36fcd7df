# GS2 API - Sistema de Autenticação e Auditoria

Sistema de API unificado usando Knex.js com auditoria completa e roteador universal compatível com Lambda e Express.

## Características

- ✅ Roteador universal (Lambda Function URL + Express)
- ✅ Autenticação JWT
- ✅ Sistema de auditoria completo
- ✅ Knex.js para queries
- ✅ Logs estruturados
- ✅ CORS configurado
- ✅ Middleware de autenticação

## Configuração

### 1. Variáveis de Ambiente

Copie o arquivo `.env` e configure:

```bash
# Configurações do Banco de Dados
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=sua_senha
DB_NAME=gs2
DB_PORT=3306

# JWT Secret
JWT_SECRET=sua-chave-secreta-super-forte

# Ambiente
NODE_ENV=development
```

### 2. Banco de Dados

Execute o script SQL para criar as tabelas:

```bash
mysql -u root -p gs2 < database-schema.sql
```

### 3. Instalação

```bash
npm install
```

## Uso

### Como Lambda Function

O sistema está pronto para ser usado como Lambda Function URL. O handler principal está em `index.mjs`.

### Como Express

```javascript
import express from 'express';
import { router } from './index.mjs';

const app = express();
app.use(express.json());
app.use('/', router.expressHandler());

app.listen(3000, () => {
  console.log('Server running on port 3000');
});
```

### Teste Local

```bash
node test-local.mjs
```

## Endpoints

### POST /login
Autenticação de usuário

**Request:**
```json
{
  "username": "12345678901",
  "password": "senha123"
}
```

**Response:**
```json
{
  "statusCode": 200,
  "success": true,
  "message": "Login realizado com sucesso",
  "data": {
    "user": {
      "id": 1,
      "cpf": "12345678901",
      "nome": "João Silva",
      "email": "<EMAIL>",
      "grupos": [...],
      "rotas": [...]
    },
    "token": "jwt_token_here",
    "expires_at": "2024-01-01T00:00:00.000Z"
  }
}
```

### GET /verify
Verificar token JWT

**Headers:**
```
Authorization: Bearer jwt_token_here
```

### GET /health
Health check da API

## Sistema de Auditoria

Todas as ações são automaticamente logadas na tabela `audit_logs`:

- Login/Logout
- Criação/Atualização/Exclusão de recursos
- Acesso a dados sensíveis
- Falhas de autenticação

### Níveis de Compliance

- **STANDARD**: Operações normais
- **HIGH**: Login/Logout, alterações importantes
- **CRITICAL**: Exclusões, acesso a dados sensíveis

## Estrutura do Projeto

```
src/lambdas/gs2api/
├── index.mjs           # Handler principal e roteador
├── auth.mjs            # Serviço de autenticação
├── audit.mjs           # Sistema de auditoria
├── database.mjs        # Configuração do Knex
├── database-schema.sql # Schema do banco
├── test-local.mjs      # Teste local
├── package.json        # Dependências
├── .env               # Configurações
└── README.md          # Este arquivo
```

## Compatibilidade

O sistema foi projetado para ser compatível com:

- AWS Lambda Function URL
- Express.js
- Qualquer framework Node.js

O roteador universal normaliza requests e responses automaticamente.

## Endpoints Disponíveis

### Públicos
- `POST /login` - Login de usuário
- `POST /brwlogin` - Login (compatibilidade)
- `GET /health` - Health check

### Protegidos (requer token)
- `GET /verify` - Verificar token
- `GET /dashboard/stats` - Estatísticas do dashboard
- `GET /dashboard/activity` - Atividade recente
- `GET /users` - Listar usuários
- `POST /users` - Criar usuário
- `GET /professionals` - Listar profissionais
- `GET /clients` - Listar clientes
- `GET /audit` - Logs de auditoria

## Frontend Integrado

O frontend React está completamente integrado com:

### Hooks Personalizados
- `useDashboard()` - Dashboard stats e atividade
- `useUsers()` - Gerenciamento de usuários
- `useProfessionals()` - Gerenciamento de profissionais
- `useClients()` - Gerenciamento de clientes
- `useAudit()` - Logs de auditoria

### Providers
- `ApiProvider` - Interceptors globais e tratamento de erros
- Autenticação automática com JWT
- Logout automático em caso de token expirado

### Funcionalidades
- ✅ Login/Logout automático
- ✅ Interceptors de request/response
- ✅ Tratamento de erros global
- ✅ Toast notifications
- ✅ Loading states
- ✅ Cache inteligente com React Query

## Teste da Integração

Execute o teste completo:

```bash
node test-integration.mjs
```

Este teste verifica:
- Health check
- Login com usuário admin
- Verificação de token
- Dashboard stats
- Listagem de usuários
- Logs de auditoria

## Usuário Padrão

- **CPF**: `00000000000`
- **Senha**: `admin123`
- **Grupo**: Super Admin (acesso total)

## Próximos Passos

1. **Configure o banco** executando o SQL
2. **Teste a API** com `node test-integration.mjs`
3. **Inicie o frontend** com `npm run dev`
4. **Faça login** com as credenciais padrão
5. **Explore** todas as funcionalidades integradas

A integração está completa e pronta para uso! 🚀
