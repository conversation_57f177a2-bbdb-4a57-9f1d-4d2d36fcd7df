// =====================================================
// LOADING SPINNER - COMPONENTE DE CARREGAMENTO
// Componente de spinner de carregamento customizável
// =====================================================

import React from 'react'
import { motion } from 'framer-motion'
import { Loader2 } from 'lucide-react'
import { cn } from '@/utils'

interface LoadingSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'primary' | 'secondary' | 'white'
  className?: string
  text?: string
}

export function LoadingSpinner({ 
  size = 'md', 
  variant = 'primary', 
  className,
  text 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }

  const variantClasses = {
    primary: 'text-primary-600',
    secondary: 'text-secondary-500',
    white: 'text-white'
  }

  const textSizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  }

  return (
    <div className={cn('flex flex-col items-center justify-center', className)}>
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
      >
        <Loader2 className={cn(
          sizeClasses[size],
          variantClasses[variant]
        )} />
      </motion.div>
      
      {text && (
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className={cn(
            'mt-2 font-medium',
            textSizeClasses[size],
            variant === 'white' ? 'text-white' : 'text-secondary-600 dark:text-secondary-400'
          )}
        >
          {text}
        </motion.p>
      )}
    </div>
  )
}

// Componente de loading para páginas inteiras
interface PageLoadingProps {
  text?: string
}

export function PageLoading({ text = 'Carregando...' }: PageLoadingProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-secondary-50 dark:bg-secondary-900">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className="text-center"
      >
        <LoadingSpinner size="lg" text={text} />
      </motion.div>
    </div>
  )
}

// Componente de loading para cards/seções
interface SectionLoadingProps {
  className?: string
  text?: string
  size?: 'sm' | 'md' | 'lg'
}

export function SectionLoading({ 
  className, 
  text = 'Carregando...', 
  size = 'md' 
}: SectionLoadingProps) {
  return (
    <div className={cn(
      'flex items-center justify-center py-8',
      className
    )}>
      <LoadingSpinner size={size} text={text} />
    </div>
  )
}

// Componente de loading inline
interface InlineLoadingProps {
  className?: string
  size?: 'xs' | 'sm' | 'md'
}

export function InlineLoading({ className, size = 'sm' }: InlineLoadingProps) {
  return (
    <LoadingSpinner 
      size={size} 
      className={cn('inline-flex', className)} 
    />
  )
}

// Componente de skeleton loading
interface SkeletonProps {
  className?: string
  variant?: 'text' | 'rectangular' | 'circular'
  width?: string | number
  height?: string | number
  lines?: number
}

export function Skeleton({ 
  className, 
  variant = 'text', 
  width, 
  height, 
  lines = 1 
}: SkeletonProps) {
  const baseClasses = [
    'animate-pulse bg-secondary-200 dark:bg-secondary-700'
  ]

  const variantClasses = {
    text: 'rounded',
    rectangular: 'rounded-md',
    circular: 'rounded-full'
  }

  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height
  }

  if (variant === 'text' && lines > 1) {
    return (
      <div className={cn('space-y-2', className)}>
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={cn(
              baseClasses,
              variantClasses[variant],
              index === lines - 1 ? 'w-3/4' : 'w-full',
              !height && 'h-4'
            )}
            style={style}
          />
        ))}
      </div>
    )
  }

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        !width && variant === 'text' && 'w-full',
        !height && variant === 'text' && 'h-4',
        !width && variant === 'rectangular' && 'w-full',
        !height && variant === 'rectangular' && 'h-32',
        !width && variant === 'circular' && 'w-10',
        !height && variant === 'circular' && 'h-10',
        className
      )}
      style={style}
    />
  )
}

// Componente de loading para tabelas
export function TableLoading({ rows = 5, columns = 4 }: { rows?: number; columns?: number }) {
  return (
    <div className="space-y-3">
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton
              key={colIndex}
              variant="rectangular"
              height={40}
              className="flex-1"
            />
          ))}
        </div>
      ))}
    </div>
  )
}

// Componente de loading para cards
export function CardLoading() {
  return (
    <div className="p-6 space-y-4">
      <div className="flex items-center space-x-4">
        <Skeleton variant="circular" width={40} height={40} />
        <div className="flex-1 space-y-2">
          <Skeleton variant="text" width="60%" />
          <Skeleton variant="text" width="40%" />
        </div>
      </div>
      <Skeleton variant="rectangular" height={100} />
      <div className="flex space-x-2">
        <Skeleton variant="rectangular" width={80} height={32} />
        <Skeleton variant="rectangular" width={80} height={32} />
      </div>
    </div>
  )
}
