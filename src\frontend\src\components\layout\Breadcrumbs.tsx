// =====================================================
// BREADCRUMBS COMPONENT - COMPONENTE DE BREADCRUMBS
// Navegação hierárquica de páginas
// =====================================================

import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { ChevronRight, Home } from 'lucide-react'
import { motion } from 'framer-motion'
import { cn } from '@/utils'
import type { BreadcrumbItem } from '@/types'

export function Breadcrumbs() {
  const { t } = useTranslation()
  const location = useLocation()

  // Mapear rotas para breadcrumbs
  const routeMap: Record<string, string> = {
    '/dashboard': t('navigation.dashboard'),
    '/users': t('navigation.users'),
    '/professionals': t('navigation.professionals'),
    '/clients': t('navigation.clients'),
    '/shifts': t('navigation.shifts'),
    '/advances': t('navigation.advances'),
    '/whatsapp': t('navigation.whatsapp'),
    '/audit': t('navigation.audit'),
    '/reports': t('navigation.reports'),
    '/settings': t('navigation.settings')
  }

  // Gerar breadcrumbs baseado na rota atual
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(Boolean)
    const breadcrumbs: BreadcrumbItem[] = []

    // Sempre incluir Home
    breadcrumbs.push({
      label: t('navigation.dashboard'),
      path: '/dashboard'
    })

    // Construir breadcrumbs baseado nos segmentos
    let currentPath = ''
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`
      
      // Pular se for dashboard (já incluído como Home)
      if (segment === 'dashboard') return

      const label = routeMap[currentPath] || segment.charAt(0).toUpperCase() + segment.slice(1)
      
      breadcrumbs.push({
        label,
        path: index === pathSegments.length - 1 ? undefined : currentPath
      })
    })

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  // Não mostrar breadcrumbs se estiver na página inicial
  if (location.pathname === '/dashboard') {
    return null
  }

  return (
    <nav aria-label="Breadcrumb" className="flex items-center space-x-2 text-sm">
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        className="flex items-center space-x-2"
      >
        {breadcrumbs.map((item, index) => (
          <React.Fragment key={index}>
            {index > 0 && (
              <ChevronRight className="w-4 h-4 text-secondary-400 dark:text-secondary-500" />
            )}
            
            <motion.div
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center"
            >
              {index === 0 && (
                <Home className="w-4 h-4 mr-1 text-secondary-500 dark:text-secondary-400" />
              )}
              
              {item.path ? (
                <Link
                  to={item.path}
                  className={cn(
                    'text-secondary-600 dark:text-secondary-400 hover:text-primary-600 dark:hover:text-primary-400',
                    'transition-colors duration-200 font-medium'
                  )}
                >
                  {item.label}
                </Link>
              ) : (
                <span className="text-secondary-900 dark:text-secondary-100 font-semibold">
                  {item.label}
                </span>
              )}
            </motion.div>
          </React.Fragment>
        ))}
      </motion.div>
    </nav>
  )
}

// Hook para definir breadcrumbs customizados
export function useBreadcrumbs(items: BreadcrumbItem[]) {
  // Este hook pode ser usado para definir breadcrumbs customizados
  // em páginas específicas que precisam de navegação mais complexa
  
  React.useEffect(() => {
    // Implementar lógica para breadcrumbs customizados se necessário
    // Por exemplo, armazenar em contexto ou estado global
  }, [items])

  return {
    setBreadcrumbs: (newItems: BreadcrumbItem[]) => {
      // Implementar função para definir breadcrumbs customizados
    }
  }
}
