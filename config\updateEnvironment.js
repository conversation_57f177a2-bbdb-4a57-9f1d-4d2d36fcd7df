const { exec } = require('child_process');
const { getArg } = require('./functions');

const profile = getArg('profile');
// const region = getArg('region');
const region = 'us-east-2';
const timeout = parseInt(getArg('timeout')) || 30;

if (!profile || !region) {
  console.log(
    '\x1b[31m%s\x1b[0m',
    '\nVocê precisa passar "profile" e "region"\n'
  );
  process.exit();
}

const lambdas = require('./lambdas')[region];

const updateFunctionConfig = async function (FunctionName, profile, region) {
  const env = require('../ENV.local.json');

  const envs = Object.entries(env)
    .filter((i) => i[1])
    .map((i) => `${i[0]}=${i[1]}`)
    .join(',');

  let environments = '--environment "Variables={' + envs + '}"';
  return new Promise((resolve) => {
    let command = `aws lambda update-function-configuration --function-name ${FunctionName} ${environments} --profile ${profile} --region ${region} --timeout ${timeout}`;
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.log(`error: ${error.message}`);
        resolve([]);
      }
      if (stderr) {
        console.log(`stderr: ${stderr}`);
        resolve([]);
      }
      resolve(stdout);
    });
  });
};

const init = async function () {
  try {
    await Promise.all(
      lambdas.map((f) => updateFunctionConfig(f, profile, region))
    );
  } catch (e) {
    console.log('error', e);
  }
};

init();
