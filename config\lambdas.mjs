export default {
    'us-east-2': [
        'brocreateaditivo20',
        'brogeracsv',
        'broemailraw',
        'brwProfissionalSaude',
        'teste',
        'cabConcluiDocAssinado',
        'capGraficosPlataforma',
        'brwAtendimento',
        'brwPerfilMenuRotas',
        'EnviaDocClickSign',
        'brwProfSolPlantao',
        'broimprimecontrato',
        'brwUsuario',
        'capCreateCCB',
        'brwinstfinanceira',
        'brogerapdf',
        'brwGrupoMenu',
        'brwAtendPlantao',
        'capProcessaAntecipacao',
        'brwMenuRotas',
        'capAtendimentoProfissional',
        'brosalvapdf20',
        'brwContrato',
        'brwAuthToken',
        'brosalvapdf',
        'capFechamento',
        'brwLogin',
        'capKuaraIntegracao',
        'brwPerfilMenu',
        'capCep',
        'capSolicitacaoDPO',
        'brwCliente',
        'gs2Hospitalar',
        'capRotinaFechamento',
        'capBaseHistorica',
        'gs2DadosHospitalar',
        'broemailcsv',
        'broenviaemail',
        'cabSolicitaAntecipacao',
        'brwInstSaude',
        'broenviacontrato',
        'cabAditivoAprovado',
        'cabSolicitaDocAssinado',
        'brwLists',
        'brwAntecipacao',
        'AuraGeraOcorrencia',
    ],
};
