// =====================================================
// I18N CONFIGURATION - CONFIGURAÇÃO DE INTERNACIONALIZAÇÃO
// Configuração do react-i18next para múltiplos idiomas
// =====================================================

import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

// Importar traduções
import ptBR from './pt-BR.json'
import enUS from './en-US.json'
import esES from './es-ES.json'

// Recursos de tradução
const resources = {
  pt: {
    translation: ptBR
  },
  en: {
    translation: enUS
  },
  es: {
    translation: esES
  }
}

// Configuração do i18next
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    
    // Idioma padrão
    fallbackLng: 'pt',
    lng: 'pt', // Definir idioma inicial explicitamente
    
    // Idiomas suportados
    supportedLngs: ['pt', 'en', 'es'],
    
    // Configurações de detecção
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      lookupLocalStorage: 'gs2-language',
      caches: ['localStorage'],
    },
    
    // Configurações de interpolação
    interpolation: {
      escapeValue: false, // React já escapa por padrão
      format: (value, format, lng) => {
        // Formatação customizada
        if (format === 'currency') {
          return new Intl.NumberFormat(lng === 'pt' ? 'pt-BR' : lng === 'en' ? 'en-US' : 'es-ES', {
            style: 'currency',
            currency: lng === 'pt' ? 'BRL' : lng === 'en' ? 'USD' : 'EUR'
          }).format(value)
        }
        
        if (format === 'date') {
          return new Intl.DateTimeFormat(lng === 'pt' ? 'pt-BR' : lng === 'en' ? 'en-US' : 'es-ES').format(new Date(value))
        }
        
        if (format === 'datetime') {
          return new Intl.DateTimeFormat(lng === 'pt' ? 'pt-BR' : lng === 'en' ? 'en-US' : 'es-ES', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          }).format(new Date(value))
        }
        
        if (format === 'number') {
          return new Intl.NumberFormat(lng === 'pt' ? 'pt-BR' : lng === 'en' ? 'en-US' : 'es-ES').format(value)
        }
        
        return value
      }
    },
    
    // Configurações de desenvolvimento
    debug: false, // Desabilitar debug para evitar conflitos
    
    // Configurações de namespace
    defaultNS: 'translation',
    
    // Configurações de carregamento
    load: 'languageOnly',
    
    // Configurações de pluralização
    pluralSeparator: '_',
    contextSeparator: '_',
    
    // Configurações de retorno
    returnNull: false,
    returnEmptyString: false,
    returnObjects: false,
    
    // Configurações de parsing
    parseMissingKeyHandler: (key) => {
      if (import.meta.env.DEV) {
        console.warn(`Missing translation key: ${key}`)
      }
      return key
    },
    
    // Aguardar inicialização
    initImmediate: false,
  })

// Função para alterar idioma
export const changeLanguage = (lng: string) => {
  return i18n.changeLanguage(lng)
}

// Função para obter idioma atual
export const getCurrentLanguage = () => {
  return i18n.language
}

// Função para obter idiomas disponíveis
export const getAvailableLanguages = () => {
  return [
    { code: 'pt', name: 'Português (Brasil)', flag: '🇧🇷' },
    { code: 'en', name: 'English (US)', flag: '🇺🇸' },
    { code: 'es', name: 'Español (España)', flag: '🇪🇸' }
  ]
}

// Função para formatar moeda
export const formatCurrency = (value: number, currency?: string) => {
  const lng = getCurrentLanguage()
  const currencyCode = currency || (lng === 'pt' ? 'BRL' : lng === 'en' ? 'USD' : 'EUR')
  
  return new Intl.NumberFormat(lng === 'pt' ? 'pt-BR' : lng === 'en' ? 'en-US' : 'es-ES', {
    style: 'currency',
    currency: currencyCode
  }).format(value)
}

// Função para formatar data
export const formatDate = (date: Date | string, options?: Intl.DateTimeFormatOptions) => {
  const lng = getCurrentLanguage()
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }
  
  return new Intl.DateTimeFormat(lng === 'pt' ? 'pt-BR' : lng === 'en' ? 'en-US' : 'es-ES', { ...defaultOptions, ...options }).format(dateObj)
}

// Função para formatar data e hora
export const formatDateTime = (date: Date | string, options?: Intl.DateTimeFormatOptions) => {
  const lng = getCurrentLanguage()
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }
  
  return new Intl.DateTimeFormat(lng === 'pt' ? 'pt-BR' : lng === 'en' ? 'en-US' : 'es-ES', { ...defaultOptions, ...options }).format(dateObj)
}

// Função para formatar número
export const formatNumber = (value: number, options?: Intl.NumberFormatOptions) => {
  const lng = getCurrentLanguage()
  return new Intl.NumberFormat(lng === 'pt' ? 'pt-BR' : lng === 'en' ? 'en-US' : 'es-ES', options).format(value)
}

// Função para formatar porcentagem
export const formatPercent = (value: number, options?: Intl.NumberFormatOptions) => {
  const lng = getCurrentLanguage()
  return new Intl.NumberFormat(lng === 'pt' ? 'pt-BR' : lng === 'en' ? 'en-US' : 'es-ES', {
    style: 'percent',
    minimumFractionDigits: 2,
    ...options
  }).format(value / 100)
}

// Função para obter texto de direção (RTL/LTR)
export const getTextDirection = () => {
  const lng = getCurrentLanguage()
  // Adicionar idiomas RTL se necessário
  const rtlLanguages = ['ar', 'he', 'fa']
  return rtlLanguages.includes(lng) ? 'rtl' : 'ltr'
}

export { i18n }
