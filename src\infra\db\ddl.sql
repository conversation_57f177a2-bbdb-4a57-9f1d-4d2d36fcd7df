CREATE TABLE `brcGrupoMenu` (
  `gmNome` varchar(100) NOT NULL,
  `gmDescricao` varchar(600) NOT NULL,
  `gmTipo` varchar(1) DEFAULT NULL COMMENT 'M=Mobile - O=Operacional - G=Gestao - A=Administrativo - T=Tecnologia - C=CS - F=Financeiro',
  PRIMARY KEY (`gmNome`)
)

CREATE TABLE `brcMenuAcesso` (
  `maMenu` varchar(20) NOT NULL,
  `maDescricao` varchar(100) NOT NULL,
  `rtNome` varchar(20) NOT NULL,
  `maAtivo` int DEFAULT NULL,
  `maMenuPai` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`maMenu`),
  KEY `fk_brMenuAcesso_brRotas1_idx` (`rtNome`)
)

CREATE TABLE `brcMenuRotas` (
  `dtInclusao` timestamp NULL DEFAULT NULL,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `mrNome` varchar(100) NOT NULL,
  `mrNomeTela` varchar(100) DEFAULT NULL,
  `mrRota` varchar(100) DEFAULT NULL,
  `mrOrdemMenu` int DEFAULT NULL,
  PRIMARY KEY (`mrNome`)
)

CREATE TABLE `brcPerfilAcesso` (
  `paPerfilAcesso` varchar(20) NOT NULL,
  `paDescrição` varchar(100) DEFAULT NULL,
  `paAtivo` int DEFAULT NULL,
  PRIMARY KEY (`paPerfilAcesso`)
)

CREATE TABLE `brcPerfilMenu` (
  `pmNome` varchar(100) NOT NULL,
  `pmNomeTela` varchar(100) DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT NULL,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`pmNome`)
)

CREATE TABLE `brcRotas` (
  `rtNome` varchar(20) NOT NULL,
  `rtDescricao` varchar(100) NOT NULL,
  `rtExecutavel` int NOT NULL,
  `rtAtivo` int DEFAULT NULL,
  PRIMARY KEY (`rtNome`)
)

CREATE TABLE `brcTipoUsuario` (
  `tpTipoUsuario` varchar(5) NOT NULL COMMENT 'gcon = gestor de contrato - profs = profissional de saude - admin = administrador - opadm = administrador operador - opope = operacional do operador - opesc = escalista do operador - fiadm = administrador financeira - finope = operacional da financeira',
  `tpDescricao` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`tpTipoUsuario`)
)

CREATE TABLE `capAditivoProfSaude` (
  `isInstSaude` varchar(100) NOT NULL,
  `laNome` varchar(100) NOT NULL,
  `esEspecialidade` varchar(100) NOT NULL,
  `ocNrContrato` varchar(50) NOT NULL,
  `clCliente` varchar(100) NOT NULL,
  `ceTipoPagamento` varchar(100) NOT NULL,
  `opNrPlantao` int NOT NULL,
  `psCPF` varchar(11) NOT NULL,
  `adDataSolicitacao` timestamp NOT NULL,
  `adDataAprovacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `usCPFUsuarioAprovador` varchar(11) DEFAULT NULL,
  `adValorFixo` float DEFAULT NULL,
  `adValor` float DEFAULT NULL,
  `adValorLiquido` float DEFAULT NULL,
  `adValorBruto` float DEFAULT NULL,
  `adDataVencimento` date DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT NULL,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`adDataSolicitacao`),
  KEY `fk_brAditivoOperadorProfSaude-_broProfissionalSaude1_idx` (`psCPF`),
  KEY `fk_capAditivoProfSaude_capInstSaudePlantao1_idx` (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`)
)

CREATE TABLE `capAntecipacao` (
  `anNrAntecipacao` int NOT NULL AUTO_INCREMENT,
  `anDataSolicitacao` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `psCPF` varchar(11) NOT NULL,
  `ifInstFinanceira` varchar(100) NOT NULL,
  `icNomeAprovador` varchar(100) DEFAULT NULL,
  `anDataAprovacao` timestamp NULL DEFAULT NULL,
  `anValorSolicitado` float NOT NULL,
  `anKuaraId` varchar(100) DEFAULT NULL,
  `anKuaraStatus` varchar(100) DEFAULT NULL,
  `anKuaraDataPagamento` date DEFAULT NULL,
  `anValorAprovado` float DEFAULT NULL,
  `anValorLiquido` float DEFAULT NULL,
  `anTokenAprovacao` varchar(200) DEFAULT NULL,
  `usCPFUsuarioAprovador` varchar(11) DEFAULT NULL,
  `anTaxaAntecipacao` float DEFAULT NULL,
  `opDataRecebimento` date DEFAULT NULL,
  `idEnvelope` varchar(200) DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT NULL,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `anValorBruto` float DEFAULT NULL,
  PRIMARY KEY (`anNrAntecipacao`),
  KEY `fk_brAntecipacao_brInstFinanceira1_idx` (`ifInstFinanceira`),
  KEY `fk_brAntecipacao_ifInstFinancContato1_idx` (`ifInstFinanceira`,`usCPFUsuarioAprovador`),
  KEY `ix1_psCPF` (`psCPF`)
)

CREATE TABLE `capAtendimentoFechaAditivo` (
  `isInstSaude` varchar(100) NOT NULL,
  `laNome` varchar(100) NOT NULL,
  `esEspecialidade` varchar(100) NOT NULL,
  `ocNrContrato` varchar(50) NOT NULL,
  `clCliente` varchar(100) NOT NULL,
  `ceTipoPagamento` varchar(100) NOT NULL,
  `opNrPlantao` int NOT NULL,
  `psCPF` varchar(11) NOT NULL,
  `adDataSolicitacao` timestamp NOT NULL,
  `afNome` varchar(100) NOT NULL,
  `afIni` timestamp NOT NULL,
  `adValorFixo` float DEFAULT NULL,
  `adValor` float DEFAULT NULL,
  `adValorLiquido` float DEFAULT NULL,
  `adValorBruto` float DEFAULT NULL
)

CREATE TABLE `capBanco` (
  `bcBancoNR` varchar(4) NOT NULL,
  `bcBancoNome` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`bcBancoNR`)
)

CREATE TABLE `capCliente` (
  `clCliente` varchar(100) NOT NULL,
  `clNomeCliente` varchar(100) NOT NULL,
  `clTelefone` varchar(100) NOT NULL,
  `clEmail` varchar(100) NOT NULL,
  `clCEP` varchar(100) DEFAULT NULL,
  `clEndereco` varchar(200) DEFAULT NULL,
  `clNrEnd` varchar(20) DEFAULT NULL,
  `clComplEnd` varchar(100) DEFAULT NULL,
  `clBairro` varchar(100) DEFAULT NULL,
  `clCidade` varchar(100) DEFAULT NULL,
  `clUF` varchar(2) DEFAULT NULL,
  `clAtivo` int DEFAULT '1',
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT NULL,
  `clNomeContato` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`clCliente`)
)

CREATE TABLE `capClienteUsuario` (
  `clCliente` varchar(100) NOT NULL,
  `usCPFUsuario` varchar(11) NOT NULL,
  `ucGestor` int DEFAULT NULL,
  `dtinclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`clCliente`,`usCPFUsuario`),
  KEY `fk_brGestorContratoUsuario_brUsuario1_idx` (`usCPFUsuario`),
  KEY `fk_broClienteUsuario_broCliente1_idx` (`clCliente`)
)

CREATE TABLE `capCodFechamento` (
  `codFechamento` int NOT NULL AUTO_INCREMENT COMMENT 'codigo sequencial para os fechamentos',
  PRIMARY KEY (`codFechamento`)
)

CREATE TABLE `capConfiguracao` (
  `chave` varchar(255) NOT NULL,
  `valor` text NOT NULL,
  `descricao` text COLLATE utf8mb3_unicode_ci,
  `tipo` enum('string','number','boolean','json') DEFAULT 'string',
  `dataAtualizacao` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`chave`),
  UNIQUE KEY `chave` (`chave`)
)

CREATE TABLE `capConselhoClasse` (
  `ccConselhoClasse` varchar(10) NOT NULL,
  `ccDenominacao` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`ccConselhoClasse`)
)

CREATE TABLE `capEspecialidade` (
  `esEspecialidade` varchar(100) NOT NULL,
  PRIMARY KEY (`esEspecialidade`)
)

CREATE TABLE `capFaleConosco` (
  `dsNome` varchar(200) NOT NULL,
  `dsEmail` varchar(200) DEFAULT NULL,
  `dsTelefone` varchar(50) DEFAULT NULL,
  `dsMensagem` longtext NOT NULL,
  `dtCriacao` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `dsProtocolo` varchar(100) DEFAULT NULL,
  `dsAssunto` longtext,
  PRIMARY KEY (`dtCriacao`,`dsNome`)
)

CREATE TABLE `capInstFinancContato` (
  `ifInstFinanceira` varchar(100) NOT NULL,
  `usCPFUsuario` varchar(11) NOT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ifInstFinanceira`,`usCPFUsuario`),
  KEY `fk_ifInstFinancContato_brInstFinanceira1_idx` (`ifInstFinanceira`),
  KEY `fk_ifInstFinancContato_brUsuario1_idx` (`usCPFUsuario`)
)

CREATE TABLE `capInstFinanceira` (
  `ifInstFinanceira` varchar(100) NOT NULL,
  `ifNome` varchar(100) NOT NULL,
  `ifTelefone` varchar(20) NOT NULL,
  `ifCEP` varchar(100) DEFAULT NULL,
  `ifEndereco` varchar(200) NOT NULL,
  `ifNrEnd` varchar(20) NOT NULL,
  `ifComplEnd` varchar(100) DEFAULT NULL,
  `ifBairro` varchar(100) NOT NULL,
  `ifCidade` varchar(100) NOT NULL,
  `ifUF` varchar(2) NOT NULL,
  `dtInclusao` timestamp NULL DEFAULT NULL,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `isAtivo` int DEFAULT '1',
  PRIMARY KEY (`ifInstFinanceira`)
)

CREATE TABLE `capInstSaude` (
  `isInstSaude` varchar(100) NOT NULL COMMENT 'CNPJ',
  `isNome` varchar(200) NOT NULL,
  `isEmail` varchar(100) DEFAULT NULL,
  `isCEP` varchar(100) DEFAULT NULL,
  `isEndereco` varchar(200) NOT NULL,
  `isNrEnd` varchar(20) NOT NULL,
  `isComplEnd` varchar(100) DEFAULT NULL,
  `isBairro` varchar(100) NOT NULL,
  `isCidade` varchar(100) NOT NULL,
  `isUF` varchar(2) NOT NULL,
  `isCodANS` varchar(100) DEFAULT NULL,
  `isAtivo` int DEFAULT '1',
  `dtInclusao` timestamp NULL DEFAULT NULL,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`)
)

CREATE TABLE `capInstSaudeContratoEspec` (
  `isInstSaude` varchar(100) NOT NULL,
  `laNome` varchar(100) NOT NULL,
  `esEspecialidade` varchar(100) NOT NULL,
  `ocNrContrato` varchar(50) NOT NULL,
  `clCliente` varchar(100) NOT NULL,
  `ceTipoPagamento` varchar(100) NOT NULL COMMENT 'Unitario - Hora - Fixo',
  `ceValorHora` float DEFAULT NULL,
  `ceQtdHoraMes` float DEFAULT NULL,
  `ceValorFixoProf` float DEFAULT NULL,
  `ceValorHoraProf` float DEFAULT NULL,
  `ceValorUnitProf` float DEFAULT NULL,
  `ceTaxaDesagio` float DEFAULT NULL,
  `cdDiaRecebimento` int DEFAULT NULL,
  `ceQtdDiasMinRecebim` int DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT NULL,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`),
  KEY `fk_broInstSaudeContratoEspec_broInstSaudeContrato1_idx` (`isInstSaude`,`ocNrContrato`,`clCliente`) /*!80000 INVISIBLE */
)

CREATE TABLE `capInstSaudeLocalAtendimento` (
  `isInstSaude` varchar(100) NOT NULL,
  `laNome` varchar(100) NOT NULL,
  `laCEP` varchar(100) DEFAULT NULL,
  `laEndereco` varchar(200) DEFAULT NULL,
  `laNrEnd` varchar(20) DEFAULT NULL,
  `laComplEnd` varchar(100) DEFAULT NULL,
  `laBairro` varchar(100) DEFAULT NULL,
  `laCidade` varchar(100) DEFAULT NULL,
  `laUF` varchar(2) DEFAULT NULL,
  `laDescricao` varchar(100) DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT NULL,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`,`laNome`),
  KEY `fk_brOperadorLocalAtendimento-_broInstSaude1_idx` (`isInstSaude`)
)

CREATE TABLE `capInstSaudePlSolicitAprovado` (
  `isInstSaude` varchar(100) NOT NULL,
  `laNome` varchar(100) NOT NULL,
  `esEspecialidade` varchar(100) NOT NULL,
  `ocNrContrato` varchar(50) NOT NULL,
  `clCliente` varchar(100) NOT NULL,
  `ceTipoPagamento` varchar(100) NOT NULL,
  `opNrPlantao` int NOT NULL,
  `psCPF` varchar(11) NOT NULL,
  `psSequenciaSolicitacao` int NOT NULL,
  `opSituacao` varchar(100) NOT NULL,
  `saDataEvento` timestamp NOT NULL,
  `saUsuarioEvento` varchar(100) DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`psSequenciaSolicitacao`,`opSituacao`,`saDataEvento`),
  KEY `fk_capInstSaudePlSolicitAprovado_capInstSaudePlantaoSolicit_idx` (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`psSequenciaSolicitacao`)
)

CREATE TABLE `capInstSaudePlantao` (
  `isInstSaude` varchar(100) NOT NULL,
  `laNome` varchar(100) NOT NULL,
  `esEspecialidade` varchar(100) NOT NULL,
  `ocNrContrato` varchar(50) NOT NULL,
  `clCliente` varchar(100) NOT NULL,
  `ceTipoPagamento` varchar(100) NOT NULL,
  `opNrPlantao` int NOT NULL,
  `usCPFUsuarioAprovador` varchar(11) DEFAULT NULL,
  `opDataDivulgacao` timestamp NULL DEFAULT NULL,
  `opAtivo` int DEFAULT NULL,
  `opTipoFechamento` varchar(100) DEFAULT NULL COMMENT 'manual, diario, semanal, mensal',
  `opDataFechamento` timestamp NULL DEFAULT NULL,
  `opPeriodoIni` timestamp NOT NULL,
  `opPeriodoFim` timestamp NOT NULL,
  `psCPF` varchar(11) DEFAULT NULL,
  `opDatarecebimento` date DEFAULT NULL,
  `opDataAntecipacao` date DEFAULT NULL,
  `opQtdHorasRequisitada` int NOT NULL,
  `opQtdHorasRealizadas` time DEFAULT NULL,
  `opValorFixo` float DEFAULT NULL,
  `opValorHora` float DEFAULT NULL,
  `opValorUnit` float DEFAULT NULL,
  `opChaveAcesso` varchar(100) DEFAULT NULL,
  `opSituacao` varchar(100) DEFAULT NULL COMMENT 'Aberto, Solicitado, Aprovado, Executado, AprovExecucao, Antecipado',
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `opDiaFechamento` int DEFAULT NULL,
  `opValorExtra1` float DEFAULT NULL,
  `opValorExtra2` float DEFAULT NULL,
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`),
  KEY `fk_capInstSaudePlantao_capInstSaudeContratoEspec1_idx` (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`)
)

CREATE TABLE `capInstSaudePlantaoSolicitacao` (
  `isInstSaude` varchar(100) NOT NULL,
  `laNome` varchar(100) NOT NULL,
  `esEspecialidade` varchar(100) NOT NULL,
  `ocNrContrato` varchar(50) NOT NULL,
  `clCliente` varchar(100) NOT NULL,
  `ceTipoPagamento` varchar(100) NOT NULL,
  `opNrPlantao` int NOT NULL,
  `psCPF` varchar(11) NOT NULL,
  `psSequenciaSolicitacao` int NOT NULL,
  `psDataSolicitacao` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `psSituacao` varchar(100) NOT NULL COMMENT 'Solicitado Rejeitado Aprovado Desistente',
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`psSequenciaSolicitacao`),
  KEY `fk_broInstSaudePlantaoSolicitacao_broProfissionalSaude1_idx` (`psCPF`),
  KEY `fk_capInstSaudePlantaoSolicitacao_capInstSaudePlantao1_idx` (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`)
)

CREATE TABLE `capInstSaudeUsuario` (
  `isInstSaude` varchar(100) NOT NULL,
  `usCPFUsuario` varchar(11) NOT NULL,
  `dtInclusão` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`,`usCPFUsuario`),
  KEY `fk_broInstSaudeUsuario_broInstSaude1_idx` (`isInstSaude`),
  KEY `fk_broInstSaudeUsuario_broUsuario1_idx` (`usCPFUsuario`)
)

CREATE TABLE `capMensagemWhatsapp` (
  `mwTelefone` varchar(27) NOT NULL,
  `mwDataMens` timestamp NOT NULL,
  `mwEnvRec` varchar(1) NOT NULL COMMENT 'E = Enviada   R = Recebida',
  `mwTelefoneFrom` varchar(27) DEFAULT NULL,
  `mwType` varchar(50) DEFAULT NULL,
  `mwpayload` varchar(100) DEFAULT NULL,
  `mwText` longtext COLLATE utf8mb3_unicode_ci,
  `mwPayloadEnviado` json DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`mwTelefone`,`mwDataMens`,`mwEnvRec`)
)

CREATE TABLE `capNrContrato` (
  `nrContrato` varchar(50) NOT NULL,
  PRIMARY KEY (`nrContrato`)
)

CREATE TABLE `capNrPlantao` (
  `id` int NOT NULL AUTO_INCREMENT,
  `opNrPlantao` int NOT NULL,
  PRIMARY KEY (`id`,`opNrPlantao`)
)

CREATE TABLE `capOperPlantaoCheckJustifica` (
  `pcJustificativa` varchar(100) NOT NULL,
  PRIMARY KEY (`pcJustificativa`)
)

CREATE TABLE `capOuvidoria` (
  `dsNome` varchar(200) NOT NULL,
  `dsEmail` varchar(200) DEFAULT NULL,
  `dsTelefone` varchar(50) DEFAULT NULL,
  `dsMensagem` longtext NOT NULL,
  `dtCriacao` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `dsProtocolo` varchar(100) DEFAULT NULL,
  `dsAssunto` longtext,
  PRIMARY KEY (`dtCriacao`,`dsNome`)
)

CREATE TABLE `capProfSaudeCliente` (
  `psCPF` varchar(11) NOT NULL,
  `clCliente` varchar(100) NOT NULL,
  `dtinclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`psCPF`,`clCliente`),
  KEY `fk_broProfSaudeCliente_broProfissionalSaude1_idx` (`psCPF`)
)

CREATE TABLE `capProfSaudeCliente_bkp` (
  `psCPF` varchar(11) NOT NULL,
  `clCliente` varchar(100) NOT NULL,
  `dtinclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP
)

CREATE TABLE `capProfSaudeEspecialidade` (
  `psCPF` varchar(11) NOT NULL,
  `esEspecialidade` varchar(100) NOT NULL,
  PRIMARY KEY (`psCPF`,`esEspecialidade`),
  KEY `fk_Branco_brEspecialidade1_idx` (`esEspecialidade`),
  KEY `fk_brProfSaudeEspecialidade_brProfissionalSaude1_idx` (`psCPF`)
)

CREATE TABLE `capProtocolo` (
  `nrProtocolo` int NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`nrProtocolo`)
)

CREATE TABLE `capSolicitacaoDPO` (
  `dsNome` varchar(200) NOT NULL,
  `dsEmail` varchar(200) DEFAULT NULL,
  `dsTelefone` varchar(50) DEFAULT NULL,
  `dsMensagem` longtext NOT NULL,
  `dtCriacao` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `dsProtocolo` varchar(100) DEFAULT NULL,
  `dsAssunto` longtext,
  PRIMARY KEY (`dtCriacao`,`dsNome`)
)

CREATE TABLE `capTipoAssinaturaEletronica` (
  `aeAssEletronicaTipo` varchar(10) NOT NULL COMMENT 'ecpf - ecnpj - elet',
  `aeAssEletronicaDescricao` varchar(100) DEFAULT NULL COMMENT 'e-CPF e-CNPJ Eletronica',
  PRIMARY KEY (`aeAssEletronicaTipo`)
)

CREATE TABLE `capTipoPIX` (
  `pxPIXTipo` varchar(10) NOT NULL COMMENT 'email cpf cnpj cel aleat',
  `pxPIXDescricao` varchar(200) DEFAULT NULL COMMENT 'eMail CPF CNPJ Celular Aleatoria',
  PRIMARY KEY (`pxPIXTipo`)
)

CREATE TABLE `capUsuario` (
  `usCPFUsuario` varchar(11) NOT NULL,
  `usNome` varchar(200) NOT NULL,
  `usSenha` varchar(100) DEFAULT NULL,
  `usNomeMae` varchar(200) DEFAULT NULL,
  `usDatNasc` date DEFAULT NULL,
  `usTelefone` varchar(20) NOT NULL,
  `usEmail` varchar(100) DEFAULT NULL,
  `usCEP` varchar(100) DEFAULT NULL,
  `usEndereco` varchar(100) DEFAULT NULL,
  `usNrEnd` varchar(20) DEFAULT NULL,
  `usComplEnd` varchar(200) DEFAULT NULL,
  `usBairro` varchar(100) DEFAULT NULL,
  `usCidade` varchar(100) DEFAULT NULL,
  `usUF` varchar(2) DEFAULT NULL,
  `usAtivo` int DEFAULT NULL,
  `usCargo` varchar(100) DEFAULT NULL,
  `usDepartamento` varchar(100) DEFAULT NULL,
  `usRegFuncional` varchar(100) DEFAULT NULL,
  `usNacionalidade` varchar(100) DEFAULT NULL,
  `usGenero` varchar(50) DEFAULT NULL,
  `usEstadoCivil` varchar(50) DEFAULT NULL,
  `usSenhaNova` int DEFAULT NULL,
  `usAceiteLGPD` int DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT NULL,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`usCPFUsuario`)
)

CREATE TABLE `brcGrupoMenuPerfilMenu` (
  `gmNome` varchar(100) NOT NULL,
  `pmNome` varchar(100) NOT NULL,
  PRIMARY KEY (`gmNome`,`pmNome`),
  KEY `fk_capGrupoMenuPerfilMenu_capPerfilMenu1_idx` (`pmNome`),
  CONSTRAINT `fk_capGrupoMenuPerfilMenu_capGrupoMenu1` FOREIGN KEY (`gmNome`) REFERENCES `brcGrupoMenu` (`gmNome`),
  CONSTRAINT `fk_capGrupoMenuPerfilMenu_capPerfilMenu1` FOREIGN KEY (`pmNome`) REFERENCES `brcPerfilMenu` (`pmNome`)
)

CREATE TABLE `brcPerfilMenuRotas` (
  `gmNome` varchar(100) NOT NULL,
  `mrNome` varchar(100) NOT NULL,
  `pmNome` varchar(200) NOT NULL,
  PRIMARY KEY (`gmNome`,`mrNome`,`pmNome`),
  KEY `fk_capPerfilMenuRotas_capMenuRotas1_idx` (`mrNome`),
  KEY `fk_capPerfilMenuRotas_capGrupoMenuPerfilMenu1_idx` (`gmNome`,`pmNome`),
  CONSTRAINT `fk_capPerfilMenuRotas_capGrupoMenuPerfilMenu1` FOREIGN KEY (`gmNome`, `pmNome`) REFERENCES `brcGrupoMenuPerfilMenu` (`gmNome`, `pmNome`),
  CONSTRAINT `fk_capPerfilMenuRotas_capMenuRotas1` FOREIGN KEY (`mrNome`) REFERENCES `brcMenuRotas` (`mrNome`)
)

CREATE TABLE `brcUsuarioGrupoMenu` (
  `sgCliente` varchar(10) NOT NULL,
  `usuarioCPF` varchar(11) NOT NULL,
  `sgInstSaude` varchar(100) NOT NULL,
  `gmNome` varchar(200) NOT NULL,
  `usuarioPermissao` varchar(1) DEFAULT NULL COMMENT 'pode atribuir permissao S N',
  PRIMARY KEY (`sgCliente`,`usuarioCPF`,`sgInstSaude`,`gmNome`),
  KEY `fk_capUsuarioPerfilMenu_capGrupoMenu1_idx` (`gmNome`),
  KEY `index_cpf` (`usuarioCPF`),
  CONSTRAINT `fk_capUsuarioPerfilMenu_capGrupoMenu1` FOREIGN KEY (`gmNome`) REFERENCES `brcGrupoMenu` (`gmNome`)
)

CREATE TABLE `brcUsuarioPerfilAcesso` (
  `usCPFUsuario` varchar(11) NOT NULL,
  `tpTipoUsuario` varchar(5) NOT NULL,
  `paPerfilAcesso` varchar(20) NOT NULL,
  PRIMARY KEY (`usCPFUsuario`,`tpTipoUsuario`,`paPerfilAcesso`),
  KEY `fk_brUsuarioPerfilAcesso_brPerfilAcesso1_idx` (`paPerfilAcesso`),
  KEY `fk_brUsuarioPerfilAcesso_brTipoUsuario1_idx` (`tpTipoUsuario`),
  CONSTRAINT `fk_brUsuarioPerfilAcesso_brUsuario1` FOREIGN KEY (`usCPFUsuario`) REFERENCES `capUsuario` (`usCPFUsuario`)
)

CREATE TABLE `capAditivoProfSaudeAntecipacao` (
  `anNrAntecipacao` int NOT NULL,
  `isInstSaude` varchar(100) NOT NULL,
  `laNome` varchar(100) NOT NULL,
  `esEspecialidade` varchar(100) NOT NULL,
  `ocNrContrato` varchar(50) NOT NULL,
  `clCliente` varchar(100) NOT NULL,
  `ceTipoPagamento` varchar(100) NOT NULL,
  `opNrPlantao` int NOT NULL,
  `psCPF` varchar(11) NOT NULL,
  `adDataSolicitacao` timestamp NOT NULL,
  PRIMARY KEY (`anNrAntecipacao`,`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`adDataSolicitacao`),
  KEY `fk_capAditivoProfSaudeAntecipacao_capAntecipacao1_idx` (`anNrAntecipacao`),
  KEY `fk_capAditivoProfSaudeAntecipacao_capAditivoProfSaude1_idx` (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`adDataSolicitacao`),
  CONSTRAINT `fk_capAditivoProfSaudeAntecipacao_capAntecipacao1` FOREIGN KEY (`anNrAntecipacao`) REFERENCES `capAntecipacao` (`anNrAntecipacao`)
)

CREATE TABLE `capAntecipacaoHistorico` (
  `id` int NOT NULL AUTO_INCREMENT,
  `anNrAntecipacao` int NOT NULL,
  `anKuaraStatus` varchar(100) NOT NULL,
  `dtInclusao` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `anNrAntecipacao` (`anNrAntecipacao`),
  CONSTRAINT `capAntecipacaoHistorico_ibfk_1` FOREIGN KEY (`anNrAntecipacao`) REFERENCES `capAntecipacao` (`anNrAntecipacao`)
)

CREATE TABLE `capAtendimentoFechamento` (
  `isInstSaude` varchar(100) NOT NULL,
  `laNome` varchar(100) NOT NULL,
  `esEspecialidade` varchar(100) NOT NULL,
  `ocNrContrato` varchar(50) NOT NULL,
  `clCliente` varchar(100) NOT NULL,
  `ceTipoPagamento` varchar(100) NOT NULL,
  `opNrPlantao` int NOT NULL,
  `psCPF` varchar(11) NOT NULL,
  `adDataSolicitacao` timestamp NOT NULL,
  `afNome` varchar(100) NOT NULL,
  `afIni` timestamp NOT NULL,
  `afFim` timestamp NOT NULL,
  `codFechamento` int DEFAULT NULL,
  `afValor` float DEFAULT NULL,
  `anNrAntecipacao` int DEFAULT NULL,
  `adSituacao` varchar(45) DEFAULT NULL,
  `afUsuarioAprovacao` varchar(11) DEFAULT NULL,
  `afDataAprovacao` timestamp NULL DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`adDataSolicitacao`,`afNome`,`afIni`),
  KEY `fk_capAtendimentoFechamento_capAditivoProfSaude1_idx` (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`adDataSolicitacao`),
  CONSTRAINT `fk_capAtendimentoFechamento_capAditivoProfSaude1` FOREIGN KEY (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`, `psCPF`, `adDataSolicitacao`) REFERENCES `capAditivoProfSaude` (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`, `psCPF`, `adDataSolicitacao`)
)

CREATE TABLE `capInstFinancCliente` (
  `clCliente` varchar(100) NOT NULL,
  `ifInstFinanceira` varchar(100) NOT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`clCliente`,`ifInstFinanceira`),
  KEY `fk_capInstFinancCliente_broCliente1_idx` (`clCliente`),
  KEY `fk_capInstFinancCliente_broInstFinanceira1` (`ifInstFinanceira`),
  CONSTRAINT `fk_capInstFinancCliente_broInstFinanceira1` FOREIGN KEY (`ifInstFinanceira`) REFERENCES `capInstFinanceira` (`ifInstFinanceira`)
)

CREATE TABLE `capInstSaudeContrato` (
  `isInstSaude` varchar(100) NOT NULL,
  `ocNrContrato` varchar(50) NOT NULL,
  `clCliente` varchar(100) NOT NULL,
  `ocDataContrato` timestamp NOT NULL,
  `acDataFimContrato` timestamp NULL DEFAULT NULL,
  `ocValorFixo` float DEFAULT NULL,
  `ocValorVariavel` float DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT NULL,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `ocValorUnitario` float DEFAULT NULL,
  PRIMARY KEY (`isInstSaude`,`ocNrContrato`,`clCliente`),
  KEY `fk_brOperadorContrato-_broCliente1_idx` (`clCliente`),
  KEY `fk_broInstSaudeContrato_broInstSaude1_idx` (`isInstSaude`),
  CONSTRAINT `fk_brOperadorContrato-_broCliente1` FOREIGN KEY (`clCliente`) REFERENCES `capCliente` (`clCliente`)
)

CREATE TABLE `capInstSaudeEspecialidade` (
  `isInstSaude` varchar(100) NOT NULL,
  `laNome` varchar(100) NOT NULL,
  `esEspecialidade` varchar(100) NOT NULL,
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`),
  KEY `fk_broInstSaudeEspecialidade_broInstSaudeLocalAtendimento1_idx` (`isInstSaude`,`laNome`),
  KEY `fk_broInstSaudeEspecialidade_broEspecialidade1_idx` (`esEspecialidade`),
  CONSTRAINT `fk_broInstSaudeEspecialidade_broInstSaudeLocalAtendimento1` FOREIGN KEY (`isInstSaude`, `laNome`) REFERENCES `capInstSaudeLocalAtendimento` (`isInstSaude`, `laNome`)
)

CREATE TABLE `capInstSaudePlantaoAgenda` (
  `isInstSaude` varchar(100) NOT NULL,
  `laNome` varchar(100) NOT NULL,
  `esEspecialidade` varchar(100) NOT NULL,
  `ocNrContrato` varchar(50) NOT NULL,
  `clCliente` varchar(100) NOT NULL,
  `ceTipoPagamento` varchar(100) NOT NULL,
  `opNrPlantao` int NOT NULL,
  `agData` date NOT NULL,
  `agDiaSem` int DEFAULT NULL,
  `agHoraIni` time DEFAULT NULL,
  `agHoraFim` time DEFAULT NULL,
  `agIntervalo` time DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `agAtivo` int DEFAULT '1',
  `agDataIni` datetime NOT NULL,
  `agDataFim` datetime NOT NULL,
  `agTipoValor` int DEFAULT '0' COMMENT '0 = Normal, 1 = extra1 (capInstSaudePlantao.opValorExtra1), 2 = extra2 (capInstSaudePlantao.opValorExtra2)',
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`agData`),
  CONSTRAINT `fk_capInstSaudePlantaoAgenda_capInstSaudePlantao1` FOREIGN KEY (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`) REFERENCES `capInstSaudePlantao` (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`)
)

CREATE TABLE `capOperPlantaoCheck` (
  `isInstSaude` varchar(100) NOT NULL,
  `laNome` varchar(100) NOT NULL,
  `esEspecialidade` varchar(100) NOT NULL,
  `ocNrContrato` varchar(50) NOT NULL,
  `clCliente` varchar(100) NOT NULL,
  `ceTipoPagamento` varchar(100) NOT NULL,
  `opNrPlantao` int NOT NULL,
  `agData` date NOT NULL,
  `ocCheckIn` timestamp NOT NULL,
  `ocIntervalo` time DEFAULT NULL,
  `ocCheckOut` timestamp NULL DEFAULT NULL,
  `ocQtRealizadas` time DEFAULT NULL,
  `ocQtAprovadas` time DEFAULT NULL,
  `ocQtGlosadas` time DEFAULT NULL,
  `pcJustificativa` varchar(100) DEFAULT NULL,
  `codFechamento` int DEFAULT NULL,
  `ocCheckAprovado` int NOT NULL DEFAULT '0',
  `ocUsuarioAprovacao` varchar(11) DEFAULT NULL,
  `ocSituacao` varchar(40) DEFAULT NULL COMMENT 'EnviadoAprovacao | Aprovado | Fechado',
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`agData`,`ocCheckIn`),
  KEY `fk_broOperPlantaoCheck_capOperPlantaoCheckJustifica1_idx` (`pcJustificativa`),
  KEY `fk_capOperPlantaoCheck_capInstSaudePlantaoAgenda1_idx` (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`agData`),
  CONSTRAINT `fk_broOperPlantaoCheck_capOperPlantaoCheckJustifica1` FOREIGN KEY (`pcJustificativa`) REFERENCES `capOperPlantaoCheckJustifica` (`pcJustificativa`),
  CONSTRAINT `fk_capOperPlantaoCheck_capInstSaudePlantaoAgenda1` FOREIGN KEY (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`, `agData`) REFERENCES `capInstSaudePlantaoAgenda` (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`, `agData`)
)

CREATE TABLE `capProfissionalSaude` (
  `psCPF` varchar(11) NOT NULL DEFAULT 'psCPF',
  `psRG` varchar(11) NOT NULL,
  `psContatoSecundario` varchar(100) DEFAULT NULL,
  `psCNES` varchar(100) DEFAULT NULL,
  `psCNS` varchar(100) DEFAULT NULL,
  `ccConselhoClasse` varchar(10) NOT NULL,
  `psCCNrReg` varchar(100) NOT NULL,
  `psCCCidadeReg` varchar(100) NOT NULL,
  `psCCUFReg` varchar(2) NOT NULL,
  `psCCOrgEmissor` varchar(100) NOT NULL,
  `bcBancoNR` varchar(4) DEFAULT NULL,
  `psAgenciaBanco` varchar(100) DEFAULT NULL,
  `psContaCorrente` varchar(100) DEFAULT NULL,
  `psAgenciaDigito` varchar(1) DEFAULT NULL,
  `psContaCorrenteDigito` varchar(1) DEFAULT NULL,
  `psContaCorrenteCNPJ` varchar(20) DEFAULT NULL,
  `pxPIXTipo` varchar(10) DEFAULT NULL,
  `psPIXChave` varchar(100) DEFAULT NULL,
  `aeAssEletronicaTipo` varchar(10) DEFAULT NULL,
  `psAssinaturaEletronica` varchar(400) DEFAULT NULL,
  `psAtivo` int DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT NULL,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`psCPF`),
  KEY `fk_brProfissionalSaude_brConselhoClasse1_idx` (`ccConselhoClasse`),
  KEY `fk_brProfissionalSaude_brTipoPIX1_idx` (`pxPIXTipo`),
  KEY `fk_brProfissionalSaude_brTipoAssinaturaEletronica1_idx` (`aeAssEletronicaTipo`),
  KEY `fk_brProfissionalSaude_brBancos1_idx` (`bcBancoNR`),
  CONSTRAINT `fk_brProfissionalSaude_brBancos1` FOREIGN KEY (`bcBancoNR`) REFERENCES `capBanco` (`bcBancoNR`),
  CONSTRAINT `fk_brProfissionalSaude_brTipoPIX1` FOREIGN KEY (`pxPIXTipo`) REFERENCES `capTipoPIX` (`pxPIXTipo`),
  CONSTRAINT `fk_brProfissionalSaude_brUsuario1` FOREIGN KEY (`psCPF`) REFERENCES `capUsuario` (`usCPFUsuario`)
)