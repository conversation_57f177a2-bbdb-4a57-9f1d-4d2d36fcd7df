# Prompt para Reformulação do Sistema GS2 - Gestão de Antecipação de Recebíveis

## Visão Geral do Sistema

O sistema GS2 é uma plataforma completa para gestão de profissionais de saúde, contratos, plantões, antecipações de recebíveis e integração com WhatsApp. Baseado na análise do código existente, o sistema possui as seguintes funcionalidades principais:

### Módulos Principais Identificados:

1. **Autenticação e Autorização**
   - Login com JWT
   - Grupos de usuários (Gestor Capitale, Gestor Cliente, Profissional Saúde, Escalista) - (Eu acho melhor ter master, admin, profissional e atendente)
   - Validação de tokens e renovação

2. **Gestão de Usuários e Profissionais**
   - Cadastro de profissionais de saúde
   - Gestão de especialidades
   - Dados bancários e PIX
   - Relacionamento com clientes

3. **Gestão de Clientes e Instituições**
   - Cadastro de clientes (hospitais/clínicas)
   - Instituições de saúde
   - Locais de atendimento
   - Contratos e especialidades por local

4. **Gestão de Plantões e Atendimentos**
   - Criação de plantões
   - Agenda de plantões
   - Sistema de check-in/check-out
   - Solicitações e aprovações
   - Diferentes tipos de pagamento (Hora, Fixo, Unitário)

5. **Sistema de Fechamentos**
   - Fechamentos automáticos (diário, semanal, mensal)
   - Aprovação de horas trabalhadas
   - Cálculo de valores

6. **Antecipação de Recebíveis**
   - Solicitação de antecipações
   - Cálculo de deságio
   - Integração com sistema financeiro (Kuara)
   - Geração de contratos em PDF

7. **Integração WhatsApp**
   - Webhook para recebimento de mensagens
   - Envio de confirmações de plantão
   - Templates de mensagens

8. **Relatórios e Dashboards**
   - Gráficos da plataforma
   - Relatórios de fechamentos
   - Dados de profissionais

## Requisitos Técnicos

### Stack Tecnológica Desejada:
- **Backend**: NestJS (TypeScript)
- **Banco de Dados**: MySQL 8.0+
- **ORM**: TypeORM
- **Autenticação**: JWT + Passport
- **Documentação**: Swagger/OpenAPI
- **Validação**: class-validator
- **Estrutura**: Arquitetura modular com módulos bem definidos

### Arquitetura do Banco de Dados (Redesenhada)

Redesenhe completamente a modelagem considerando:

#### 1. **Módulo de Usuários**
```sql
-- Tabela principal de usuários
users (
  id UUID PRIMARY KEY,
  cpf VARCHAR(11) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  birth_date DATE,
  mother_name VARCHAR(255),
  address JSONB, -- Endereço completo
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)

-- Grupos/Roles
user_groups (
  id UUID PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  permissions JSONB
)

-- Relacionamento usuário-grupo
user_group_memberships (
  user_id UUID REFERENCES users(id),
  group_id UUID REFERENCES user_groups(id),
  PRIMARY KEY (user_id, group_id)
)