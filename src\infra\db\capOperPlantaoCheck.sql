-- Este script é responsável por alterar a tabela capOperPlantaoCheck
-- Adicionando a coluna 'ocCheckAprovado', 'ocUsuarioAprovacao' e 'ocSituacao'

insert into capOperPlantaoCheckJustifica (pcJustificativa) values ('Lançamento Horas a Maior');
insert into capOperPlantaoCheckJustifica (pcJustificativa) values ('Lançamento Horas a Menor');
insert into capOperPlantaoCheckJustifica (pcJustificativa) values ('Glosada pela Instituicao de Saúde');
insert into capOperPlantaoCheckJustifica (pcJustificativa) values ('Glosada pela quantidade contratada');

CREATE TABLE capOperPlantaoCheck_bkp
SELECT * FROM capOperPlantaoCheck;

DROP TABLE capOperPlantaoCheck;

CREATE TABLE capOperPlantaoCheck (
  `isInstSaude` VARCHAR(100) NOT NULL,
  `laNome` VARCHAR(100) NOT NULL,
  `esEspecialidade` VARCHAR(100) NOT NULL,
  `ocNrContrato` VARCHAR(50) NOT NULL,
  `clCliente` VARCHAR(100) NOT NULL,
  `ceTipoPagamento` VARCHAR(100) NOT NULL,
  `opNrPlantao` INT NOT NULL,
  `agData` DATE NOT NULL,
  `ocCheckIn` TIMESTAMP NOT NULL,
  `ocCheckOut` TIMESTAMP NULL,
  `ocQtRealizadas` TIME NULL,
  `ocQtAprovadas` TIME NULL,
  `ocQtGlosadas` TIME NULL,
  `pcJustificativa` VARCHAR(100) NULL,
  `codFechamento` INT NULL,
  `ocCheckAprovado` INT NOT NULL DEFAULT 0,
  `ocUsuarioAprovacao` VARCHAR(11) NULL,
  `ocSituacao` VARCHAR(40) NULL COMMENT 'EnviadoAprovacao | Aprovado | Fechado',
  PRIMARY KEY (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`, `agData`, `ocCheckIn`),
  INDEX `fk_broOperPlantaoCheck_capOperPlantaoCheckJustifica1_idx` (`pcJustificativa` ASC) VISIBLE,
  INDEX `fk_capOperPlantaoCheck_capInstSaudePlantaoAgenda1_idx` (`isInstSaude` ASC, `laNome` ASC, `esEspecialidade` ASC, `ocNrContrato` ASC, `clCliente` ASC, `ceTipoPagamento` ASC, `opNrPlantao` ASC, `agData` ASC) VISIBLE,
  CONSTRAINT `fk_broOperPlantaoCheck_capOperPlantaoCheckJustifica1`
    FOREIGN KEY (`pcJustificativa`)
    REFERENCES `capOperPlantaoCheckJustifica` (`pcJustificativa`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_capOperPlantaoCheck_capInstSaudePlantaoAgenda1`
    FOREIGN KEY (`isInstSaude` , `laNome` , `esEspecialidade` , `ocNrContrato` , `clCliente` , `ceTipoPagamento` , `opNrPlantao` , `agData`)
    REFERENCES `capInstSaudePlantaoAgenda` (`isInstSaude` , `laNome` , `esEspecialidade` , `ocNrContrato` , `clCliente` , `ceTipoPagamento` , `opNrPlantao` , `agData`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE capOperPlantaoCheck_bkp;