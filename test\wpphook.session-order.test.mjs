import { describe, test, expect, beforeEach, jest } from '@jest/globals';

// Mock dependencies
const mockDatabaseInstance = {
    initialize: jest.fn(),
    getUserSession: jest.fn(),
    createUserSession: jest.fn(),
    updateUserSession: jest.fn(),
    logInteraction: jest.fn(),
    findClinic: jest.fn()
};

const mockMessageBuilder = {
    buildGreeting: jest.fn(() => 'Hello!'),
    buildInitialHelp: jest.fn(() => 'Help message')
};

jest.unstable_mockModule('../src/lambdas/gs2api/wpphook/database.mjs', () => ({
    default: jest.fn(() => mockDatabaseInstance)
}));

jest.unstable_mockModule('../src/lambdas/gs2api/wpphook/utils/messageBuilder.mjs', () => ({
    default: jest.fn(() => mockMessageBuilder)
}));

jest.unstable_mockModule('../src/lambdas/gs2api/wpphook/utils/validators.mjs', () => ({
    default: {
        sanitizeInput: jest.fn(input => input),
        isExitCommand: jest.fn(() => false),
        isGS2Trigger: jest.fn(() => true)
    }
}));

const { StateMachine, STATES } = await import('../src/lambdas/gs2api/wpphook/services/stateMachine.mjs');

describe('Session Creation Order Test', () => {
    let stateMachine;

    beforeEach(() => {
        jest.clearAllMocks();
        stateMachine = new StateMachine(mockDatabaseInstance, mockMessageBuilder);
        
        // Mock session creation to return session with id
        mockDatabaseInstance.createUserSession.mockResolvedValue({
            id: 123,
            user_id: '5518988156051',
            phone_number: '5518988156051',
            current_state: 'INITIAL',
            session_data: {},
            created_at: new Date(),
            updated_at: new Date()
        });
    });

    test('should create session before logging interaction', async () => {
        // Mock no existing session
        mockDatabaseInstance.getUserSession.mockResolvedValue(null);
        
        const userId = '5518988156051';
        const message = 'GS2';
        const phoneNumber = '5518988156051';

        await stateMachine.processMessage(userId, message, phoneNumber);

        // Verify order of operations
        const calls = [
            ...mockDatabaseInstance.getUserSession.mock.calls.map(() => 'getUserSession'),
            ...mockDatabaseInstance.createUserSession.mock.calls.map(() => 'createUserSession'),
            ...mockDatabaseInstance.logInteraction.mock.calls.map(() => 'logInteraction')
        ];

        expect(calls).toEqual(['getUserSession', 'createUserSession', 'logInteraction', 'logInteraction']);
        
        // Verify logInteraction is called with session.id, not userId
        expect(mockDatabaseInstance.logInteraction).toHaveBeenCalledWith(123, 'INCOMING', message);
        expect(mockDatabaseInstance.logInteraction).toHaveBeenCalledWith(123, 'OUTGOING', 'Hello!');
    });

    test('should use existing session id for logging', async () => {
        // Mock existing session
        const existingSession = {
            id: 456,
            user_id: '5518988156051',
            phone_number: '5518988156051',
            current_state: 'GREETING',
            session_data: {},
            created_at: new Date(),
            updated_at: new Date()
        };
        
        mockDatabaseInstance.getUserSession.mockResolvedValue(existingSession);
        
        const userId = '5518988156051';
        const message = 'Hello';
        const phoneNumber = '5518988156051';

        await stateMachine.processMessage(userId, message, phoneNumber);

        // Should use existing session id (456) for logging
        expect(mockDatabaseInstance.logInteraction).toHaveBeenCalledWith(456, 'INCOMING', message);
        expect(mockDatabaseInstance.createUserSession).not.toHaveBeenCalled();
    });
});