-- =====================================================
-- SCRIPT DE VALIDAÇÃO PÓS-MIGRAÇÃO
-- Verificações de integridade e consistência dos dados
-- =====================================================

-- Configurações para relatório
SET @inicio_validacao = NOW();
SET @total_erros = 0;

-- =====================================================
-- 1. VALIDAÇÃO DE CONTAGEM DE REGISTROS
-- =====================================================

SELECT '=== VALIDAÇÃO DE CONTAGEM DE REGISTROS ===' as secao;

-- Usu<PERSON>rios
SELECT 
    'USUÁRIOS' as tabela,
    (SELECT COUNT(*) FROM capUsuario) as antigo,
    (SELECT COUNT(*) FROM users) as novo,
    CASE 
        WHEN (SELECT COUNT(*) FROM capUsuario) = (SELECT COUNT(*) FROM users) 
        THEN '✅ OK' 
        ELSE '❌ ERRO' 
    END as status;

-- Profissionais de Saúde
SELECT 
    'PROFISSIONAIS' as tabela,
    (SELECT COUNT(*) FROM capProfissionalSaude) as antigo,
    (SELECT COUNT(*) FROM health_professionals) as novo,
    CASE 
        WHEN (SELECT COUNT(*) FROM capProfissionalSaude) = (SELECT COUNT(*) FROM health_professionals) 
        THEN '✅ OK' 
        ELSE '❌ ERRO' 
    END as status;

-- Clientes
SELECT 
    'CLIENTES' as tabela,
    (SELECT COUNT(*) FROM capCliente) as antigo,
    (SELECT COUNT(*) FROM clients) as novo,
    CASE 
        WHEN (SELECT COUNT(*) FROM capCliente) = (SELECT COUNT(*) FROM clients) 
        THEN '✅ OK' 
        ELSE '❌ ERRO' 
    END as status;

-- Instituições de Saúde
SELECT 
    'INSTITUIÇÕES' as tabela,
    (SELECT COUNT(*) FROM capInstSaude) as antigo,
    (SELECT COUNT(*) FROM health_institutions) as novo,
    CASE 
        WHEN (SELECT COUNT(*) FROM capInstSaude) = (SELECT COUNT(*) FROM health_institutions) 
        THEN '✅ OK' 
        ELSE '❌ ERRO' 
    END as status;

-- Plantões
SELECT 
    'PLANTÕES' as tabela,
    (SELECT COUNT(*) FROM capInstSaudePlantao) as antigo,
    (SELECT COUNT(*) FROM shifts) as novo,
    CASE 
        WHEN (SELECT COUNT(*) FROM capInstSaudePlantao) = (SELECT COUNT(*) FROM shifts) 
        THEN '✅ OK' 
        ELSE '❌ ERRO' 
    END as status;

-- Antecipações
SELECT 
    'ANTECIPAÇÕES' as tabela,
    (SELECT COUNT(*) FROM capAntecipacao) as antigo,
    (SELECT COUNT(*) FROM advances) as novo,
    CASE 
        WHEN (SELECT COUNT(*) FROM capAntecipacao) = (SELECT COUNT(*) FROM advances) 
        THEN '✅ OK' 
        ELSE '❌ ERRO' 
    END as status;

-- =====================================================
-- 2. VALIDAÇÃO DE INTEGRIDADE REFERENCIAL
-- =====================================================

SELECT '=== VALIDAÇÃO DE INTEGRIDADE REFERENCIAL ===' as secao;

-- Verificar se todos os profissionais têm usuários válidos
SELECT 
    'PROFISSIONAIS -> USUÁRIOS' as relacao,
    COUNT(*) as registros_orfaos,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ OK' 
        ELSE '❌ ERRO' 
    END as status
FROM health_professionals hp
LEFT JOIN users u ON u.id = hp.user_id
WHERE u.id IS NULL;

-- Verificar se todos os shifts têm contract_specialties válidos
SELECT 
    'PLANTÕES -> ESPECIALIDADES CONTRATO' as relacao,
    COUNT(*) as registros_orfaos,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ OK' 
        ELSE '❌ ERRO' 
    END as status
FROM shifts s
LEFT JOIN contract_specialties cs ON cs.id = s.contract_specialty_id
WHERE cs.id IS NULL;

-- Verificar se todos os advances têm profissionais válidos
SELECT 
    'ANTECIPAÇÕES -> PROFISSIONAIS' as relacao,
    COUNT(*) as registros_orfaos,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ OK' 
        ELSE '❌ ERRO' 
    END as status
FROM advances a
LEFT JOIN health_professionals hp ON hp.id = a.health_professional_id
WHERE hp.id IS NULL;

-- =====================================================
-- 3. VALIDAÇÃO DE DADOS CRÍTICOS
-- =====================================================

SELECT '=== VALIDAÇÃO DE DADOS CRÍTICOS ===' as secao;

-- Verificar CPFs únicos
SELECT 
    'CPFs ÚNICOS' as validacao,
    COUNT(*) - COUNT(DISTINCT cpf) as duplicados,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT cpf) THEN '✅ OK' 
        ELSE '❌ ERRO' 
    END as status
FROM users;

-- Verificar emails únicos
SELECT 
    'EMAILS ÚNICOS' as validacao,
    COUNT(*) - COUNT(DISTINCT email) as duplicados,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT email) THEN '✅ OK' 
        ELSE '❌ ERRO' 
    END as status
FROM users;

-- Verificar UUIDs únicos em todas as tabelas principais
SELECT 
    'UUIDs ÚNICOS - USERS' as validacao,
    COUNT(*) - COUNT(DISTINCT uuid) as duplicados,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT uuid) THEN '✅ OK' 
        ELSE '❌ ERRO' 
    END as status
FROM users;

-- Verificar valores monetários não negativos
SELECT 
    'VALORES MONETÁRIOS VÁLIDOS' as validacao,
    COUNT(*) as valores_negativos,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ OK' 
        ELSE '❌ ERRO' 
    END as status
FROM advances 
WHERE requested_amount < 0 OR approved_amount < 0 OR net_amount < 0;

-- =====================================================
-- 4. VALIDAÇÃO DE PERFORMANCE
-- =====================================================

SELECT '=== VALIDAÇÃO DE PERFORMANCE ===' as secao;

-- Testar consulta complexa no schema novo
SET @inicio_query = NOW();

SELECT COUNT(*) as total_plantoes_com_profissionais
FROM shifts s
JOIN contract_specialties cs ON cs.id = s.contract_specialty_id
JOIN contracts co ON co.id = cs.contract_id
JOIN health_institutions hi ON hi.id = co.health_institution_id
JOIN clients cl ON cl.id = co.client_id
LEFT JOIN health_professionals hp ON hp.id = s.health_professional_id
LEFT JOIN users u ON u.id = hp.user_id;

SET @fim_query = NOW();

SELECT 
    'CONSULTA COMPLEXA' as teste,
    TIMESTAMPDIFF(MICROSECOND, @inicio_query, @fim_query) / 1000 as tempo_ms,
    CASE 
        WHEN TIMESTAMPDIFF(MICROSECOND, @inicio_query, @fim_query) / 1000 < 1000 
        THEN '✅ RÁPIDA' 
        ELSE '⚠️ LENTA' 
    END as status;

-- =====================================================
-- 5. VALIDAÇÃO DE ESTRUTURA
-- =====================================================

SELECT '=== VALIDAÇÃO DE ESTRUTURA ===' as secao;

-- Verificar se todas as tabelas principais têm campos de auditoria
SELECT 
    table_name,
    CASE 
        WHEN column_name IS NOT NULL THEN '✅ OK' 
        ELSE '❌ FALTANDO' 
    END as created_at_status
FROM information_schema.tables t
LEFT JOIN information_schema.columns c ON c.table_name = t.table_name 
    AND c.column_name = 'created_at' 
    AND c.table_schema = DATABASE()
WHERE t.table_schema = DATABASE() 
    AND t.table_type = 'BASE TABLE'
    AND t.table_name NOT LIKE '%_old'
    AND t.table_name NOT LIKE '%_bkp'
ORDER BY t.table_name;

-- Verificar foreign keys
SELECT 
    'FOREIGN KEYS' as validacao,
    COUNT(*) as total_fks,
    CASE 
        WHEN COUNT(*) > 20 THEN '✅ OK' 
        ELSE '❌ INSUFICIENTES' 
    END as status
FROM information_schema.key_column_usage 
WHERE table_schema = DATABASE() 
    AND referenced_table_name IS NOT NULL;

-- =====================================================
-- 6. RELATÓRIO FINAL
-- =====================================================

SELECT '=== RELATÓRIO FINAL ===' as secao;

SET @fim_validacao = NOW();

SELECT 
    'TEMPO TOTAL DE VALIDAÇÃO' as metrica,
    TIMESTAMPDIFF(SECOND, @inicio_validacao, @fim_validacao) as segundos,
    '✅ CONCLUÍDO' as status;

-- Resumo de tabelas migradas
SELECT 
    'TABELAS NO NOVO SCHEMA' as metrica,
    COUNT(*) as quantidade,
    '📊 INVENTÁRIO' as status
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
    AND table_type = 'BASE TABLE'
    AND table_name NOT LIKE 'cap%'
    AND table_name NOT LIKE 'brc%';

-- Verificação final de integridade
SELECT 
    'INTEGRIDADE GERAL' as validacao,
    CASE 
        WHEN (
            SELECT COUNT(*) FROM information_schema.key_column_usage 
            WHERE table_schema = DATABASE() 
                AND referenced_table_name IS NOT NULL
        ) > 20 
        AND (
            SELECT COUNT(*) FROM users
        ) > 0
        THEN '✅ SISTEMA ÍNTEGRO' 
        ELSE '❌ PROBLEMAS DETECTADOS' 
    END as status;

-- =====================================================
-- 7. RECOMENDAÇÕES PÓS-MIGRAÇÃO
-- =====================================================

SELECT '=== RECOMENDAÇÕES PÓS-MIGRAÇÃO ===' as secao;

SELECT 
    '1. OTIMIZAR ÍNDICES' as recomendacao,
    'ANALYZE TABLE para todas as tabelas' as acao,
    'ALTA' as prioridade;

SELECT 
    '2. ATUALIZAR ESTATÍSTICAS' as recomendacao,
    'OPTIMIZE TABLE para tabelas grandes' as acao,
    'MÉDIA' as prioridade;

SELECT 
    '3. MONITORAR PERFORMANCE' as recomendacao,
    'Configurar slow query log' as acao,
    'ALTA' as prioridade;

SELECT 
    '4. BACKUP INCREMENTAL' as recomendacao,
    'Configurar backup automático' as acao,
    'CRÍTICA' as prioridade;

SELECT 
    '✅ VALIDAÇÃO CONCLUÍDA COM SUCESSO!' as resultado,
    NOW() as timestamp_conclusao;
