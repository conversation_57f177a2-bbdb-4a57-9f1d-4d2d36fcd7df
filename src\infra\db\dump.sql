CREATE TABLE `brcGrupoMenu` (
  `gmNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `gmDescricao` varchar(600) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `gmTipo` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT 'M=Mobile - O=Operacional - G=Gestao - A=Administrativo - T=Tecnologia - C=CS - F=Financeiro',
  PRIMARY KEY (`gmNome`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;


CREATE TABLE `brcMenuAcesso` (
  `maMenu` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `maDescricao` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `rtNome` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `maAtivo` int DEFAULT NULL,
  `maMenuPai` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`maMenu`),
  KEY `fk_brMenuAcesso_brRotas1_idx` (`rtNome`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `brcMenuRotas` (
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `mrNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `mrNomeTela` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `mrRota` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `mrOrdemMenu` int DEFAULT NULL,
  PRIMARY KEY (`mrNome`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `brcPerfilAcesso` (
  `paPerfilAcesso` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `paDescrição` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `paAtivo` int DEFAULT NULL,
  PRIMARY KEY (`paPerfilAcesso`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `brcPerfilMenu` (
  `pmNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `pmNomeTela` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`pmNome`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `brcRotas` (
  `rtNome` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `rtDescricao` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `rtExecutavel` int NOT NULL,
  `rtAtivo` int DEFAULT NULL,
  PRIMARY KEY (`rtNome`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `brcTipoUsuario` (
  `tpTipoUsuario` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'gcon = gestor de contrato - profs = profissional de saude - admin = administrador - opadm = administrador operador - opope = operacional do operador - opesc = escalista do operador - fiadm = administrador financeira - finope = operacional da financeira',
  `tpDescricao` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`tpTipoUsuario`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capAditivoProfSaude` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ceTipoPagamento` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `opNrPlantao` int NOT NULL,
  `psCPF` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `adDataSolicitacao` timestamp NOT NULL,
  `adDataAprovacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `usCPFUsuarioAprovador` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `adValorFixo` float DEFAULT NULL,
  `adValor` float DEFAULT NULL,
  `adValorLiquido` float DEFAULT NULL,
  `adValorBruto` float DEFAULT NULL,
  `adDataVencimento` date DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`adDataSolicitacao`),
  KEY `fk_brAditivoOperadorProfSaude-_broProfissionalSaude1_idx` (`psCPF`),
  KEY `fk_capAditivoProfSaude_capInstSaudePlantao1_idx` (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capAntecipacao` (
  `anNrAntecipacao` int NOT NULL AUTO_INCREMENT,
  `anDataSolicitacao` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `psCPF` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ifInstFinanceira` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `icNomeAprovador` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `anDataAprovacao` timestamp NULL DEFAULT NULL,
  `anValorSolicitado` float NOT NULL,
  `anKuaraId` varchar(100) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `anKuaraStatus` varchar(100) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `anKuaraDataPagamento` date DEFAULT NULL,
  `anValorAprovado` float DEFAULT NULL,
  `anValorLiquido` float DEFAULT NULL,
  `anTokenAprovacao` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usCPFUsuarioAprovador` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `anTaxaAntecipacao` float DEFAULT NULL,
  `opDataRecebimento` date DEFAULT NULL,
  `idEnvelope` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `anValorBruto` float DEFAULT NULL,
  PRIMARY KEY (`anNrAntecipacao`),
  KEY `fk_brAntecipacao_brInstFinanceira1_idx` (`ifInstFinanceira`),
  KEY `fk_brAntecipacao_ifInstFinancContato1_idx` (`ifInstFinanceira`,`usCPFUsuarioAprovador`),
  KEY `ix1_psCPF` (`psCPF`)
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capAtendimentoFechaAditivo` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ceTipoPagamento` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `opNrPlantao` int NOT NULL,
  `psCPF` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `adDataSolicitacao` timestamp NOT NULL,
  `afNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `afIni` timestamp NOT NULL,
  `adValorFixo` float DEFAULT NULL,
  `adValor` float DEFAULT NULL,
  `adValorLiquido` float DEFAULT NULL,
  `adValorBruto` float DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capAtendimentoFechaAditivo_bkp` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ceTipoPagamento` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `opNrPlantao` int NOT NULL,
  `psCPF` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `adDataSolicitacao` timestamp NOT NULL,
  `afNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `afIni` timestamp NOT NULL,
  `adValorFixo` float DEFAULT NULL,
  `adValor` float DEFAULT NULL,
  `adValorLiquido` float DEFAULT NULL,
  `adValorBruto` float DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capAtendimentoFechamento_bkp` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ceTipoPagamento` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `opNrPlantao` int NOT NULL,
  `afNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `afIni` timestamp NOT NULL,
  `afFim` timestamp NOT NULL,
  `afSituacao` varchar(45) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `codFechamento` int DEFAULT NULL,
  `anNrAntecipacao` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capBanco` (
  `bcBancoNR` varchar(4) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `bcBancoNome` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`bcBancoNR`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capCliente` (
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clNomeCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clTelefone` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clEmail` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCEP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `clEndereco` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `clNrEnd` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `clComplEnd` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `clBairro` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `clCidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `clUF` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `clAtivo` int DEFAULT '1',
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT NULL,
  `clNomeContato` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`clCliente`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capClienteUsuario` (
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `usCPFUsuario` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ucGestor` int DEFAULT NULL,
  `dtinclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`clCliente`,`usCPFUsuario`),
  KEY `fk_brGestorContratoUsuario_brUsuario1_idx` (`usCPFUsuario`),
  KEY `fk_broClienteUsuario_broCliente1_idx` (`clCliente`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capCodFechamento` (
  `codFechamento` int NOT NULL AUTO_INCREMENT COMMENT 'codigo sequencial para os fechamentos',
  PRIMARY KEY (`codFechamento`)
) ENGINE=InnoDB AUTO_INCREMENT=685 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capConfiguracao` (
  `chave` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `valor` text COLLATE utf8mb3_unicode_ci NOT NULL,
  `descricao` text COLLATE utf8mb3_unicode_ci,
  `tipo` enum('string','number','boolean','json') COLLATE utf8mb3_unicode_ci DEFAULT 'string',
  `dataAtualizacao` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`chave`),
  UNIQUE KEY `chave` (`chave`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capConselhoClasse` (
  `ccConselhoClasse` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ccDenominacao` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`ccConselhoClasse`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capEspecialidade` (
  `esEspecialidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  PRIMARY KEY (`esEspecialidade`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capFaleConosco` (
  `dsNome` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `dsEmail` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `dsTelefone` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `dsMensagem` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `dtCriacao` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `dsProtocolo` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `dsAssunto` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  PRIMARY KEY (`dtCriacao`,`dsNome`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capInstFinancContato` (
  `ifInstFinanceira` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `usCPFUsuario` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ifInstFinanceira`,`usCPFUsuario`),
  KEY `fk_ifInstFinancContato_brInstFinanceira1_idx` (`ifInstFinanceira`),
  KEY `fk_ifInstFinancContato_brUsuario1_idx` (`usCPFUsuario`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capInstFinanceira` (
  `ifInstFinanceira` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ifNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ifTelefone` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ifCEP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `ifEndereco` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ifNrEnd` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ifComplEnd` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `ifBairro` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ifCidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ifUF` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `isAtivo` int DEFAULT '1',
  PRIMARY KEY (`ifInstFinanceira`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capInstSaude` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'CNPJ',
  `isNome` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `isEmail` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `isCEP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `isEndereco` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `isNrEnd` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `isComplEnd` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `isBairro` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `isCidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `isUF` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `isCodANS` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `isAtivo` int DEFAULT '1',
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capInstSaudeContratoEspec` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ceTipoPagamento` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'Unitario - Hora - Fixo',
  `ceValorHora` float DEFAULT NULL,
  `ceQtdHoraMes` float DEFAULT NULL,
  `ceValorFixoProf` float DEFAULT NULL,
  `ceValorHoraProf` float DEFAULT NULL,
  `ceValorUnitProf` float DEFAULT NULL,
  `ceTaxaDesagio` float DEFAULT NULL,
  `cdDiaRecebimento` int DEFAULT NULL,
  `ceQtdDiasMinRecebim` int DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`),
  KEY `fk_broInstSaudeContratoEspec_broInstSaudeContrato1_idx` (`isInstSaude`,`ocNrContrato`,`clCliente`) /*!80000 INVISIBLE */
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capInstSaudeLocalAtendimento` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `laCEP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `laLat` decimal(10,8) DEFAULT NULL,
  `laLong` decimal(11,8) DEFAULT NULL,
  `laEndereco` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `laNrEnd` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `laComplEnd` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `laBairro` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `laCidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `laUF` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `laDescricao` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`,`laNome`),
  KEY `fk_brOperadorLocalAtendimento-_broInstSaude1_idx` (`isInstSaude`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capInstSaudePlSolicitAprovado` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ceTipoPagamento` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `opNrPlantao` int NOT NULL,
  `psCPF` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `psSequenciaSolicitacao` int NOT NULL,
  `opSituacao` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `saDataEvento` timestamp NOT NULL,
  `saUsuarioEvento` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`psSequenciaSolicitacao`,`opSituacao`,`saDataEvento`),
  KEY `fk_capInstSaudePlSolicitAprovado_capInstSaudePlantaoSolicit_idx` (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`psSequenciaSolicitacao`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capInstSaudePlantao` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ceTipoPagamento` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `opNrPlantao` int NOT NULL,
  `usCPFUsuarioAprovador` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `opDataDivulgacao` timestamp NULL DEFAULT NULL,
  `opAtivo` int DEFAULT NULL,
  `opTipoFechamento` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT 'manual, diario, semanal, mensal',
  `opDataFechamento` timestamp NULL DEFAULT NULL,
  `opPeriodoIni` timestamp NOT NULL,
  `opPeriodoFim` timestamp NOT NULL,
  `psCPF` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `opDatarecebimento` date DEFAULT NULL,
  `opDataAntecipacao` date DEFAULT NULL,
  `opQtdHorasRequisitada` int NOT NULL,
  `opQtdHorasRealizadas` time DEFAULT NULL,
  `opValorFixo` float DEFAULT NULL,
  `opValorHora` float DEFAULT NULL,
  `opValorUnit` float DEFAULT NULL,
  `opChaveAcesso` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `opSituacao` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT 'Aberto, Solicitado, Aprovado, Executado, AprovExecucao, Antecipado',
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `opDiaFechamento` int DEFAULT NULL,
  `opValorExtra1` float DEFAULT NULL,
  `opValorExtra2` float DEFAULT NULL,
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`),
  KEY `fk_capInstSaudePlantao_capInstSaudeContratoEspec1_idx` (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capInstSaudePlantaoSolicitacao` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ceTipoPagamento` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `opNrPlantao` int NOT NULL,
  `psCPF` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `psSequenciaSolicitacao` int NOT NULL,
  `psDataSolicitacao` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `psSituacao` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'Solicitado Rejeitado Aprovado Desistente',
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`psSequenciaSolicitacao`),
  KEY `fk_broInstSaudePlantaoSolicitacao_broProfissionalSaude1_idx` (`psCPF`),
  KEY `fk_capInstSaudePlantaoSolicitacao_capInstSaudePlantao1_idx` (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capInstSaudeUsuario` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `usCPFUsuario` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`,`usCPFUsuario`),
  KEY `fk_broInstSaudeUsuario_broInstSaude1_idx` (`isInstSaude`),
  KEY `fk_broInstSaudeUsuario_broUsuario1_idx` (`usCPFUsuario`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capMensagemWhatsapp` (
  `mwTelefone` varchar(27) COLLATE utf8mb3_unicode_ci NOT NULL,
  `mwDataMens` timestamp NOT NULL,
  `mwEnvRec` varchar(1) COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'E = Enviada   R = Recebida',
  `mwTelefoneFrom` varchar(27) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `mwType` varchar(50) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `mwpayload` varchar(100) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `mwText` longtext COLLATE utf8mb3_unicode_ci,
  `mwPayloadEnviado` json DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`mwTelefone`,`mwDataMens`,`mwEnvRec`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capMensageria` (
  `meCodigo` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `meModeloMeta` varchar(100) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `meTitulo` varchar(200) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `meEvento` varchar(200) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `meMensagem` varchar(4000) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `meFrequencia` varchar(1) COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT 'M minuto - H hora - D dia - U unico',
  PRIMARY KEY (`meCodigo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capNrContrato` (
  `nrContrato` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  PRIMARY KEY (`nrContrato`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capNrPlantao` (
  `id` int NOT NULL AUTO_INCREMENT,
  `opNrPlantao` int NOT NULL,
  PRIMARY KEY (`id`,`opNrPlantao`)
) ENGINE=InnoDB AUTO_INCREMENT=179 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capOperPlantaoCheckJustifica` (
  `pcJustificativa` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  PRIMARY KEY (`pcJustificativa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capOperPlantaoCheck_bkp` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ceTipoPagamento` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `opNrPlantao` int NOT NULL,
  `psCPF` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `psSequenciaSolicitacao` int NOT NULL,
  `ocCheckIn` timestamp NOT NULL,
  `ocCheckOut` timestamp NULL DEFAULT NULL,
  `ocQtRealizadas` time DEFAULT NULL,
  `ocQtAprovadas` time DEFAULT NULL,
  `ocQtGlosadas` time DEFAULT NULL,
  `pcJustificativa` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `codFechamento` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capOuvidoria` (
  `dsNome` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `dsEmail` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `dsTelefone` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `dsMensagem` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `dtCriacao` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `dsProtocolo` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `dsAssunto` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  PRIMARY KEY (`dtCriacao`,`dsNome`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capProfSaudeCliente` (
  `psCPF` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `dtinclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`psCPF`,`clCliente`),
  KEY `fk_broProfSaudeCliente_broProfissionalSaude1_idx` (`psCPF`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capProfSaudeCliente_bkp` (
  `psCPF` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `dtinclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capProfSaudeEspecialidade` (
  `psCPF` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`psCPF`,`esEspecialidade`),
  KEY `fk_Branco_brEspecialidade1_idx` (`esEspecialidade`),
  KEY `fk_brProfSaudeEspecialidade_brProfissionalSaude1_idx` (`psCPF`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capProtocolo` (
  `nrProtocolo` int NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`nrProtocolo`)
) ENGINE=InnoDB AUTO_INCREMENT=551 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capSolicitacaoDPO` (
  `dsNome` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `dsEmail` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `dsTelefone` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `dsMensagem` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `dtCriacao` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `dsProtocolo` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `dsAssunto` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  PRIMARY KEY (`dtCriacao`,`dsNome`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capTipoAssinaturaEletronica` (
  `aeAssEletronicaTipo` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'ecpf - ecnpj - elet',
  `aeAssEletronicaDescricao` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT 'e-CPF e-CNPJ Eletronica',
  PRIMARY KEY (`aeAssEletronicaTipo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capTipoPIX` (
  `pxPIXTipo` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'email cpf cnpj cel aleat',
  `pxPIXDescricao` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT 'eMail CPF CNPJ Celular Aleatoria',
  PRIMARY KEY (`pxPIXTipo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capUsuario` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `usCPFUsuario` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `usNome` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `usSenha` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usNomeMae` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usDatNasc` date DEFAULT NULL,
  `usTelefone` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `usEmail` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usCEP` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usEndereco` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usNrEnd` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usComplEnd` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usBairro` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usCidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usUF` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usAtivo` int DEFAULT NULL,
  `usCargo` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usDepartamento` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usRegFuncional` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usNacionalidade` varchar(100) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usGenero` varchar(50) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usEstadoCivil` varchar(50) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `usSenhaNova` int DEFAULT NULL,
  `usAceiteLGPD` int DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `isMaster` tinyint(1) DEFAULT '0',
  `psOnboardingPendente` int DEFAULT '0' COMMENT 'se verdadeiro, nao deve mostrar nada na tela, somente boas vindas',
  PRIMARY KEY (`usCPFUsuario`),
  UNIQUE KEY `uk_capUsuario_id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=173 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `gerenciamento_termos_lgpd` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `versao` varchar(20) COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'Identificador da versão, ex: v1.0, v2.1',
  `titulo` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'Título dos termos/política',
  `conteudo` longtext COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'Texto legal completo do termo/política LGPD',
  `hash_conteudo` varchar(64) COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'Hash SHA-256 do conteúdo para verificação de integridade',
  `ativo` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Se esta versão está atualmente ativa',
  `criado_por` bigint unsigned DEFAULT NULL COMMENT 'ID do usuário que criou esta versão',
  `criado_em` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `atualizado_em` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `valido_de` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data quando este termo se torna válido',
  `valido_ate` datetime DEFAULT NULL COMMENT 'Data de expiração opcional se aplicável',
  `tipo_documento` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT 'termos_lgpd' COMMENT 'Tipo do documento: termos_uso, politica_privacidade, consentimento_lgpd, politica_cookies, termos_lgpd',
  PRIMARY KEY (`id`),
  UNIQUE KEY `versao` (`versao`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `aceite_termos_lgpd` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `usuario_id` bigint unsigned DEFAULT NULL COMMENT 'Identificador do usuário campo id da tabela capUsuario',
  `termo_versao_id` bigint unsigned NOT NULL COMMENT 'Referência para gerenciamento_termos_lgpd.id',
  `consentimento_lgpd` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Se o consentimento LGPD foi dado (lgpdConsent)',
  `aceito_em` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data e hora do aceite (acceptedAt)',
  `endereco_ip` varchar(45) COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'Endereço IPv4 ou IPv6 do usuário (ipAddress)',
  `latitude` decimal(10,8) DEFAULT NULL COMMENT 'Latitude opcional para rastreamento de localização',
  `longitude` decimal(11,8) DEFAULT NULL COMMENT 'Longitude opcional para rastreamento de localização',
  `user_agent` text COLLATE utf8mb3_unicode_ci COMMENT 'String User-Agent do navegador no momento do aceite',
  `dados_adicionais` json DEFAULT NULL COMMENT 'Dados adicionais em formato JSON (additionalData)',
  `termo_versao` varchar(20) COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'Versão dos termos aceitos (termsVersion)',
  PRIMARY KEY (`id`),
  KEY `termo_versao_id` (`termo_versao_id`),
  KEY `usuario_id` (`usuario_id`),
  CONSTRAINT `aceite_termos_lgpd_ibfk_1` FOREIGN KEY (`termo_versao_id`) REFERENCES `gerenciamento_termos_lgpd` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `aceite_termos_lgpd_ibfk_2` FOREIGN KEY (`usuario_id`) REFERENCES `capUsuario` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `brcGrupoMenuPerfilMenu` (
  `gmNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `pmNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  PRIMARY KEY (`gmNome`,`pmNome`),
  KEY `fk_capGrupoMenuPerfilMenu_capPerfilMenu1_idx` (`pmNome`),
  CONSTRAINT `fk_capGrupoMenuPerfilMenu_capGrupoMenu1` FOREIGN KEY (`gmNome`) REFERENCES `brcGrupoMenu` (`gmNome`),
  CONSTRAINT `fk_capGrupoMenuPerfilMenu_capPerfilMenu1` FOREIGN KEY (`pmNome`) REFERENCES `brcPerfilMenu` (`pmNome`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `brcPerfilMenuRotas` (
  `gmNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `mrNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `pmNome` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  PRIMARY KEY (`gmNome`,`mrNome`,`pmNome`),
  KEY `fk_capPerfilMenuRotas_capMenuRotas1_idx` (`mrNome`),
  KEY `fk_capPerfilMenuRotas_capGrupoMenuPerfilMenu1_idx` (`gmNome`,`pmNome`),
  CONSTRAINT `fk_capPerfilMenuRotas_capGrupoMenuPerfilMenu1` FOREIGN KEY (`gmNome`, `pmNome`) REFERENCES `brcGrupoMenuPerfilMenu` (`gmNome`, `pmNome`),
  CONSTRAINT `fk_capPerfilMenuRotas_capMenuRotas1` FOREIGN KEY (`mrNome`) REFERENCES `brcMenuRotas` (`mrNome`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `brcUsuarioGrupoMenu` (
  `sgCliente` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `usuarioCPF` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `sgInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `gmNome` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `usuarioPermissao` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT 'pode atribuir permissao S N',
  PRIMARY KEY (`sgCliente`,`usuarioCPF`,`sgInstSaude`,`gmNome`),
  KEY `fk_capUsuarioPerfilMenu_capGrupoMenu1_idx` (`gmNome`),
  KEY `index_cpf` (`usuarioCPF`),
  CONSTRAINT `fk_capUsuarioPerfilMenu_capGrupoMenu1` FOREIGN KEY (`gmNome`) REFERENCES `brcGrupoMenu` (`gmNome`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `brcUsuarioPerfilAcesso` (
  `usCPFUsuario` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `tpTipoUsuario` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `paPerfilAcesso` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  PRIMARY KEY (`usCPFUsuario`,`tpTipoUsuario`,`paPerfilAcesso`),
  KEY `fk_brUsuarioPerfilAcesso_brPerfilAcesso1_idx` (`paPerfilAcesso`),
  KEY `fk_brUsuarioPerfilAcesso_brTipoUsuario1_idx` (`tpTipoUsuario`),
  CONSTRAINT `fk_brUsuarioPerfilAcesso_brUsuario1` FOREIGN KEY (`usCPFUsuario`) REFERENCES `capUsuario` (`usCPFUsuario`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capAditivoProfSaudeAntecipacao` (
  `anNrAntecipacao` int NOT NULL,
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ceTipoPagamento` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `opNrPlantao` int NOT NULL,
  `psCPF` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `adDataSolicitacao` timestamp NOT NULL,
  PRIMARY KEY (`anNrAntecipacao`,`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`adDataSolicitacao`),
  KEY `fk_capAditivoProfSaudeAntecipacao_capAntecipacao1_idx` (`anNrAntecipacao`),
  KEY `fk_capAditivoProfSaudeAntecipacao_capAditivoProfSaude1_idx` (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`adDataSolicitacao`),
  CONSTRAINT `fk_capAditivoProfSaudeAntecipacao_capAntecipacao1` FOREIGN KEY (`anNrAntecipacao`) REFERENCES `capAntecipacao` (`anNrAntecipacao`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capAntecipacaoHistorico` (
  `id` int NOT NULL AUTO_INCREMENT,
  `anNrAntecipacao` int NOT NULL,
  `anKuaraStatus` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `dtInclusao` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `anNrAntecipacao` (`anNrAntecipacao`),
  CONSTRAINT `capAntecipacaoHistorico_ibfk_1` FOREIGN KEY (`anNrAntecipacao`) REFERENCES `capAntecipacao` (`anNrAntecipacao`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capAtendimentoFechamento` (
  `isInstSaude` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `ceTipoPagamento` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `opNrPlantao` int NOT NULL,
  `psCPF` varchar(11) COLLATE utf8mb3_unicode_ci NOT NULL,
  `adDataSolicitacao` timestamp NOT NULL,
  `afNome` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `afIni` timestamp NOT NULL,
  `afFim` timestamp NOT NULL,
  `codFechamento` int DEFAULT NULL,
  `afValor` float DEFAULT NULL,
  `anNrAntecipacao` int DEFAULT NULL,
  `adSituacao` varchar(45) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `afUsuarioAprovacao` varchar(11) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `afDataAprovacao` timestamp NULL DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`adDataSolicitacao`,`afNome`,`afIni`),
  KEY `fk_capAtendimentoFechamento_capAditivoProfSaude1_idx` (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`psCPF`,`adDataSolicitacao`),
  CONSTRAINT `fk_capAtendimentoFechamento_capAditivoProfSaude1` FOREIGN KEY (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`, `psCPF`, `adDataSolicitacao`) REFERENCES `capAditivoProfSaude` (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`, `psCPF`, `adDataSolicitacao`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capCliMensagem` (
  `clCliente` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `meCodigo` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `cmIncidencia` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`clCliente`,`meCodigo`,`cmIncidencia`),
  KEY `fk_capCliMensagem_capMensageria1_idx` (`meCodigo`),
  CONSTRAINT `fk_capCliMensagem_capMensageria1` FOREIGN KEY (`meCodigo`) REFERENCES `capMensageria` (`meCodigo`),
  CONSTRAINT `fk_datas_capCliente1` FOREIGN KEY (`clCliente`) REFERENCES `capCliente` (`clCliente`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capInstFinancCliente` (
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ifInstFinanceira` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`clCliente`,`ifInstFinanceira`),
  KEY `fk_capInstFinancCliente_broCliente1_idx` (`clCliente`),
  KEY `fk_capInstFinancCliente_broInstFinanceira1` (`ifInstFinanceira`),
  CONSTRAINT `fk_capInstFinancCliente_broInstFinanceira1` FOREIGN KEY (`ifInstFinanceira`) REFERENCES `capInstFinanceira` (`ifInstFinanceira`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capInstSaudeContrato` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocDataContrato` timestamp NOT NULL,
  `acDataFimContrato` timestamp NULL DEFAULT NULL,
  `ocValorFixo` float DEFAULT NULL,
  `ocValorVariavel` float DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `ocValorUnitario` float DEFAULT NULL,
  PRIMARY KEY (`isInstSaude`,`ocNrContrato`,`clCliente`),
  KEY `fk_brOperadorContrato-_broCliente1_idx` (`clCliente`),
  KEY `fk_broInstSaudeContrato_broInstSaude1_idx` (`isInstSaude`),
  CONSTRAINT `fk_brOperadorContrato-_broCliente1` FOREIGN KEY (`clCliente`) REFERENCES `capCliente` (`clCliente`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capInstSaudeEspecialidade` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`),
  KEY `fk_broInstSaudeEspecialidade_broInstSaudeLocalAtendimento1_idx` (`isInstSaude`,`laNome`),
  KEY `fk_broInstSaudeEspecialidade_broEspecialidade1_idx` (`esEspecialidade`),
  CONSTRAINT `fk_broInstSaudeEspecialidade_broInstSaudeLocalAtendimento1` FOREIGN KEY (`isInstSaude`, `laNome`) REFERENCES `capInstSaudeLocalAtendimento` (`isInstSaude`, `laNome`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capInstSaudePlantaoAgenda` (
  `isInstSaude` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ceTipoPagamento` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `opNrPlantao` int NOT NULL,
  `agData` date NOT NULL,
  `agDiaSem` int DEFAULT NULL,
  `agHoraIni` time DEFAULT NULL,
  `agHoraFim` time DEFAULT NULL,
  `agIntervalo` time DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `agAtivo` int DEFAULT '1',
  `agDataIni` datetime NOT NULL,
  `agDataFim` datetime NOT NULL,
  `agTipoValor` int DEFAULT '0' COMMENT '0 = Normal, 1 = extra1 (capInstSaudePlantao.opValorExtra1), 2 = extra2 (capInstSaudePlantao.opValorExtra2)',
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`agData`),
  CONSTRAINT `fk_capInstSaudePlantaoAgenda_capInstSaudePlantao1` FOREIGN KEY (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`) REFERENCES `capInstSaudePlantao` (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capMensPlantaoAgenda` (
  `isInstSaude` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `ceTipoPagamento` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `opNrPlantao` int NOT NULL,
  `agData` date NOT NULL,
  `agHoraIni` time NOT NULL,
  `agHoraFim` time NOT NULL,
  `meCodigo` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `cmIncidencia` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`agData`,`agHoraIni`,`agHoraFim`,`meCodigo`,`cmIncidencia`),
  CONSTRAINT `fk_capMensPlantaoAgenda_capInstSaudePlantaoAgenda1` FOREIGN KEY (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`, `agData`) REFERENCES `capInstSaudePlantaoAgenda` (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`, `agData`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capOperPlantaoCheck` (
  `isInstSaude` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `laNome` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `esEspecialidade` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `ocNrContrato` varchar(50) COLLATE utf8mb3_unicode_ci NOT NULL,
  `clCliente` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `ceTipoPagamento` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `opNrPlantao` int NOT NULL,
  `agData` date NOT NULL,
  `ocCheckIn` timestamp NOT NULL,
  `ocIntervalo` time DEFAULT NULL,
  `ocCheckOut` timestamp NULL DEFAULT NULL,
  `ocQtRealizadas` time DEFAULT NULL,
  `ocQtAprovadas` time DEFAULT NULL,
  `ocQtGlosadas` time DEFAULT NULL,
  `pcJustificativa` varchar(100) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `codFechamento` int DEFAULT NULL,
  `ocCheckAprovado` int NOT NULL DEFAULT '0',
  `ocUsuarioAprovacao` varchar(11) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `ocSituacao` varchar(40) COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT 'EnviadoAprovacao | Aprovado | Fechado',
  `ocLatCheckin` decimal(10,8) DEFAULT NULL,
  `ocLongCheckin` decimal(11,8) DEFAULT NULL,
  `ocLatCheckout` decimal(10,8) DEFAULT NULL,
  `ocLongCheckout` decimal(11,8) DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`agData`,`ocCheckIn`),
  KEY `fk_broOperPlantaoCheck_capOperPlantaoCheckJustifica1_idx` (`pcJustificativa`),
  KEY `fk_capOperPlantaoCheck_capInstSaudePlantaoAgenda1_idx` (`isInstSaude`,`laNome`,`esEspecialidade`,`ocNrContrato`,`clCliente`,`ceTipoPagamento`,`opNrPlantao`,`agData`),
  CONSTRAINT `fk_broOperPlantaoCheck_capOperPlantaoCheckJustifica1` FOREIGN KEY (`pcJustificativa`) REFERENCES `capOperPlantaoCheckJustifica` (`pcJustificativa`),
  CONSTRAINT `fk_capOperPlantaoCheck_capInstSaudePlantaoAgenda1` FOREIGN KEY (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`, `agData`) REFERENCES `capInstSaudePlantaoAgenda` (`isInstSaude`, `laNome`, `esEspecialidade`, `ocNrContrato`, `clCliente`, `ceTipoPagamento`, `opNrPlantao`, `agData`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;




CREATE TABLE `capProfissionalSaude` (
  `psCPF` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT 'psCPF',
  `psRG` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `psContatoSecundario` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `psCNES` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `psCNS` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `ccConselhoClasse` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `psCCNrReg` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `psCCCidadeReg` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `psCCUFReg` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `psCCOrgEmissor` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `bcBancoNR` varchar(4) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `psAgenciaBanco` varchar(100) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `psContaCorrente` varchar(20) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `psAgenciaDigito` varchar(2) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `psContaCorrenteDigito` varchar(5) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `psContaCorrenteCNPJ` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `pxPIXTipo` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `psPIXChave` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `aeAssEletronicaTipo` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `psAssinaturaEletronica` varchar(400) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `psAtivo` int DEFAULT NULL,
  `dtInclusao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dtModificacao` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`psCPF`),
  KEY `fk_brProfissionalSaude_brConselhoClasse1_idx` (`ccConselhoClasse`),
  KEY `fk_brProfissionalSaude_brTipoPIX1_idx` (`pxPIXTipo`),
  KEY `fk_brProfissionalSaude_brTipoAssinaturaEletronica1_idx` (`aeAssEletronicaTipo`),
  KEY `fk_brProfissionalSaude_brBancos1_idx` (`bcBancoNR`),
  CONSTRAINT `fk_brProfissionalSaude_brBancos1` FOREIGN KEY (`bcBancoNR`) REFERENCES `capBanco` (`bcBancoNR`),
  CONSTRAINT `fk_brProfissionalSaude_brTipoPIX1` FOREIGN KEY (`pxPIXTipo`) REFERENCES `capTipoPIX` (`pxPIXTipo`),
  CONSTRAINT `fk_brProfissionalSaude_brUsuario1` FOREIGN KEY (`psCPF`) REFERENCES `capUsuario` (`usCPFUsuario`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;