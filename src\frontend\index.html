<!doctype html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Sistema GS2 - Gestão de Saúde e Plantões" />
    <meta name="theme-color" content="#3b82f6" />
    
    <!-- Preconnect para fontes -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Fonte Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Fonte JetBrains Mono para código -->
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet" />
    
    <title>Sistema GS2 - Gestão de Saúde</title>
    
    <!-- Prevent FOUC (Flash of Unstyled Content) -->
    <script>
      // Aplicar tema antes do carregamento para evitar flash
      (function() {
        const theme = localStorage.getItem('gs2-theme-storage');
        if (theme) {
          const parsed = JSON.parse(theme);
          const currentTheme = parsed.state?.theme || 'system';
          
          if (currentTheme === 'dark' || 
              (currentTheme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
          }
        }
      })();
    </script>
  </head>
  <body class="antialiased">
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
