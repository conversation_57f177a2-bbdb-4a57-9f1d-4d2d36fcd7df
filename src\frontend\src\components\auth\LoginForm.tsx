// =====================================================
// LOGIN FORM - FORMULÁRIO DE LOGIN
// Componente de formulário de autenticação
// =====================================================

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'
import { Eye, EyeOff, User, Lock, LogIn } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/Card'
import { useAuth } from '@/hooks/useAuth'
import { formatCPF, isValidCPF } from '@/utils'
import type { LoginCredentials } from '@/types'

// Schema de validação
const loginSchema = z.object({
  cpf: z
    .string()
    .min(1, 'CPF é obrigatório')
    .refine((cpf) => isValidCPF(cpf), 'CPF inválido'),
  password: z
    .string()
    .min(1, 'Senha é obrigatória')
    .min(6, 'Senha deve ter pelo menos 6 caracteres'),
  rememberMe: z.boolean().optional()
})

type LoginFormData = z.infer<typeof loginSchema>

interface LoginFormProps {
  onForgotPassword?: () => void
}

export function LoginForm({ onForgotPassword }: LoginFormProps) {
  const { t } = useTranslation()
  const { login, isLoggingIn, error, clearError } = useAuth()
  const [showPassword, setShowPassword] = React.useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    setValue
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: 'onChange',
    defaultValues: {
      cpf: '',
      password: '',
      rememberMe: false
    }
  })

  const cpfValue = watch('cpf')

  // Formatar CPF enquanto digita
  React.useEffect(() => {
    if (cpfValue) {
      const formatted = formatCPF(cpfValue)
      if (formatted !== cpfValue) {
        setValue('cpf', formatted, { shouldValidate: true })
      }
    }
  }, [cpfValue, setValue])

  // Limpar erro quando começar a digitar
  React.useEffect(() => {
    if (error) {
      clearError()
    }
  }, [cpfValue, watch('password'), error, clearError])

  const onSubmit = (data: LoginFormData) => {
    const credentials: LoginCredentials = {
      cpf: data.cpf.replace(/\D/g, ''), // Remove formatação
      password: data.password
    }
    
    login(credentials)
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-md mx-auto"
    >
      <Card className="shadow-strong">
        <CardHeader className="text-center pb-8">
          {/* Logo */}
          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.3 }}
            className="w-16 h-16 bg-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"
          >
            <span className="text-white font-bold text-2xl">GS</span>
          </motion.div>

          <CardTitle className="text-2xl font-bold text-heading">
            {t('auth.loginTitle')}
          </CardTitle>
          <CardDescription className="text-muted">
            {t('auth.loginSubtitle')}
          </CardDescription>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Campo CPF */}
            <div>
              <Input
                {...register('cpf')}
                type="text"
                label={t('auth.cpf')}
                placeholder="000.000.000-00"
                leftIcon={<User className="w-5 h-5" />}
                error={errors.cpf?.message}
                maxLength={14}
                autoComplete="username"
                autoFocus
              />
            </div>

            {/* Campo Senha */}
            <div>
              <Input
                {...register('password')}
                type={showPassword ? 'text' : 'password'}
                label={t('auth.password')}
                placeholder="••••••••"
                leftIcon={<Lock className="w-5 h-5" />}
                rightIcon={
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="text-secondary-400 hover:text-secondary-600 transition-colors"
                  >
                    {showPassword ? (
                      <EyeOff className="w-5 h-5" />
                    ) : (
                      <Eye className="w-5 h-5" />
                    )}
                  </button>
                }
                error={errors.password?.message}
                autoComplete="current-password"
              />
            </div>

            {/* Lembrar-me e Esqueci a senha */}
            <div className="flex items-center justify-between">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  {...register('rememberMe')}
                  type="checkbox"
                  className="w-4 h-4 text-primary-600 border-secondary-300 rounded focus:ring-primary-500 focus:ring-2"
                />
                <span className="text-sm text-body">
                  {t('auth.rememberMe')}
                </span>
              </label>

              {onForgotPassword && (
                <button
                  type="button"
                  onClick={onForgotPassword}
                  className="text-sm text-primary-600 hover:text-primary-700 font-medium transition-colors"
                >
                  {t('auth.forgotPassword')}
                </button>
              )}
            </div>

            {/* Erro de login */}
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-3 bg-error-50 border border-error-200 rounded-lg"
              >
                <p className="text-sm text-error-600">{error}</p>
              </motion.div>
            )}

            {/* Botão de login */}
            <Button
              type="submit"
              variant="primary"
              size="lg"
              fullWidth
              loading={isLoggingIn}
              disabled={!isValid || isLoggingIn}
              icon={<LogIn className="w-5 h-5" />}
            >
              {t('auth.loginButton')}
            </Button>
          </form>

          {/* Informações adicionais */}
          <div className="mt-8 pt-6 border-t border-secondary-200 dark:border-secondary-700">
            <div className="text-center">
              <p className="text-xs text-muted">
                Sistema GS2 - Gestão de Saúde
              </p>
              <p className="text-xs text-muted mt-1">
                Versão 1.0.0
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
