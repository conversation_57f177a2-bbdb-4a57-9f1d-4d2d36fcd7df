// =====================================================
// CARD COMPONENT - COMPONENTE DE CARTÃO
// Componente de cartão reutilizável e customizável
// =====================================================

import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/utils'

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'outlined' | 'elevated' | 'filled'
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  hover?: boolean
  clickable?: boolean
  children: React.ReactNode
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({
    className,
    variant = 'default',
    padding = 'md',
    rounded = 'lg',
    shadow = 'md',
    hover = false,
    clickable = false,
    children,
    ...props
  }, ref) => {
    const baseClasses = [
      'transition-all duration-200',
      clickable && 'cursor-pointer select-none'
    ]

    const variantClasses = {
      default: [
        'bg-white border border-secondary-200',
        'dark:bg-secondary-900 dark:border-secondary-700'
      ],
      outlined: [
        'bg-transparent border-2 border-secondary-300',
        'dark:border-secondary-600'
      ],
      elevated: [
        'bg-white border-0',
        'dark:bg-secondary-900'
      ],
      filled: [
        'bg-secondary-50 border border-secondary-200',
        'dark:bg-secondary-800 dark:border-secondary-700'
      ]
    }

    const paddingClasses = {
      none: '',
      sm: 'p-3',
      md: 'p-4',
      lg: 'p-6',
      xl: 'p-8'
    }

    const roundedClasses = {
      none: '',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      xl: 'rounded-xl',
      full: 'rounded-full'
    }

    const shadowClasses = {
      none: '',
      sm: 'shadow-sm',
      md: 'shadow-soft',
      lg: 'shadow-medium',
      xl: 'shadow-strong'
    }

    const hoverClasses = hover ? [
      'hover:shadow-medium hover:-translate-y-0.5',
      'dark:hover:shadow-xl'
    ] : []

    const classes = cn(
      baseClasses,
      variantClasses[variant],
      paddingClasses[padding],
      roundedClasses[rounded],
      shadowClasses[shadow],
      hoverClasses,
      className
    )

    const MotionCard = clickable ? motion.div : 'div'

    const motionProps = clickable ? {
      whileHover: { scale: 1.02, y: -2 },
      whileTap: { scale: 0.98 },
      transition: { duration: 0.1 }
    } : {}

    return (
      <MotionCard
        ref={ref}
        className={classes}
        {...(clickable ? motionProps : {})}
        {...props}
      >
        {children}
      </MotionCard>
    )
  }
)

Card.displayName = 'Card'

// Sub-componentes do Card
interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('flex flex-col space-y-1.5 pb-4', className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)

CardHeader.displayName = 'CardHeader'

interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
  children: React.ReactNode
}

const CardTitle = React.forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ className, as: Component = 'h3', children, ...props }, ref) => {
    return (
      <Component
        ref={ref}
        className={cn(
          'text-lg font-semibold leading-none tracking-tight',
          'text-secondary-900 dark:text-secondary-100',
          className
        )}
        {...props}
      >
        {children}
      </Component>
    )
  }
)

CardTitle.displayName = 'CardTitle'

interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode
}

const CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <p
        ref={ref}
        className={cn(
          'text-sm text-secondary-600 dark:text-secondary-400',
          className
        )}
        {...props}
      >
        {children}
      </p>
    )
  }
)

CardDescription.displayName = 'CardDescription'

interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('pt-0', className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)

CardContent.displayName = 'CardContent'

interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

const CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('flex items-center pt-4', className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)

CardFooter.displayName = 'CardFooter'

export {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter
}

export type {
  CardProps,
  CardHeaderProps,
  CardTitleProps,
  CardDescriptionProps,
  CardContentProps,
  CardFooterProps
}
