// =====================================================
// MODERN API SERVICE - SERVIÇO DE API MODERNA
// Cliente HTTP para comunicação com API moderna
// =====================================================

import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { useAuthStore } from '@/stores/authStore'
import toast from 'react-hot-toast'

// Configuração base da API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'

/**
 * Interface para resposta da API moderna
 */
interface ModernApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: any
  timestamp: string
}

/**
 * Interface para resposta paginada
 */
interface PaginatedResponse<T = any> {
  items: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

/**
 * Classe do serviço de API moderna
 */
class ModernApiService {
  private client: AxiosInstance

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  /**
   * Configurar interceptors
   */
  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Adicionar token de autorização
        const token = useAuthStore.getState().token
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // Log da requisição em desenvolvimento
        if (import.meta.env.DEV) {
          console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, {
            data: config.data,
            params: config.params,
          })
        }

        return config
      },
      (error) => {
        console.error('❌ Request error:', error)
        return Promise.reject(error)
      }
    )

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse<ModernApiResponse>) => {
        // Log da resposta em desenvolvimento
        if (import.meta.env.DEV) {
          console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url}`, {
            status: response.status,
            data: response.data,
          })
        }

        return response
      },
      async (error) => {
        const originalRequest = error.config

        // Se erro 401 e não é uma tentativa de refresh
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true

          try {
            // Tentar renovar token
            const response = await this.client.post('/api/auth/refresh')
            
            if (response.data?.data?.token) {
              const { token } = response.data.data
              useAuthStore.getState().setToken(token)
              
              // Repetir requisição original com novo token
              originalRequest.headers.Authorization = `Bearer ${token}`
              return this.client(originalRequest)
            }
          } catch (refreshError) {
            // Se refresh falhar, fazer logout
            useAuthStore.getState().logout()
            window.location.href = '/login'
          }
        }

        // Mostrar toast de erro se não for erro de autenticação
        if (error.response?.status !== 401) {
          const message = error.response?.data?.message || error.message || 'Erro na requisição'
          toast.error(message)
        }

        return Promise.reject(error)
      }
    )
  }

  /**
   * Métodos HTTP genéricos
   */
  async get<T = any>(url: string, params?: any): Promise<T> {
    const response = await this.client.get<ModernApiResponse<T>>(url, { params })
    return response.data.data as T
  }

  async post<T = any>(url: string, data?: any): Promise<T> {
    const response = await this.client.post<ModernApiResponse<T>>(url, data)
    return response.data.data as T
  }

  async put<T = any>(url: string, data?: any): Promise<T> {
    const response = await this.client.put<ModernApiResponse<T>>(url, data)
    return response.data.data as T
  }

  async delete<T = any>(url: string): Promise<T> {
    const response = await this.client.delete<ModernApiResponse<T>>(url)
    return response.data.data as T
  }

  /**
   * Métodos específicos da API
   */

  // === AUTENTICAÇÃO ===
  async login(credentials: { cpf: string; password: string }) {
    // Fazer chamada direta para o endpoint do lambda
    const response = await this.client.post('/login', {
      username: credentials.cpf,
      password: credentials.password
    })
    return response.data
  }

  async logout() {
    return this.post('/api/auth/logout')
  }

  async refreshToken() {
    return this.post('/api/auth/refresh')
  }

  async verifyToken() {
    return this.get('/api/auth/verify')
  }

  async getProfile() {
    return this.get('/api/auth/profile')
  }

  async updateProfile(data: any) {
    return this.put('/api/auth/profile', data)
  }

  async changePassword(data: { currentPassword: string; newPassword: string }) {
    return this.post('/api/auth/change-password', data)
  }

  async forgotPassword(email: string) {
    return this.post('/api/auth/forgot-password', { email })
  }

  async resetPassword(data: { token: string; newPassword: string }) {
    return this.post('/api/auth/reset-password', data)
  }

  // === USUÁRIOS ===
  async getUsers(params?: {
    page?: number
    limit?: number
    search?: string
    active?: boolean
    group?: string
  }): Promise<PaginatedResponse> {
    return this.get('/api/users', params)
  }

  async getUser(id: string) {
    return this.get(`/api/users/${id}`)
  }

  async createUser(data: any) {
    return this.post('/api/users', data)
  }

  async updateUser(id: string, data: any) {
    return this.put(`/api/users/${id}`, data)
  }

  async deleteUser(id: string) {
    return this.delete(`/api/users/${id}`)
  }

  // === DASHBOARD ===
  async getDashboardStats() {
    return this.get('/api/dashboard/stats')
  }

  async getRecentActivity(limit?: number) {
    return this.get('/api/dashboard/activity', { limit })
  }

  // === SISTEMA ===
  async healthCheck() {
    const response = await this.client.get('/health')
    return response.data
  }

  async getApiInfo() {
    const response = await this.client.get('/api')
    return response.data
  }

  /**
   * Método legacy para compatibilidade
   */
  async lambda<T = any>(endpoint: string, data?: any): Promise<{ data: T }> {
    // Mapear endpoints legacy para modernos
    const endpointMap: Record<string, string> = {
      'brwUsuario': '/api/auth/login',
      'brwDashboard': '/api/dashboard/stats',
    }

    const modernEndpoint = endpointMap[endpoint] || `/${endpoint}`
    
    try {
      const result = await this.post<T>(modernEndpoint, data)
      return { data: result }
    } catch (error) {
      console.warn(`Legacy endpoint ${endpoint} not found, using fallback`)
      throw error
    }
  }
}

// Instância singleton
export const modernApi = new ModernApiService()

// Export para compatibilidade
export { ModernApiService }
export type { ModernApiResponse, PaginatedResponse }
