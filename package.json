{"name": "api", "version": "1.0.0", "description": ".", "private": true, "type": "module", "scripts": {"dev": "nodemon --watch ./ --inspect ./src/app/server.mjs", "downloadEnv": "node ./config/downloadEnv.js", "updateFunction": "node ./config/updateFunction.mjs", "downloadFunction": "node ./config/downloadFunction.mjs", "downloadLayer": "node ./config/downloadLayer.mjs", "buildLayer": "node ./config/buildLayer.mjs", "updateEnv": "node ./config/updateEnv.js", "docker:up": "docker-compose -f capitale-docker/docker-compose.yml -p capitale-api up --build -d", "dev:docker": "yarn docker:up && yarn dev", "prisma:generate": "npx prisma db pull && npx prisma generate", "test": "cross-env NODE_OPTIONS=--experimental-vm-modules npx jest --passWithNoTests --config jest.config.mjs", "last-updated": "git log --since=\"15 days ago\" --name-only --pretty=format: | grep -E \"src/.*\" | sort | uniq"}, "dependencies": {"@aws-sdk/client-lambda": "^3.693.0", "@aws-sdk/client-sqs": "^3.693.0", "@prisma/client": "^6.3.1", "axios": "^0.27.2", "bcryptjs": "^2.4.3", "celebrate": "^15.0.3", "cors": "^2.8.5", "dayjs": "^1.11.4", "dotenv": "^16.0.1", "express": "^4.21.1", "jsonwebtoken": "^8.5.1", "moment": "^2.29.3", "mysql": "^2.18.1", "uuid": "^8.3.2", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "adm-zip": "^0.5.9", "aws-sdk": "^2.1243.0", "babel-jest": "^29.7.0", "capfunctions": "file:./src/layers/capfunctions", "capcorpconf": "file:./src/layers/capcorpconf", "cross-env": "^7.0.3", "decompress": "^4.2.1", "download-file": "^0.1.5", "eslint": "^8.22.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.7.0", "nodemon": "^3.1.7", "prettier": "^2.7.1", "prisma": "^6.3.1"}}