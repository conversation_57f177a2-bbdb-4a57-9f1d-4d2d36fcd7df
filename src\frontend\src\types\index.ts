// =====================================================
// TYPES - DEFINIÇÕES DE TIPOS TYPESCRIPT
// Tipos principais do sistema GS2
// =====================================================

// Tipos de autenticação
export interface User {
  id: number
  cpf: string
  nome: string
  email: string
  aceiteLGPD: boolean
  resetPassword?: string
  psOnboardingPendente: boolean
  grupos: UserGroup[]
  rotas?: Route[]
  valorMinimoSolicitacao?: number
  valorMaximoSolicitacao?: number
  permissions?: Record<string, boolean>
}

export interface UserGroup {
  nome: string
  descricao: string
}

export interface Route {
  rota: string
  nomeTela: string
  icone?: string
}

export interface LoginCredentials {
  cpf: string
  password: string
}

export interface AuthResponse {
  user: User
  token: string
  expires_at: string
}

// Tipos de cliente
export interface Client {
  id: number
  cnpj: string
  company_name: string
  trade_name?: string
  contact_name?: string
  phone?: string
  email?: string
  active: boolean
  created_at: string
  updated_at: string
}

// Tipos de profissional de saúde
export interface HealthProfessional {
  id: number
  user_id: number
  user: User
  crm?: string
  specialty_id?: number
  specialty?: Specialty
  pix_key?: string
  bank_account?: BankAccount
  active: boolean
  created_at: string
  updated_at: string
}

export interface Specialty {
  id: number
  name: string
  description?: string
  active: boolean
}

export interface BankAccount {
  bank_code: string
  bank_name: string
  agency: string
  account: string
  account_type: 'checking' | 'savings'
  account_holder_name: string
  account_holder_cpf: string
}

// Tipos de plantão
export interface Shift {
  id: number
  shift_number: number
  contract_specialty_id: number
  health_professional_id?: number
  health_professional?: HealthProfessional
  approver_user_id?: number
  approver?: User
  publication_date: string
  period_start: string
  period_end: string
  required_hours?: number
  worked_hours?: number
  fixed_value?: number
  hourly_rate?: number
  unit_value?: number
  extra_value_1?: number
  extra_value_2?: number
  access_key?: string
  closing_type: 'manual' | 'automatic'
  closing_date?: string
  closing_day?: number
  receive_date?: string
  advance_date?: string
  status: ShiftStatus
  active: boolean
  created_at: string
  updated_at: string
}

export type ShiftStatus = 
  | 'aberto'
  | 'solicitado'
  | 'aprovado'
  | 'rejeitado'
  | 'executado'
  | 'aprovado_execucao'
  | 'fechado'

// Tipos de antecipação
export interface Advance {
  id: number
  advance_number: number
  health_professional_id: number
  health_professional?: HealthProfessional
  financial_institution_id: number
  financial_institution?: FinancialInstitution
  requested_amount: number
  approved_amount?: number
  net_amount?: number
  advance_rate?: number
  kuara_id?: string
  kuara_status?: string
  request_date: string
  approval_date?: string
  receive_date?: string
  approver_user_id?: number
  approver?: User
  approval_token?: string
  notes?: string
  status: AdvanceStatus
  created_at: string
  updated_at: string
}

export type AdvanceStatus = 
  | 'solicitado'
  | 'em_analise'
  | 'aprovado'
  | 'rejeitado'
  | 'processando'
  | 'concluido'
  | 'cancelado'

export interface FinancialInstitution {
  id: number
  cnpj: string
  name: string
  active: boolean
}

// Tipos de WhatsApp
export interface WhatsAppMessage {
  id: number
  phone_number: string
  message_date: string
  direction: 'enviada' | 'recebida'
  phone_from: string
  message_type: string
  payload: string
  message_text: string
  payload_sent?: any
  created_at: string
}

// Tipos de auditoria
export interface AuditLog {
  id: number
  uuid: string
  user_cpf?: string
  user_name?: string
  user_email?: string
  operation_type: string
  resource_type?: string
  resource_id?: string
  resource_identifier?: string
  action_description?: string
  old_values?: any
  new_values?: any
  ip_address?: string
  user_agent?: string
  success: boolean
  compliance_level: 'STANDARD' | 'HIGH' | 'CRITICAL'
  created_at: string
}

// Tipos de dashboard
export interface DashboardStats {
  users: {
    total: number
    active: number
    inactive: number
    new_last_30_days: number
  }
  clients: {
    total: number
    active: number
    inactive: number
    new_last_30_days: number
  }
  professionals: {
    total: number
    active: number
    inactive: number
    new_last_30_days: number
  }
  shifts: {
    total: number
    open: number
    approved: number
    executed: number
  }
  advances: {
    total: number
    pending: number
    approved: number
    total_amount: number
  }
}

// Tipos de API
export interface ApiResponse<T = any> {
  statusCode: number
  message: string
  success: boolean
  data?: T
  pagination?: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export interface ApiError {
  statusCode: number
  message: string
  success: false
  error?: string
}

// Tipos de formulário
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'date' | 'checkbox'
  placeholder?: string
  required?: boolean
  options?: { value: string | number; label: string }[]
  validation?: any
}

// Tipos de tema
export type Theme = 'light' | 'dark' | 'system'

// Tipos de idioma
export type Language = 'pt-BR' | 'en-US' | 'es-ES'

// Tipos de paginação
export interface PaginationParams {
  page?: number
  limit?: number
  orderBy?: string
  orderDirection?: 'asc' | 'desc'
}

// Tipos de filtros
export interface FilterParams {
  search?: string
  active?: boolean
  date_from?: string
  date_to?: string
  [key: string]: any
}

// Tipos de tabela
export interface TableColumn<T = any> {
  key: keyof T | string
  label: string
  sortable?: boolean
  render?: (value: any, row: T) => React.ReactNode
  width?: string
  align?: 'left' | 'center' | 'right'
}

export interface TableProps<T = any> {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  pagination?: {
    page: number
    limit: number
    total: number
    pages: number
    onPageChange: (page: number) => void
    onLimitChange: (limit: number) => void
  }
  onSort?: (column: string, direction: 'asc' | 'desc') => void
  onRowClick?: (row: T) => void
}

// Tipos de notificação
export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

// Tipos de modal
export interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  children: React.ReactNode
}

// Tipos de menu
export interface MenuItem {
  id: string
  label: string
  icon?: React.ComponentType<any>
  path?: string
  children?: MenuItem[]
  permission?: string
  badge?: string | number
}

export interface BreadcrumbItem {
  label: string
  path?: string
}
