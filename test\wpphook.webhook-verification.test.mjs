import { describe, test, expect, beforeEach, jest } from '@jest/globals';

// Mock capfunctions module
const mockBaseResponse = {
    ok: jest.fn((message, data) => ({ statusCode: 200, message, data })),
    error: jest.fn((message, data) => ({ statusCode: 500, message, data }))
};

jest.unstable_mockModule('capfunctions', () => ({
    baseResponse: mockBaseResponse
}));

const { handler } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

describe('WhatsApp Webhook Verification', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Webhook Verification Response Format', () => {
        test('should return challenge value for valid webhook verification', async () => {
            const event = {
                requestContext: {
                    http: {
                        method: 'GET',
                        path: '/wpphook'
                    }
                },
                queryStringParameters: {
                    'hub.mode': 'subscribe',
                    'hub.verify_token': 'CWMKRQ0i4sxMfqv6k9kEZCSk9DThKFiRQZBSqxaIDApTqtf',
                    'hub.challenge': '**********'
                }
            };

            const result = await handler(event);

            expect(result).toEqual({
                statusCode: 200,
                headers: {
                    'Content-Type': 'text/plain'
                },
                body: '**********'
            });

            // Should not call baseResponse functions for webhook verification
            expect(mockBaseResponse.ok).not.toHaveBeenCalled();
            expect(mockBaseResponse.error).not.toHaveBeenCalled();
        });

        test('should reject webhook verification with invalid token', async () => {
            const event = {
                requestContext: {
                    http: {
                        method: 'GET',
                        path: '/wpphook'
                    }
                },
                queryStringParameters: {
                    'hub.mode': 'subscribe',
                    'hub.verify_token': 'INVALID_TOKEN',
                    'hub.challenge': '**********'
                }
            };

            const result = await handler(event);

            // Should not return the challenge for invalid token
            expect(result.body).not.toBe('**********');
            expect(result.statusCode).toBe(200); // Still 200 to prevent retries
        });

        test('should reject webhook verification with invalid mode', async () => {
            const event = {
                requestContext: {
                    http: {
                        method: 'GET',
                        path: '/wpphook'
                    }
                },
                queryStringParameters: {
                    'hub.mode': 'invalid',
                    'hub.verify_token': 'CWMKRQ0i4sxMfqv6k9kEZCSk9DThKFiRQZBSqxaIDApTqtf',
                    'hub.challenge': '**********'
                }
            };

            const result = await handler(event);

            // Should not return the challenge for invalid mode
            expect(result.body).not.toBe('**********');
            expect(result.statusCode).toBe(200); // Still 200 to prevent retries
        });

        test('should handle missing query parameters', async () => {
            const event = {
                requestContext: {
                    http: {
                        method: 'GET',
                        path: '/wpphook'
                    }
                },
                queryStringParameters: null
            };

            const result = await handler(event);

            expect(result.statusCode).toBe(200); // Still 200 to prevent retries
            expect(result.body).not.toBe('**********');
        });
    });

    describe('Health Check Response Format', () => {
        test('should return health check response for /health path', async () => {
            const event = {
                requestContext: {
                    http: {
                        method: 'GET',
                        path: '/health'
                    }
                }
            };

            const result = await handler(event);

            expect(result.statusCode).toBe(200);
            expect(result.message).toBe('Kuará Capital Chatbot is healthy');
        });
    });

    describe('WhatsApp API Compliance', () => {
        test('webhook verification response should only contain challenge value', async () => {
            const challengeValue = '**********';
            const event = {
                requestContext: {
                    http: {
                        method: 'GET',
                        path: '/wpphook'
                    }
                },
                queryStringParameters: {
                    'hub.mode': 'subscribe',
                    'hub.verify_token': 'CWMKRQ0i4sxMfqv6k9kEZCSk9DThKFiRQZBSqxaIDApTqtf',
                    'hub.challenge': challengeValue
                }
            };

            const result = await handler(event);

            // Must be exactly the challenge value as body
            expect(result.body).toBe(challengeValue);
            
            // Must have correct content type
            expect(result.headers['Content-Type']).toBe('text/plain');
            
            // Must have 200 status
            expect(result.statusCode).toBe(200);
            
            // Should not include extra fields that WhatsApp doesn't expect
            expect(result).not.toHaveProperty('message');
            expect(result).not.toHaveProperty('data');
            expect(result).not.toHaveProperty('timestamp');
        });
    });
});