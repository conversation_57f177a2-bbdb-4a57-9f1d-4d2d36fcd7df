import { describe, test, expect, beforeEach, jest } from '@jest/globals';

// Mock capfunctions module
const mockBaseResponse = {
    ok: jest.fn((message, data) => ({ statusCode: 200, message, data })),
    error: jest.fn((message, data) => ({ statusCode: 500, message, data }))
};

jest.unstable_mockModule('capfunctions', () => ({
    baseResponse: mockBaseResponse
}));

const { parseWebhookPayload } = await import('../src/lambdas/gs2api/wpphook/index.mjs');

describe('WhatsApp Official Format Parsing', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('parseWebhookPayload', () => {
        test('should parse official WhatsApp Business API format correctly', () => {
            const realWhatsAppPayload = {
                object: "whatsapp_business_account",
                entry: [{
                    id: "***************",
                    changes: [{
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "*************",
                                phone_number_id: "***************"
                            },
                            contacts: [{
                                profile: {
                                    name: "Vinicius"
                                },
                                wa_id: "*************"
                            }],
                            messages: [{
                                from: "*************",
                                id: "wamid.HBgNNTUxODk4ODE1NjA1MRUCABIYFjNFQjAwODRDNzU3RjJEOTBGMzk2RDMA",
                                timestamp: "**********",
                                text: {
                                    body: "Ola"
                                },
                                type: "text"
                            }]
                        },
                        field: "messages"
                    }]
                }]
            };

            const event = {
                body: JSON.stringify(realWhatsAppPayload)
            };

            const result = parseWebhookPayload(event);

            expect(result).toEqual({
                userId: "*************",
                phoneNumber: "*************",
                message: "Ola",
                originalPayload: realWhatsAppPayload,
                messageId: "wamid.HBgNNTUxODk4ODE1NjA1MRUCABIYFjNFQjAwODRDNzU3RjJEOTBGMzk2RDMA",
                timestamp: "**********",
                profileName: "Vinicius"
            });
        });

        test('should parse WhatsApp interactive button messages', () => {
            const interactivePayload = {
                object: "whatsapp_business_account",
                entry: [{
                    id: "***************",
                    changes: [{
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "*************",
                                phone_number_id: "***************"
                            },
                            contacts: [{
                                profile: {
                                    name: "João"
                                },
                                wa_id: "*************"
                            }],
                            messages: [{
                                from: "*************",
                                id: "wamid.interactive123",
                                timestamp: "**********",
                                type: "interactive",
                                interactive: {
                                    type: "button_reply",
                                    button_reply: {
                                        id: "option_1",
                                        title: "✅ Sim"
                                    }
                                }
                            }]
                        },
                        field: "messages"
                    }]
                }]
            };

            const event = {
                body: JSON.stringify(interactivePayload)
            };

            const result = parseWebhookPayload(event);

            expect(result).toEqual({
                userId: "*************",
                phoneNumber: "*************",
                message: "✅ Sim",
                originalPayload: interactivePayload,
                messageId: "wamid.interactive123",
                timestamp: "**********",
                profileName: "João"
            });
        });

        test('should parse WhatsApp list reply messages', () => {
            const listPayload = {
                object: "whatsapp_business_account",
                entry: [{
                    id: "***************",
                    changes: [{
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "*************",
                                phone_number_id: "***************"
                            },
                            contacts: [{
                                profile: {
                                    name: "Maria"
                                },
                                wa_id: "*************"
                            }],
                            messages: [{
                                from: "*************",
                                id: "wamid.list123",
                                timestamp: "**********",
                                type: "interactive",
                                interactive: {
                                    type: "list_reply",
                                    list_reply: {
                                        id: "clinic_option",
                                        title: "Clínica ABC"
                                    }
                                }
                            }]
                        },
                        field: "messages"
                    }]
                }]
            };

            const event = {
                body: JSON.stringify(listPayload)
            };

            const result = parseWebhookPayload(event);

            expect(result).toEqual({
                userId: "*************",
                phoneNumber: "*************",
                message: "Clínica ABC",
                originalPayload: listPayload,
                messageId: "wamid.list123",
                timestamp: "**********",
                profileName: "Maria"
            });
        });

        test('should handle missing profile name gracefully', () => {
            const payloadWithoutProfile = {
                object: "whatsapp_business_account",
                entry: [{
                    id: "***************",
                    changes: [{
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "*************",
                                phone_number_id: "***************"
                            },
                            contacts: [{
                                wa_id: "*************"
                            }],
                            messages: [{
                                from: "*************",
                                id: "wamid.test123",
                                timestamp: "**********",
                                text: {
                                    body: "Hello"
                                },
                                type: "text"
                            }]
                        },
                        field: "messages"
                    }]
                }]
            };

            const event = {
                body: JSON.stringify(payloadWithoutProfile)
            };

            const result = parseWebhookPayload(event);

            expect(result.profileName).toBe('');
            expect(result.message).toBe('Hello');
        });

        test('should fallback to legacy format for non-WhatsApp payloads', () => {
            const legacyPayload = {
                message: "Test message",
                from: "*************"
            };

            const event = {
                body: legacyPayload
            };

            const result = parseWebhookPayload(event);

            expect(result).toEqual({
                userId: "*************",
                phoneNumber: "*************",
                message: "Test message",
                originalPayload: legacyPayload
            });
        });

        test('should handle event body as string', () => {
            const whatsappPayload = {
                object: "whatsapp_business_account",
                entry: [{
                    id: "***************",
                    changes: [{
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "*************",
                                phone_number_id: "***************"
                            },
                            contacts: [{
                                profile: { name: "Test User" },
                                wa_id: "*************"
                            }],
                            messages: [{
                                from: "*************",
                                id: "wamid.string123",
                                timestamp: "**********",
                                text: { body: "String test" },
                                type: "text"
                            }]
                        },
                        field: "messages"
                    }]
                }]
            };

            const event = {
                body: JSON.stringify(whatsappPayload) // String format
            };

            const result = parseWebhookPayload(event);

            expect(result.message).toBe('String test');
            expect(result.profileName).toBe('Test User');
        });

        test('should throw error for invalid WhatsApp payload', () => {
            const invalidPayload = {
                object: "whatsapp_business_account",
                entry: [{
                    id: "***************",
                    changes: [{
                        value: {
                            // Missing messages array
                        },
                        field: "messages"
                    }]
                }]
            };

            const event = {
                body: JSON.stringify(invalidPayload)
            };

            expect(() => parseWebhookPayload(event)).toThrow('Invalid webhook payload format');
        });
    });
});