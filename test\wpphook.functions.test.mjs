import { describe, test, expect, beforeEach, jest } from '@jest/globals';

// Mock capfunctions module
const mockBaseResponse = {
    ok: jest.fn((message, data) => ({ statusCode: 200, message, data })),
    error: jest.fn((message, data) => ({ statusCode: 500, message, data }))
};

jest.unstable_mockModule('capfunctions', () => ({
    baseResponse: mockBaseResponse
}));

// Mock the database and other dependencies
const mockDatabase = {
    initialize: jest.fn(),
    getUserSession: jest.fn(),
    createUserSession: jest.fn(),
    updateUserSession: jest.fn(),
    saveUserData: jest.fn(),
    logInteraction: jest.fn(),
    healthCheck: jest.fn(),
    executeQuery: jest.fn(),
    getInteractionHistory: jest.fn(),
    cleanupOldSessions: jest.fn(),
    deleteUserSession: jest.fn(),
    markTermsAccepted: jest.fn()
};

const mockMessageBuilder = {
    buildGreeting: jest.fn(() => 'Test greeting message'),
    buildErrorMessage: jest.fn(() => 'Test error message')
};

const mockStateMachine = {
    processMessage: jest.fn(),
    cleanupExpiredSessions: jest.fn(),
    getSessionStats: jest.fn(),
    getUserJourney: jest.fn(),
    resetSession: jest.fn()
};

jest.unstable_mockModule('../src/lambdas/gs2api/wpphook/database.mjs', () => ({
    default: jest.fn(() => mockDatabase)
}));

jest.unstable_mockModule('../src/lambdas/gs2api/wpphook/utils/messageBuilder.mjs', () => ({
    default: jest.fn(() => mockMessageBuilder)
}));

jest.unstable_mockModule('../src/lambdas/gs2api/wpphook/services/stateMachine.mjs', () => ({
    StateMachine: jest.fn(() => mockStateMachine)
}));

// Import the functions to test after setting up mocks
const {
    _processMessage,
    _getSession,
    _saveUserData,
    _healthCheck,
    _cleanupSessions,
    _getSessionStats,
    _getUserJourney,
    _resetUserSession
} = await import('../src/lambdas/gs2api/wpphook/functions.mjs');

describe('wpphook Functions', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('_processMessage', () => {
        test('should process message successfully', async () => {
            const mockParams = {
                userId: '123456789',
                phoneNumber: '+5511999999999',
                message: 'GS2',
                originalPayload: { test: 'data' }
            };

            const expectedResponse = {
                message: 'Olá, tudo bem?',
                nextState: 'GREETING'
            };

            mockStateMachine.processMessage.mockResolvedValue(expectedResponse);

            const result = await _processMessage(mockParams);

            expect(mockStateMachine.processMessage).toHaveBeenCalledWith(
                mockParams.userId,
                mockParams.message,
                mockParams.phoneNumber
            );
            expect(mockBaseResponse.ok).toHaveBeenCalledWith(
                'Message processed successfully',
                expect.objectContaining({
                    message: expectedResponse.message,
                    nextState: expectedResponse.nextState
                })
            );
            expect(result.statusCode).toBe(200);
        });

        test('should handle missing required parameters', async () => {
            const incompleteParams = {
                userId: '123456789',
                message: 'GS2'
                // phoneNumber is missing
            };

            const result = await _processMessage(incompleteParams);

            expect(mockBaseResponse.error).toHaveBeenCalledWith(
                'Desculpe, ocorreu um erro interno. Tente novamente em alguns instantes.'
            );
            expect(result.statusCode).toBe(500);
        });

        test('should handle state machine errors', async () => {
            const mockParams = {
                userId: '123456789',
                phoneNumber: '+5511999999999',
                message: 'GS2'
            };

            mockStateMachine.processMessage.mockRejectedValue(new Error('Database connection failed'));

            const result = await _processMessage(mockParams);

            expect(mockBaseResponse.error).toHaveBeenCalled();
            expect(result.statusCode).toBe(500);
        });
    });

    describe('_getSession', () => {
        test('should retrieve session successfully', async () => {
            const mockParams = { userId: '123456789' };
            const mockSession = {
                id: '123456789',
                phone_number: '+5511999999999',
                current_state: 'GREETING',
                session_data: { test: 'data' },
                created_at: new Date(),
                updated_at: new Date()
            };

            mockDatabase.getUserSession.mockResolvedValue(mockSession);

            const result = await _getSession(mockParams);

            expect(mockDatabase.getUserSession).toHaveBeenCalledWith(mockParams.userId);
            expect(mockBaseResponse.ok).toHaveBeenCalledWith(
                'Session retrieved successfully',
                expect.objectContaining({
                    session: expect.objectContaining({
                        id: mockSession.id,
                        phoneNumber: mockSession.phone_number,
                        currentState: mockSession.current_state
                    })
                })
            );
            expect(result.statusCode).toBe(200);
        });

        test('should handle session not found', async () => {
            const mockParams = { userId: '123456789' };
            mockDatabase.getUserSession.mockResolvedValue(null);

            const result = await _getSession(mockParams);

            expect(mockBaseResponse.ok).toHaveBeenCalledWith(
                'Session not found',
                { session: null }
            );
            expect(result.statusCode).toBe(200);
        });

        test('should handle missing userId parameter', async () => {
            const result = await _getSession({});

            expect(mockBaseResponse.error).toHaveBeenCalledWith(
                'Failed to retrieve session data'
            );
            expect(result.statusCode).toBe(500);
        });
    });

    describe('_saveUserData', () => {
        test('should save user data successfully', async () => {
            const mockParams = {
                sessionId: '123456789',
                clinicId: 1,
                userData: {
                    fullName: 'João da Silva',
                    cpf: '12345678901',
                    email: '<EMAIL>',
                    termsAccepted: true
                }
            };

            mockDatabase.saveUserData.mockResolvedValue(true);

            const result = await _saveUserData(mockParams);

            expect(mockDatabase.saveUserData).toHaveBeenCalledWith(
                mockParams.sessionId,
                mockParams.clinicId,
                mockParams.userData
            );
            expect(mockBaseResponse.ok).toHaveBeenCalledWith(
                'User data saved successfully',
                expect.objectContaining({
                    sessionId: mockParams.sessionId,
                    clinicId: mockParams.clinicId,
                    termsAccepted: mockParams.userData.termsAccepted
                })
            );
            expect(result.statusCode).toBe(200);
        });

        test('should handle missing required parameters', async () => {
            const incompleteParams = {
                sessionId: '123456789',
                userData: {
                    fullName: 'João da Silva'
                    // cpf and email are missing
                }
            };

            const result = await _saveUserData(incompleteParams);

            expect(mockBaseResponse.error).toHaveBeenCalledWith(
                'Failed to save user data'
            );
            expect(result.statusCode).toBe(500);
        });

        test('should mask CPF in logs', async () => {
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
            
            const mockParams = {
                sessionId: '123456789',
                clinicId: 1,
                userData: {
                    fullName: 'João da Silva',
                    cpf: '12345678901',
                    email: '<EMAIL>',
                    termsAccepted: true
                }
            };

            mockDatabase.saveUserData.mockResolvedValue(true);

            await _saveUserData(mockParams);

            expect(consoleSpy).toHaveBeenCalledWith(
                'Saving user data:',
                expect.objectContaining({
                    cpf: '123***' // CPF should be masked
                })
            );

            consoleSpy.mockRestore();
        });
    });

    describe('_healthCheck', () => {
        test('should return healthy status', async () => {
            mockDatabase.healthCheck.mockResolvedValue({
                status: 'healthy',
                timestamp: new Date().toISOString()
            });

            const result = await _healthCheck();

            expect(mockDatabase.healthCheck).toHaveBeenCalled();
            expect(mockBaseResponse.ok).toHaveBeenCalledWith(
                'System is healthy',
                expect.objectContaining({
                    status: 'healthy',
                    service: 'wpphook-chatbot'
                })
            );
            expect(result.statusCode).toBe(200);
        });

        test('should return unhealthy status when database fails', async () => {
            mockDatabase.healthCheck.mockResolvedValue({
                status: 'unhealthy',
                error: 'Connection timeout',
                timestamp: new Date().toISOString()
            });

            const result = await _healthCheck();

            expect(mockBaseResponse.error).toHaveBeenCalledWith(
                'System health check failed',
                expect.objectContaining({
                    status: 'unhealthy'
                })
            );
        });

        test('should handle health check errors', async () => {
            mockDatabase.healthCheck.mockRejectedValue(new Error('Health check failed'));

            const result = await _healthCheck();

            expect(mockBaseResponse.error).toHaveBeenCalledWith(
                'Health check failed',
                expect.objectContaining({
                    status: 'unhealthy',
                    error: 'Health check failed'
                })
            );
        });
    });

    describe('_cleanupSessions', () => {
        test('should cleanup expired sessions successfully', async () => {
            const mockParams = { hoursOld: 48 };
            const cleanedCount = 5;

            mockStateMachine.cleanupExpiredSessions.mockResolvedValue(cleanedCount);

            const result = await _cleanupSessions(mockParams);

            expect(mockStateMachine.cleanupExpiredSessions).toHaveBeenCalled();
            expect(mockBaseResponse.ok).toHaveBeenCalledWith(
                'Session cleanup completed',
                expect.objectContaining({
                    cleanedSessions: cleanedCount,
                    hoursOld: mockParams.hoursOld
                })
            );
            expect(result.statusCode).toBe(200);
        });

        test('should use default hours when not provided', async () => {
            const cleanedCount = 3;
            mockStateMachine.cleanupExpiredSessions.mockResolvedValue(cleanedCount);

            const result = await _cleanupSessions();

            expect(mockBaseResponse.ok).toHaveBeenCalledWith(
                'Session cleanup completed',
                expect.objectContaining({
                    hoursOld: 24 // default value
                })
            );
        });

        test('should handle cleanup errors', async () => {
            mockStateMachine.cleanupExpiredSessions.mockRejectedValue(new Error('Cleanup failed'));

            const result = await _cleanupSessions();

            expect(mockBaseResponse.error).toHaveBeenCalledWith('Session cleanup failed');
            expect(result.statusCode).toBe(500);
        });
    });

    describe('_getSessionStats', () => {
        test('should retrieve session statistics successfully', async () => {
            const mockStats = [
                { current_state: 'GREETING', count: 10, avg_duration_minutes: 5.5 },
                { current_state: 'COMPLETION', count: 8, avg_duration_minutes: 15.2 }
            ];

            mockStateMachine.getSessionStats.mockResolvedValue(mockStats);

            const result = await _getSessionStats();

            expect(mockStateMachine.getSessionStats).toHaveBeenCalled();
            expect(mockBaseResponse.ok).toHaveBeenCalledWith(
                'Session statistics retrieved',
                expect.objectContaining({
                    stats: mockStats
                })
            );
            expect(result.statusCode).toBe(200);
        });

        test('should handle stats retrieval errors', async () => {
            mockStateMachine.getSessionStats.mockRejectedValue(new Error('Stats failed'));

            const result = await _getSessionStats();

            expect(mockBaseResponse.error).toHaveBeenCalledWith(
                'Failed to retrieve session statistics'
            );
            expect(result.statusCode).toBe(500);
        });
    });

    describe('_getUserJourney', () => {
        test('should retrieve user journey successfully', async () => {
            const mockParams = { userId: '123456789' };
            const mockJourney = {
                session: { current_state: 'GREETING' },
                interactions: [{ message_type: 'INCOMING', message_content: 'GS2' }],
                totalInteractions: 1
            };

            mockStateMachine.getUserJourney.mockResolvedValue(mockJourney);

            const result = await _getUserJourney(mockParams);

            expect(mockStateMachine.getUserJourney).toHaveBeenCalledWith(mockParams.userId);
            expect(mockBaseResponse.ok).toHaveBeenCalledWith(
                'User journey retrieved successfully',
                expect.objectContaining({
                    journey: mockJourney
                })
            );
            expect(result.statusCode).toBe(200);
        });

        test('should handle journey not found', async () => {
            const mockParams = { userId: '123456789' };
            mockStateMachine.getUserJourney.mockResolvedValue(null);

            const result = await _getUserJourney(mockParams);

            expect(mockBaseResponse.ok).toHaveBeenCalledWith(
                'User journey not found',
                { journey: null }
            );
        });

        test('should handle missing userId', async () => {
            const result = await _getUserJourney({});

            expect(mockBaseResponse.error).toHaveBeenCalledWith(
                'Failed to retrieve user journey'
            );
            expect(result.statusCode).toBe(500);
        });
    });

    describe('_resetUserSession', () => {
        test('should reset user session successfully', async () => {
            const mockParams = { userId: '123456789', reason: 'customer_request' };
            mockStateMachine.resetSession.mockResolvedValue(true);

            const result = await _resetUserSession(mockParams);

            expect(mockStateMachine.resetSession).toHaveBeenCalledWith(
                mockParams.userId,
                mockParams.reason
            );
            expect(mockBaseResponse.ok).toHaveBeenCalledWith(
                'Session reset successful',
                expect.objectContaining({
                    userId: mockParams.userId,
                    reason: mockParams.reason
                })
            );
            expect(result.statusCode).toBe(200);
        });

        test('should use default reason when not provided', async () => {
            const mockParams = { userId: '123456789' };
            mockStateMachine.resetSession.mockResolvedValue(true);

            const result = await _resetUserSession(mockParams);

            expect(mockStateMachine.resetSession).toHaveBeenCalledWith(
                mockParams.userId,
                'manual_reset'
            );
        });

        test('should handle reset failure', async () => {
            const mockParams = { userId: '123456789' };
            mockStateMachine.resetSession.mockResolvedValue(false);

            const result = await _resetUserSession(mockParams);

            expect(mockBaseResponse.error).toHaveBeenCalledWith('Session reset failed');
            expect(result.statusCode).toBe(500);
        });

        test('should handle missing userId', async () => {
            const result = await _resetUserSession({});

            expect(mockBaseResponse.error).toHaveBeenCalledWith('Session reset failed');
            expect(result.statusCode).toBe(500);
        });
    });
});