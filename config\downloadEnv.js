const fs = require('fs');
const path = require('path');
const { getParams, getFunctionDescription } = require('./functions');

const getOriginLambda = (lambdaName = '') => {
  return path.resolve(__dirname, '..', 'src', 'lambdas', lambdaName);
};

const main = async () => {
  try {
    const { lambdaName, profile, region = 'us-east-2' } = await getParams();

    const { Configuration } = await getFunctionDescription({
      profile,
      region,
      name: lambdaName,
    });

    if (!Configuration?.Environment?.Variables) {
      Configuration.Environment = {
        Variables: {
          REGION: region,
        },
      };
    }

    Configuration.ConfiguracaoLocal = {
      lambdaName,
      profile,
      region,
    };

    fs.writeFileSync(
      `${getOriginLambda(lambdaName)}/Configuration.local.json`,
      JSON.stringify(Configuration, null, 2)
    );
    console.log('\nFinalizado!');
  } catch (error) {
    console.log('\n');
    console.log('\x1b[31m%s\x1b[0m', 'Catch no downloadEnv():');
    console.log(error.message);
    console.log('\n');
  } finally {
    process.exit();
  }
};

main();
