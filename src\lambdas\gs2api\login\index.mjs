import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import mysql from 'mysql2/promise';

// Configuração do banco de dados
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'gs2',
  port: process.env.DB_PORT || 3306
};

// Função para conectar ao banco
async function getConnection() {
  return await mysql.createConnection(dbConfig);
}

// Função para executar queries
async function dbQuery(query, params = []) {
  const connection = await getConnection();
  try {
    const [results] = await connection.execute(query, params);
    return results;
  } finally {
    await connection.end();
  }
}

// Função para limpar números
const onlyNumber = (n) => String(n).replace(/\D/g, '');

// Respostas padronizadas
const baseResponse = {
  ok: (message = '', data = null) => ({ statusCode: 200, message, success: true, data }),
  error: (message = '') => ({ statusCode: 500, message, success: false }),
  unauthorized: (message = '') => ({ statusCode: 401, message, success: false }),
  forbidden: (message = '') => ({ statusCode: 403, message, success: false })
};

// Função para gerar JWT token
function generateToken(user) {
  const payload = {
    id: user.id,
    cpf: user.cpf,
    email: user.email,
    nome: user.nome
  };

  return jwt.sign(payload, process.env.JWT_SECRET || 'gs2-secret-key', {
    expiresIn: '24h'
  });
}

export const handler = async (event) => {
  const CPF = onlyNumber(event?.username || event?.cpf);

  try {
    // Buscar usuário
    const usuarios = await dbQuery(
      `SELECT id, usCPFUsuario, usSenha, usEmail, usNome, isMaster, psOnboardingPendente
       FROM capUsuario WHERE usCPFUsuario = ?`,
      [CPF]
    );

    if (!usuarios || usuarios.length === 0) {
      return baseResponse.unauthorized('Usuário ou senha inválida');
    }

    const usuario = usuarios[0];

    // Verificar senha
    const isLogin = bcrypt.compareSync(event.password || '', usuario?.usSenha || '');

    if (!isLogin) {
      return baseResponse.unauthorized('Usuário ou senha inválida');
    }

    // Gerar token JWT
    const token = generateToken({
      id: usuario.id,
      cpf: usuario.usCPFUsuario,
      email: usuario.usEmail,
      nome: usuario.usNome
    });

    // Buscar dados completos do usuário
    const dadosUsuario = await dbQuery(
      `SELECT
          a.id,
          a.usCPFUsuario as cpf,
          a.usEmail as email,
          a.usNome as nome,
          a.usSenhaNova as resetPassword,
          a.psOnboardingPendente,
          a.usAceiteLGPD
        FROM capUsuario a
        WHERE a.usCPFUsuario = ?`,
      [CPF]
    );

    if (!dadosUsuario || dadosUsuario.length === 0) {
      return baseResponse.unauthorized('Usuário não encontrado');
    }

    const userData = dadosUsuario[0];

    // Buscar grupos do usuário (simplificado)
    const grupos = await dbQuery(
      `SELECT gmNome as nome, gmDescricao as descricao
       FROM usuarioGrupo ug
       JOIN grupos g ON ug.grupoId = g.id
       WHERE ug.usuarioCPF = ?`,
      [CPF]
    );

    const dadosUsuarioResponse = {
      id: userData.id,
      cpf: CPF,
      email: userData.email,
      nome: userData.nome,
      aceiteLGPD: userData.usAceiteLGPD === 1,
      resetPassword: userData.resetPassword,
      psOnboardingPendente: userData.psOnboardingPendente === 1,
      grupos: grupos || []
    };

    // Buscar configurações do sistema (simplificado)
    const configuracoes = await dbQuery(
      `SELECT chave, valor FROM capConfiguracao
       WHERE chave IN ('valor_minimo_solicitacao', 'valor_maximo_solicitacao')`,
      []
    );

    const configMap = {};
    configuracoes.forEach(config => {
      configMap[config.chave] = config.valor;
    });

    // Buscar rotas/permissões do usuário (simplificado)
    const rotas = await dbQuery(
      `SELECT r.rota, r.nomeTela, r.icone
       FROM usuarioGrupo ug
       JOIN grupoRotas gr ON ug.grupoId = gr.grupoId
       JOIN rotas r ON gr.rotaId = r.id
       WHERE ug.usuarioCPF = ?
       ORDER BY r.ordem`,
      [CPF]
    );

    // Montar resposta final
    const data = {
      user: {
        ...dadosUsuarioResponse,
        rotas: dadosUsuarioResponse.psOnboardingPendente ? [] : rotas,
        valorMinimoSolicitacao: configMap['valor_minimo_solicitacao'] || 0,
        valorMaximoSolicitacao: configMap['valor_maximo_solicitacao'] || 0,
      },
      token,
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 horas
    };

    return baseResponse.ok('Login realizado com sucesso', data);

  } catch (error) {
    console.error('Erro no login:', error);
    return baseResponse.error('Erro interno do servidor');
  }
};
