const { execTerminal, getArg } = require('./functions');

const profile = getArg('profile');
const region = getArg('region');
const role = getArg('role');

if (!profile || !region) {
  console.log(
    '\x1b[31m%s\x1b[0m',
    '\nVocê precisa passar "profile" e "region"\n'
  );
  process.exit();
}

const lambdas = require('./lambdas')[region];

const main = async () => {
  const logs = [];
  try {
    for await (let lambda of lambdas) {
      const result = await execTerminal(
        `yarn updateFunction profile=${profile} region=${region} lambdaName=${lambda} env=prod ${
          role ? `--role ${role}` : ''
        }`
      );
      console.log(result);
      logs.push(result);
    }
  } catch (error) {
    console.log('\n');
    console.log('\x1b[31m%s\x1b[0m', 'Catch no deployAll():');
    console.log(error.message);
    logs.push(error.message);
    console.log('\n');
  } finally {
    require('fs').writeFileSync(
      `LOG_deployAll_${Date.now()}.local.json`,
      JSON.stringify(logs)
    );
    process.exit();
  }
};

main();
