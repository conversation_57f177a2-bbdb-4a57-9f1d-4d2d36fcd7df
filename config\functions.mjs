import dotenv from 'dotenv';
import fs from 'fs';
import { exec } from 'child_process';
import decompress from 'decompress';
import https from 'https';
import { promisify } from 'util';

dotenv.config();

const execAsync = promisify(exec);

/**
 * @param {string} message
 * @param {{
 *  inputType?: 'inteiro'|'string'
 *  stringType?: 'lower'|'upper'
 *  acceptOptions?: Array<string>
 * }} config
 * @returns {Promise<string>}
 */
function readInputTerminal(message, config) {
    return new Promise((resolve) => {
        process.stdout.write('\n' + message + '\nR: ');
        process.stdin.resume();
        process.stdin.setEncoding('utf8');
        process.stdin.once('data', (readMessage) => {
            let result = String(readMessage.replace(/\r?\n|\r/g, ''));
            if (config?.stringType === 'lower') {
                result = result.toLowerCase();
            }
            if (config?.stringType === 'upper') {
                result = result.toUpperCase();
            }
            if (config?.inputType === 'inteiro') {
                result = Number(result);
                if (isNaN(result)) {
                    console.log('Deve responder um tipo number inteiro');
                    process.exit();
                }
            }
            if (config?.acceptOptions) {
                result = config.acceptOptions.includes(result);
            }
            resolve(result);
        });
    });
}

const getArgs = () => {
    try {
        return JSON.parse(process.env.npm_config_argv || '{}');
    } catch (error) {
        return {};
    }
};

const getArg = (arg = '') => {
    let finded = process.argv.find((a) => a.startsWith(`${arg}=`));

    if (!finded) {
        finded = String(process.env.npm_lifecycle_script)
            .split(' ')
            .find((a) => a.replace(/"/g, '').startsWith(`${arg}=`));
    }

    if (!finded) {
        const args = getArgs();
        if (args && args.original && Array.isArray(args.original)) {
            finded = args.original.find((a) => a.startsWith(`${arg}=`));
        }
    }

    if (finded) {
        return finded.split('=')[1];
    }
    return '';
};

const verifyDir = (dir = '') => {
    return fs.lstatSync(dir).isDirectory();
};

/**
 * @param {Array<'env' | 'profile' | 'region' | 'lambdaName'>} requireds
 * @returns {Promise<{
 *  profile: string
 *  region: string
 *  lambdaName: string
 *  env: string
 * }>}
 */
const getParams = async (requireds = [], silenceDefault = false) => {
    const silence = getArg('silence');
    const shouldSilence = silenceDefault === true || ['ok', 'true', '1', 'yes', 's'].some(
        (key) => key === silence.toLowerCase()
    );
    const env = getArg('env') || 'prod';

    const profile = process.env.AWS_PROFILE || getArg('profile');

    const region =
        (env.toLowerCase() === 'prod'
            ? process.env.AWS_REGION_PROD
            : process.env.AWS_REGION_DEV) || getArg('region');

    const lambdaName = getArg('lambdaName');
    const layerName = getArg('layerName');

    const myArguments = { profile, region, env, layerName };
    console.log({ myArguments });

    if (requireds.length > 0 && !requireds.every((k) => !!myArguments[k])) {
        throw new Error(
            'Devem ser passados os seguintes parâmetros:\n\nprofile=seu_perfil_aws\nregion=regiao_na_aws\nenv=prod|dev'
        );
    }

    const response = {
        profile,
        region,
        lambdaName,
        env,
        layerName,
    };

    Object.keys(response).forEach((key) => {
        if (!response[key]) {
            delete response[key];
        }
    });

    if (!shouldSilence) {
        const answer = await readInputTerminal(
            'Verificou os parâmetros? Pode continuar o processo? (S|N)',
            {
                stringType: 'lower',
                acceptOptions: ['s', 'sim', '1', 'ok', 'yes'],
            }
        );

        if (!answer) {
            process.exit();
        }
    }

    return response;
};

const getFunctionDescription = async function ({ name, profile, region }) {
    return new Promise((resolve, reject) => {
        exec(
            `aws lambda get-function --function-name ${name} --profile ${profile} --region ${region}`,
            function (err, data, stderr) {
                if (err || stderr) {
                    reject(
                        new Error(
                            'Catch na função getFunctionDescription(): ' +
                                err?.message || stderr
                        )
                    );
                } else {
                    resolve(JSON.parse(data));
                }
            }
        );
    });
};

const updateFunction = async function ({
    pathFile,
    FunctionName,
    Runtime,
    Role,
    profile,
    region,
}) {
    let exists = false;
    try {
        exists = await getFunctionDescription({
            name: FunctionName,
            profile,
            region,
        });
    } catch (error) {
        if (error.message.includes('Function not found')) {
            exists = false;
        }
    }
    return new Promise((resolve) => {
        let command = `aws lambda create-function --function-name ${FunctionName} --zip-file fileb://${pathFile} --handler index.handler --runtime ${Runtime} --role ${Role} --profile ${profile} --region ${region}`;

        if (exists) {
            command = `aws lambda update-function-code --function-name ${FunctionName} --zip-file fileb://${pathFile} --publish --profile ${profile} --region ${region}`;
        }

        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.log(`error: ${error.message}`);
                resolve([]);
            }
            if (stderr) {
                console.log(`stderr: ${stderr}`);
                resolve([]);
            }
            resolve(stdout);
        });
    });
};

const unzip = (url, output) => {
    return new Promise((resolve, reject) => {
        https.get(url, function (res) {
            res.setEncoding('binary');

            let data = [];

            res.on('data', function (chunk) {
                data.push(Buffer.from(chunk, 'binary'));
            });
            res.on('end', async function () {
                const binary = Buffer.concat(data);
                await decompress(binary, output, {
                    filter: (i) => {
                        return !i.path.startsWith('node_modules');
                    },
                });
                resolve();
            });
            res.on('error', function (err) {
                console.log('Error during HTTP request');
                console.log(err.message);
                reject();
            });
        });
    });
};

const unzipLayer = (url, output) => {
    return new Promise((resolve, reject) => {
        https.get(url, function (res) {
            res.setEncoding('binary');

            let data = [];

            res.on('data', function (chunk) {
                data.push(Buffer.from(chunk, 'binary'));
            });
            res.on('end', async function () {
                const binary = Buffer.concat(data);

                // await decompress(binary, output);

                await decompress(binary, output, {
                    filter: (i) => {
                        if (i.path.includes('capfunctions')) {
                            return true;
                        }
                        return false;
                    },
                });

                resolve();
            });
            res.on('error', function (err) {
                console.log('Error during HTTP request');
                console.log(err.message);
                reject();
            });
        });
    });
};

const execTerminal = async (term) => {
    try {
        const { stdout, stderr } = await execAsync(term);
        if (stderr) {
            console.error('Stderr:', stderr);
            throw new Error(stderr);
        }
        console.log('Exec Output:', stdout);
        return stdout;
    } catch (error) {
        console.error('Exec Error:', error);
        throw error;
    }
};

const getListFunctions = async (profile, region) => {
    if (!profile || !region) throw new Error('parametros invalidos');
    return await execTerminal(
        `aws lambda list-functions --profile ${profile} --region ${region}`
    );
};

export {
    getArg,
    verifyDir,
    getParams,
    updateFunction,
    getFunctionDescription,
    unzip,
    readInputTerminal,
    execTerminal,
    getListFunctions,
    unzipLayer,
};
