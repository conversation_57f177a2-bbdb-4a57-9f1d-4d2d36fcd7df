# docker-compose -p capitale-api up --build -d

services:
    capitale_database:
        image: docker.io/mysql:8.0.37
        container_name: capitale_database
        command: --default-authentication-plugin=mysql_native_password --sql-mode="STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION"
        ports:
            - '3336:3306'
        environment:
            MYSQL_ROOT_PASSWORD: $MYSQL_PASSWORD
            MYSQL_DATABASE: $MYSQL_DATABASE
            MYSQL_USER: $MYSQL_USER
            MYSQL_PASSWORD: $MYSQL_PASSWORD
            TZ: America/Sao_Paulo
        volumes:
            - db_data:/var/lib/mysql
            - ./dump:/docker-entrypoint-initdb.d
volumes:
    db_data:
