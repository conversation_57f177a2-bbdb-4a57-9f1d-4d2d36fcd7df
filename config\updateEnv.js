const { exec } = require('child_process');
const path = require('path');
const { verifyDir, getParams, getFunctionDescription } = require('./functions');

const updateFunctionConfig = async function ({
  Variables,
  profile,
  region,
  lambdaName,
  timeout = 30,
}) {
  if (Variables) {
    const exists = await getFunctionDescription({
      name: lambdaName,
      profile,
      region,
    });
    if (exists) {
      let environments =
        '--environment "Variables={' +
        Object.entries(Variables)
          .map((i) => `${i[0]}=${i[1]}`)
          .join(',') +
        '}"';
      return new Promise((resolve) => {
        let command = `aws lambda update-function-configuration --function-name ${lambdaName} ${environments} --profile ${profile} --region ${region} --timeout ${timeout}`;
        exec(command, (error, stdout, stderr) => {
          if (error) {
            console.log(`error: ${error.message}`);
            resolve([]);
          }
          if (stderr) {
            console.log(`stderr: ${stderr}`);
            resolve([]);
          }
          resolve(stdout);
        });
      });
    }
  }
};

const getOriginDir = (lambdaName = '') => {
  return path.resolve(__dirname, '..', 'src', 'lambdas', lambdaName);
};

const main = async () => {
  try {
    const { lambdaName, profile, region = 'us-east-2' } = await getParams();

    const originDir = getOriginDir(lambdaName);
    verifyDir(`${originDir}/Configuration.local.json`);

    const json = require(`${originDir}/Configuration.local.json`);
    console.log(json.Environment.Variables);
    if (
      !json.Environment.Variables ||
      typeof json.Environment.Variables !== 'object'
    ) {
      throw new Error(
        `Deve configurar ${originDir}/Configuration.local.json corretamente, execute o downloadEnv primeiro`
      );
    }

    await updateFunctionConfig({
      lambdaName,
      profile,
      region,
      Variables: json.Environment.Variables,
    });
  } catch (error) {
    console.log('\n');
    console.log('\x1b[31m%s\x1b[0m', 'Catch no updateEnv():');
    console.log(error.message);
    console.log('\n');
  } finally {
    process.exit();
  }
};

main();
