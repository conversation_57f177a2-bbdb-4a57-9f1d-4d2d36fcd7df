import 'dotenv/config';

import mysql from 'mysql';
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import { LambdaClient, InvokeCommand } from '@aws-sdk/client-lambda';

import jwt from 'jsonwebtoken';

const connOptions = {
  port: process.env.MYSQL_PORT,
  host: process.env.MYSQL_HOST,
  user: process.env.MYSQL_USER,
  password: process.env.MYSQL_PASSWORD,
  database: process.env.MYSQL_DATABASE,
  multipleStatements: true,
  dateStrings: true,
};

const SECRET_KEY = process.env.SECRET_KEY || '8rt52gs36cv9vssf74ghh14545';
const expiresIn = 60 * Number(300);

let connection = mysql.createConnection(connOptions);
if (typeof connection === 'undefined') {
  connection = mysql.createConnection(connOptions);
  console.log('Re-connect mysql');
  connection.connect();
}

function createResponse(statusCode = 404, message = '', success = false, data = null) {
  return { statusCode, message, success, data };
}

function createToken(email, secretKey, expiresIn) {
  console.info('Criando Token com data expiração ', {
    expiresIn: expiresIn * 1,
  });
  return jwt.sign({ email }, secretKey, {
    expiresIn: expiresIn * 1,
  });
}

function checkToken(token, secretKey, expiresIn) {
  try {
    decoded = jwt.verify(token, secretKey);
    console.info({ decoded });
    newToken = createToken(decoded.email, secretKey, expiresIn);
    return { ...decoded, newToken, success: true };
  } catch (err) {
    console.info({ token, secretKey, expiresIn });
    console.info(err);
    return { statusCode: 404, success: false, message: 'Token expirado' };
  }
}

const verifyToken = async (token) => {
  if (!token || token === '') {
    return {
      success: false,
      message: 'Não foi enviado token nos headers da requisição',
    };
  }

  try {
    const decoded = jwt.verify(token, SECRET_KEY);
    if (!('exp' in decoded) || !('iat' in decoded)) {
      return {
        success: false,
        message: 'Token inválido',
      };
    }

    const newToken = jwt.sign(
      {
        user: decoded.user,
      },
      SECRET_KEY,
      { expiresIn }
    );

    return {
      success: true,
      body: { ...decoded, newToken },
      newToken,
    };
  } catch (error) {
    console.log('Catch na função verifyToken()', error);
    return {
      success: false,
      message: 'Token inválido',
    };
  }
};

const getUserFromToken = async (tokenFromHeader) => {
  try {
    if (tokenFromHeader) {
      const token = await verifyToken(tokenFromHeader);

      const user = await find('capUsuario', {
        columns: ['usCPFUsuario', 'usNome'],
        where: {
          usCPFUsuario: token.body.user,
        },
      });

      if (user?.results?.usCPFUsuario !== '') {
        const query = `SELECT * FROM brcUsuarioGrupoMenu where usuarioCPF="${user?.results?.usCPFUsuario}"`;
        const queryResult = await execQuery(query);

        if (queryResult.success) {
          const groups = queryResult.results.map((result) => result.gmNome);

          return {
            body: {
              user: user.results.usCPFUsuario,
              userName: user.results.usNome,
              newToken: token.body.newToken,
              groups,
            },
          };
        }
      }
    }

    return null;
  } catch (error) {
    console.log('Catch na função getUserFromToken()', error);
    return null;
  }
};

function checkData(event, dados = { obrigatorios: [], peloMenosUm: [] }) {
  if (dados.obrigatorios) {
    const contemTodosObrigatorio = dados.obrigatorios.every(function (input) {
      return input in event && !!event[input];
    });
    if (!contemTodosObrigatorio) {
      return true;
    }
  }
  if (dados.peloMenosUm) {
    const contemPeloMenosUm = dados.peloMenosUm.some(function (input) {
      return input in event;
    });
    if (!contemPeloMenosUm) {
      return true;
    }
  }

  return false;
}

function paginationList(query, limit = 25, page = 0, orderBy = {}) {
  let _orderByArray = Object.keys(orderBy).filter(
    (v) => orderByFields.includes(v) && ['asc', 'desc'].includes(orderBy[v])
  );
  let _orderBy = '';
  if (_orderByArray.length > 0)
    _orderBy = ' ORDER BY ' + _orderByArray.map((v) => v + ' ' + orderBy[v]).join(', ');
  console.info(_orderBy);
  return (
    'SELECT * FROM (' + query + _orderBy + ') t LIMIT ' + limit * 1 + ' OFFSET ' + page * limit
  );
}

async function execQuery(query, params = []) {
  console.log('execQuery()', {
    query,
    params,
  });
  console.info({ query, params });
  return new Promise(function (resolve, reject) {
    connection.query(query, params, function (error, response) {
      if (error) {
        console.info('QueryError in execQuery', error);
        resolve({ success: false, error });
      } else {
        console.log('Response: ', response);
        if ('length' in response || response.affectedRows > 0) {
          resolve({ success: true, results: response });
        }
        resolve({ success: false, error });
      }
    });
  });
}

async function dbQuery(query, params = []) {
  console.log('execQuery()', {
    query,
    params,
  });
  return new Promise(function (resolve, reject) {
    connection.query(query, params, function (error, response) {
      if (error) {
        console.info('QueryError in execQuery', error);
        reject(error);
      } else {
        resolve(response);
      }
    });
  });
}

async function sendToLog(event) {
  if (process.env.IS_LOCAL === 'true') {
    return { success: true };
  }

  const sqsClient = new SQSClient({ region: 'us-east-2' });
  const queueUrl = 'https://sqs.us-east-2.amazonaws.com/730335345607/log.fifo';

  const params = {
    QueueUrl: queueUrl,
    MessageBody: JSON.stringify(event),
    MessageGroupId: 'log',
  };

  try {
    const command = new SendMessageCommand(params);
    const response = await sqsClient.send(command);
    console.log('Message sent successfully, ID:', response.MessageId);
    return { success: true };
  } catch (err) {
    console.error('Error sending message:', err);
    return { success: false, err };
  }
}

async function insert(table, data, event) {
  console.log('insert()', {
    table,
    data,
  });

  const strDateValid = ['current_timestamp', 'current_timestamp()', 'now()', 'now'];

  const isDate = (input) => input && strDateValid.includes(String(input));

  const keys = Object.keys(data);
  const values = keys.map((key) =>
    isDate(data[key]) ? mysql.raw('current_timestamp') : data[key]
  );

  let query = `
        INSERT INTO ${table} (${keys.join(', ')})
        VALUES (${keys.map(() => '?').join(', ')})
    `;

  console.info({ query, values });

  try {
    const logResult = await sendToLog({
      table: table,
      data: data,
      tokenReg: event?.body?.newToken,
      usTrans: event?.body?.userName,
      tpTrans: 'INS',
    });

    if (!logResult.success) {
      console.log(`Failed to log INSERT for table ${table}`);
      return { success: false, error: 'Failed to log INSERT' };
    }

    const results = await new Promise((resolve, reject) => {
      connection.query(query, values, function (error, results) {
        if (error) {
          console.error('Error in query:', error);
          reject(error);
        } else {
          console.info('Query executed successfully:', results);
          resolve(results);
        }
      });
    });

    return { success: true, results };
  } catch (error) {
    console.error('Error in insert function:', error);
    return { success: false, error };
  }
}

async function deleteEntity(table, where, event) {
  console.log('deleteEntity()', {
    table,
    where,
  });
  const whereClause = Object.entries(where)
    .map(([key, value]) => {
      if (typeof value === 'boolean' || value === null) {
        return `${key} ${value ? 'IS NOT NULL' : 'IS NULL'}`;
      }
      if (['current_timestamp', 'now'].includes(value)) {
        return `${key} = CURRENT_TIMESTAMP`;
      }
      return `${key} = ?`;
    })
    .join(' AND ');

  const query = `DELETE FROM ${table} WHERE ${whereClause}`;

  const values = Object.values(where);

  try {
    const logResult = await sendToLog({
      table: table,
      data: where,
      tokenReg: event?.body?.newToken,
      usTrans: event?.body?.userName,
      tpTrans: 'DEL',
    });

    if (!logResult.success) {
      console.log(`Failed to log DELETE for table ${table}`);
      return { success: false, error: 'Failed to log DELETE' };
    }

    const results = await new Promise((resolve, reject) => {
      connection.query(query, values, function (error, results) {
        if (error) {
          console.error('Error in DELETE:', error);
          reject(error);
        } else {
          console.info('DELETE executed successfully:', results);
          resolve(results);
        }
      });
    });

    return { success: true, results };
  } catch (error) {
    console.error('Error in DELETE function:', error);
    return { success: false, error };
  }
}

async function update(table, data, where, event) {
  const setClause = Object.entries(data)
    .map(([key, value]) => {
      if (['current_timestamp', 'now'].includes(value)) {
        return `${key} = CURRENT_TIMESTAMP`;
      }
      return `${key} = ?`;
    })
    .join(', ');

  const whereClause = Object.entries(where)
    .map(([key, value]) => {
      if (typeof value === 'boolean') {
        return `${key} ${value ? 'IS NOT NULL' : 'IS NULL'}`;
      }
      if (['current_timestamp', 'now'].includes(value)) {
        return `${key} = CURRENT_TIMESTAMP`;
      }
      return `${key} = ?`;
    })
    .join(' AND ');

  const query = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`;

  const dataValues = Object.values(data).filter(
    (value) => !['current_timestamp', 'now'].includes(value)
  );

  const whereValues = Object.values(where).filter(
    (value) => typeof value !== 'boolean' && !['current_timestamp', 'now'].includes(value)
  );

  const values = [...dataValues, ...whereValues];

  try {
    const logResult = await sendToLog({
      table,
      data,
      where,
      tokenReg: event?.body?.newToken,
      usTrans: event?.body?.userName,
      tpTrans: 'UPD',
    });

    if (!logResult.success) {
      console.log(`Failed to log UPDATE for table ${table}`);
      return { success: false, error: 'Failed to log UPDATE' };
    }

    const results = await dbQuery(query, values);
    return { success: true, results };
  } catch (error) {
    console.error('Error in UPDATE function:', error);
    return { success: false, error };
  }
}

async function find(table, { columns = [], where = {} }) {
  const whereClause = Object.entries(where)
    .map(([key, value]) => {
      if (typeof value === 'boolean') {
        return `${key} ${value ? 'IS NOT NULL' : 'IS NULL'}`;
      }
      if (['current_timestamp', 'now'].includes(value)) {
        return `${key} = CURRENT_TIMESTAMP`;
      }
      return `${key} = ?`;
    })
    .join(' AND ');

  const query = `SELECT ${columns.length > 0 ? columns.join(', ') : '*'} 
                  FROM ${table} 
                  WHERE ${whereClause}`;

  const params = Object.values(where).filter(
    (value) => typeof value !== 'boolean' && !['current_timestamp', 'now'].includes(value)
  );

  try {
    const results = await dbQuery(query, params);
    if (results.length > 0) {
      return { success: true, results: results[0] };
    }
    console.log('Não encontrou no find()');
    return { success: false, error: 'Not found' };
  } catch (error) {
    console.log('Erro na query find()', error);
    return { success: false, error };
  }
}

async function list(table, { columns = [], where = {} }) {
  const whereKeys = Object.keys(where);
  const whereValues = Object.values(where);

  const whereQuery = whereKeys
    .map((key, index) => {
      const value = whereValues[index];

      if (typeof value === true) {
        return `${key} IS NOT NULL`;
      }

      if (typeof value === null || value === undefined || value === false) {
        return `${key} IS NULL`;
      }

      if (['current_timestamp', 'now'].includes(value)) {
        return `${key} = CURRENT_TIMESTAMP`;
      }

      return `${key} = ?`;
    })
    .join(' AND ');

  const params = whereValues.filter(
    (value) =>
      typeof value !== undefined &&
      typeof value !== 'boolean' &&
      typeof value !== null &&
      !['current_timestamp', 'now'].includes(value)
  );

  return dbQuery(
    `SELECT ${columns.length > 0 ? columns.join(', ') : '*'} FROM ${table}${
      whereQuery ? ` WHERE ${whereQuery}` : ''
    }`,
    params
  );
}

const lambdaInvoke = async (FunctionName, Payload) => {
  if (process.env.IS_LOCAL === 'true') {
    const { handler } = await import(`../../../src/lambdas/${FunctionName}/index.mjs`);

    return await handler(Payload);
  }

  try {
    const { handler } = await import(`../../lambdas/${FunctionName}/index.mjs`);

    return await handler(Payload);
  } catch (error) {
    console.log('Erro no lambdaInvoke', error);
    const lambdaClient = new LambdaClient({
      region: process.env.REGION || 'us-east-2',
    });

    const params = {
      FunctionName,
      Payload: Buffer.from(JSON.stringify(Payload, null, 2)),
    };

    try {
      const command = new InvokeCommand(params);
      const data = await lambdaClient.send(command);

      let payload;
      try {
        payload = JSON.parse(Buffer.from(data.Payload).toString());
      } catch (error) {
        throw new Error('Failed to parse response payload');
      }

      return payload;
    } catch (err) {
      console.error('Error invoking Lambda function:', err);
      throw err;
    }
  }
};

const baseResponse = {
  ok: (message = '', data = null) => {
    return { statusCode: 200, message, success: true, data };
  },
  created: (message = '', data = null) => {
    return { statusCode: 201, message, success: true, data };
  },
  error: (message = '') => {
    //usa em exceptions
    return { statusCode: 500, message, success: false };
  },
  notFound: (message = '') => {
    return { statusCode: 404, message, success: false };
  },
  unauthorized: (message = '') => {
    return { statusCode: 401, message, success: false };
  },
  forbidden: (message = '', data = null) => {
    return { statusCode: 403, message, success: false, data };
  },
  badRequest: (message = '', data = null) => {
    return { statusCode: 400, message, success: false, data };
  },
  paginated: (message = '', { data, currentPage, totalPages, nextPage }) => {
    return { statusCode: 200, message, success: true, totalPages, currentPage, nextPage, data };
  },
};

const onlyNumber = (n) => String(n).replace(/\D/g, '');

const isEmailValid = (email) => {
  if (!email) return false;
  var tester =
    /^[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/;

  var emailParts = email.split('@');

  if (emailParts.length !== 2) return false;

  var account = emailParts[0];
  var address = emailParts[1];

  if (account.length > 64) return false;
  else if (address.length > 255) return false;

  var domainParts = address.split('.');
  if (
    domainParts.some(function (part) {
      return part.length > 63;
    })
  )
    return false;

  if (!tester.test(email)) return false;

  return true;
};

const UserGroupTypes = Object.freeze({
  ESCALISTA: 'escalista',
  GESTOR_CAPITALE: 'gestorcapitale',
  GESTOR_CLIENTE: 'gestorcliente',
  PROF_SAUDE: 'profsaude',
  TECNOLOGIA: 'tecnologia',
});

function formataHora(time) {
  /*
      - A função formataHora recebe uma string no formato 'HH:MM:SS' e retorna uma string no mesmo formato.
      - Se a string passada não estiver no formato correto, a função retorna '00:00:00'.
      - Pode ser que a string passada tenha horas, minutos ou segundos com apenas um dígito, nesse caso a função completa com um zero a esquerda.
    */
  if (!time) {
    return null;
  }

  let [hours, minutes] = time.split(':').map((unit) => unit.padStart(2, '0'));

  if (!hours && !minutes) {
    return '00:00:00';
  }

  if (!hours) {
    hours = '00';
  }

  if (!minutes) {
    minutes = '00';
  }

  return `${hours}:${minutes}:00`;
}

async function horarioAtual() {
  const [horario] = await dbQuery(
    `SELECT DATE_FORMAT(CURRENT_TIMESTAMP, '%Y-%m-%d %H:%i:00') as horario`
  );

  return horario.horario;
}

const calculaHorasRealizadas = (dataInicio, dataFim) => {
  var inicio = new Date(dataInicio);
  var fim = new Date(dataFim);

  var diferencaEmMilissegundos = fim.getTime() - inicio.getTime();

  var diferencaEmHoras = diferencaEmMilissegundos / (1000 * 60 * 60);
  var horas = Math.floor(diferencaEmHoras);
  var minutos = Math.round((diferencaEmHoras % 1) * 60);

  var horasFormatadas = horas < 10 ? '0' + horas : horas;
  var minutosFormatados = minutos < 10 ? '0' + minutos : minutos;

  return horasFormatadas + ':' + minutosFormatados + ':00';
};

/**
 * @param {number} nrPlantao
 * @param { 'Aberto' | 'Solicitado' | 'Aprovado' | 'Executado' | 'AprovExecucao' | 'Antecipado' | 'Concluido' } situacao
 * @returns
 */
const plantaoPorSituacao = async (nrPlantao, situacao) => {
  const [plantao] = await dbQuery(
    /*sql*/ `
            SELECT * FROM capInstSaudePlantao cisp 
            WHERE opNrPlantao = ?
            AND opSituacao = ?
        `,
    [nrPlantao, situacao]
  );

  return plantao;
};

async function buscaTotalHorasTotalValor(event) {
  const [{ totalHoras, valorTotal }] = await dbQuery(
    /*sql*/ `
        SELECT 
            SEC_TO_TIME(SUM(TIME_TO_SEC(c.ocQtAprovadas))) AS totalHoras,
            SUM(
                CASE 
                    WHEN pa.agTipoValor = 0 THEN 
                        CASE c.ceTipoPagamento
                            WHEN 'Hora' THEN TIME_TO_SEC(c.ocQtAprovadas)/3600 * p.opValorHora
                            WHEN 'Fixo' THEN TIME_TO_SEC(c.ocQtAprovadas)/3600 * p.opValorFixo
                            WHEN 'Unitario' THEN TIME_TO_SEC(c.ocQtAprovadas)/3600 * p.opValorUnit
                            ELSE 0
                        END
                    WHEN pa.agTipoValor = 1 THEN TIME_TO_SEC(c.ocQtAprovadas)/3600 * p.opValorExtra1
                    WHEN pa.agTipoValor = 2 THEN TIME_TO_SEC(c.ocQtAprovadas)/3600 * p.opValorExtra2
                    ELSE 0
                END
            ) as valorTotal
        FROM capOperPlantaoCheck c
        INNER JOIN capInstSaudePlantaoAgenda pa ON 
            c.agData = pa.agData AND
            c.isInstSaude = pa.isInstSaude AND
            c.laNome = pa.laNome AND
            c.esEspecialidade = pa.esEspecialidade AND
            c.ocNrContrato = pa.ocNrContrato AND
            c.clCliente = pa.clCliente AND
            c.ceTipoPagamento = pa.ceTipoPagamento AND
            c.opNrPlantao = pa.opNrPlantao
        INNER JOIN capInstSaudePlantao p ON 
            pa.isInstSaude = p.isInstSaude AND
            pa.laNome = p.laNome AND
            pa.esEspecialidade = p.esEspecialidade AND
            pa.ocNrContrato = p.ocNrContrato AND
            pa.clCliente = p.clCliente AND
            pa.ceTipoPagamento = p.ceTipoPagamento AND
            pa.opNrPlantao = p.opNrPlantao
        WHERE c.isInstSaude = ?
        AND c.laNome = ?
        AND c.esEspecialidade = ?
        AND c.ocNrContrato = ?
        AND c.clCliente = ?
        AND c.opNrPlantao = ?
        AND c.codFechamento = ?
       `,
    [
      event.isInstSaude,
      event.laNome,
      event.esEspecialidade,
      event.ocNrContrato,
      event.clCliente,
      event.opNrPlantao,
      event.codFechamento,
    ]
  );

  return { totalHoras, valorTotal };
}

/**
 * @param {{
 *    psCPF: string
 *    codFechamento: number,
 *    isInstSaude: string,
 *    laNome: string,
 *    esEspecialidade: string,
 *    ocNrContrato: string,
 *    clCliente: string,
 *    opNrPlantao: string,
 *    ceTipoPagamento: string
 * }} event
 */
async function calculaValorFechamento(event) {
  if (
    checkData(event, {
      obrigatorios: [
        'psCPF',
        'codFechamento',
        'isInstSaude',
        'laNome',
        'esEspecialidade',
        'ocNrContrato',
        'clCliente',
        'opNrPlantao',
        'ceTipoPagamento',
      ],
    })
  ) {
    throw new Error('Dados insuficentes.');
  }

  /*
        SELECT 
            c.*,
            CASE 
                WHEN c.agTipoValor = 0 THEN 
                    CASE c.ceTipoPagamento
                        WHEN 'Hora' THEN TIME_TO_SEC(c.ocQtAprovadas)/3600 * p.opValorHora
                        WHEN 'Fixo' THEN p.opValorFixo
                        WHEN 'Unitario' THEN p.opValorUnit
                        ELSE 0
                    END
                WHEN c.agTipoValor = 1 THEN TIME_TO_SEC(c.ocQtAprovadas)/3600 * p.opValorExtra1
                WHEN c.agTipoValor = 2 THEN TIME_TO_SEC(c.ocQtAprovadas)/3600 * p.opValorExtra2
                ELSE 0
            END as valor_total
        FROM capOperPlantaoCheck c
        INNER JOIN capInstSaudePlantao p ON 
            c.isInstSaude = p.isInstSaude AND
            c.laNome = p.laNome AND
            c.esEspecialidade = p.esEspecialidade AND
            c.ocNrContrato = p.ocNrContrato AND
            c.clCliente = p.clCliente AND
            c.ceTipoPagamento = p.ceTipoPagamento AND
            c.opNrPlantao = p.opNrPlantao;
    */
  const { totalHoras, valorTotal } = await buscaTotalHorasTotalValor(event);

  console.log(
    `Atualizando fechamento ${event.codFechamento}, com totalHoras ${totalHoras} e valorTotal ${valorTotal}`
  );

  await update(
    'capAtendimentoFechamento',
    {
      afValor: valorTotal,
    },
    {
      isInstSaude: event.isInstSaude,
      laNome: event.laNome,
      esEspecialidade: event.esEspecialidade,
      ocNrContrato: event.ocNrContrato,
      clCliente: event.clCliente,
      opNrPlantao: event.opNrPlantao,
      codFechamento: event.codFechamento,
    },
    event
  );
}

async function buscaAtendimento(event) {
  const [result] = await dbQuery(
    /*sql*/ `
            SELECT * from capInstSaudePlantao 
            WHERE isInstSaude = ?
            AND laNome = ?
            AND esEspecialidade = ?
            AND ocNrContrato = ?
            AND clCliente = ?
            AND opNrPlantao = ?
        `,
    [
      event.isInstSaude,
      event.laNome,
      event.esEspecialidade,
      event.ocNrContrato,
      event.clCliente,
      event.opNrPlantao,
    ]
  );

  return result;
}

const pegaCheckoutMaisRecente = (checks) => {
  return checks.reduce((acc, current) => {
    return new Date(current.ocCheckOut).getTime() > new Date(acc.ocCheckOut).getTime()
      ? current
      : acc;
  });
};

const pegaPrimeiroCheckin = (checks) => {
  if (checks.length === 1) {
    return checks[0];
  }

  return checks.reduce((acc, current) => {
    return new Date(current.ocCheckIn).getTime() < new Date(acc.ocCheckIn).getTime()
      ? current
      : acc;
  });
};

async function enviarCheckAprovadosParaFechamento(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'isInstSaude',
          'laNome',
          'esEspecialidade',
          'ocNrContrato',
          'clCliente',
          'opNrPlantao',
          'ceTipoPagamento',
          'checks',
        ],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    const atendimento = await buscaAtendimento(event);

    if (atendimento.opSituacao !== 'EmExecucao') {
      return baseResponse.badRequest(
        'Não é possivel realizar o fechamento de atendimentos que não estão em execução.'
      );
    }

    const checks = event.checks.filter((check) => !check.codFechamento);

    if (!checks.length) {
      return baseResponse.badRequest('Nenhum checkin/checkout encontrado para o fechamento.');
    }

    const primeiroCheckin = pegaPrimeiroCheckin(checks);
    const checkoutMaisRecente = pegaCheckoutMaisRecente(checks);

    const dataInicial = formataHora(primeiroCheckin.ocCheckIn);
    const dataFinal = formataHora(checkoutMaisRecente.ocCheckOut);

    const {
      results: { insertId: capCodFechamentoId },
    } = await insert('capCodFechamento', {}, event);

    for (let i = 0; i < checks.length; i++) {
      const checksAtualizado = await update(
        'capOperPlantaoCheck',
        {
          codFechamento: capCodFechamentoId,
          ocUsuarioAprovacao: event.body.user,
          ocSituacao: 'EnviadoAprovacao',
        },
        {
          ocCheckOut: formataHora(checks[i].ocCheckOut),
          isInstSaude: checks[i].isInstSaude,
          laNome: checks[i].laNome,
          esEspecialidade: checks[i].esEspecialidade,
          ocNrContrato: checks[i].ocNrContrato,
          clCliente: checks[i].clCliente,
          opNrPlantao: event.opNrPlantao,
          ceTipoPagamento: checks[i].ceTipoPagamento,
        },
        event
      );

      console.log(`Checkin atualizado com codFechamento: ${capCodFechamentoId}`, checksAtualizado);
    }

    const [aditivo] = await dbQuery(
      /*sql*/ `
            select * from capAditivoProfSaude
            where isInstSaude = ?
            and laNome = ?
            and esEspecialidade = ?
            and ocNrContrato = ?
            and clCliente = ?
            and opNrPlantao = ?
        `,
      [
        event.isInstSaude,
        event.laNome,
        event.esEspecialidade,
        event.ocNrContrato,
        event.clCliente,
        event.opNrPlantao,
      ]
    );

    const { totalHoras, valorTotal } = await buscaTotalHorasTotalValor({
      ...event,
      codFechamento: capCodFechamentoId,
    });

    const entityFechamento = {
      isInstSaude: event.isInstSaude,
      laNome: event.laNome,
      esEspecialidade: event.esEspecialidade,
      ocNrContrato: event.ocNrContrato,
      clCliente: event.clCliente,
      opNrPlantao: event.opNrPlantao,
      afNome: `Fechamento ${capCodFechamentoId} - Plantão: ${event.opNrPlantao}`,
      afIni: dataInicial,
      afFim: dataFinal,
      codFechamento: capCodFechamentoId,
      adSituacao: 'aguardandoAprovacao',
      ceTipoPagamento: event.ceTipoPagamento,
      psCPF: event.psCPF,
      adDataSolicitacao: aditivo.adDataSolicitacao,
      afValor: valorTotal,
    };

    console.log(
      `Atualizando fechamento ${capCodFechamentoId}, com totalHoras ${totalHoras} e valorTotal ${valorTotal}`
    );

    await insert('capAtendimentoFechamento', entityFechamento, event);

    await insert(
      'capAtendimentoFechaAditivo',
      {
        // adValorFixo: '',
        // adValor: '',
        // adValorLiquido: '',
        // adValorBruto: '',

        isInstSaude: entityFechamento.isInstSaude,
        laNome: entityFechamento.laNome,
        esEspecialidade: entityFechamento.esEspecialidade,
        ocNrContrato: entityFechamento.ocNrContrato,
        clCliente: entityFechamento.clCliente,
        opNrPlantao: entityFechamento.opNrPlantao,
        ceTipoPagamento: entityFechamento.ceTipoPagamento,
        psCPF: entityFechamento.psCPF,
        adDataSolicitacao: entityFechamento.adDataSolicitacao,
        afNome: entityFechamento.afNome,
        afIni: entityFechamento.afIni,
      },
      event
    );

    return baseResponse.created('Criado com sucesso');
  } catch (err) {
    console.error(err);

    return baseResponse.error('Error ao processar requisição');
  }
}

const timeToMinutes = (timeStr) => {
  if (
    !timeStr ||
    !timeStr.includes(':') ||
    timeStr.split(':').length !== 2 ||
    timeStr.split(':').some((part) => isNaN(Number(part)))
  ) {
    return 0;
  }

  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
};

const getNextDay = (dateStr) => {
  const date = new Date(dateStr);
  date.setDate(date.getDate() + 1);
  return date.toISOString().split('T')[0];
};

/**
 *
 * @param {string} cliente
 * @param {'Diario' | 'Semanal' | 'Mensal' | 'Manual' | '*'} tipoFechamento
 * @returns
 */
const getAtendimentos = async (cliente, tipoFechamento, page = 0, limit = 100) => {
  let tipoFechamentoQuery = '';

  if (tipoFechamento === '*') {
    tipoFechamentoQuery = /*sql*/ `
            AND (
                (atendimentos.opTipoFechamento = 'Manual' AND DATE(agendas.agData) <= CURDATE())
                OR 
                (atendimentos.opTipoFechamento = 'Diario' AND DATE(agendas.agData) < CURDATE())
                OR 
                (atendimentos.opTipoFechamento = 'Semanal' 
                    AND YEARWEEK(agendas.agData, 1) < YEARWEEK(CURDATE(), 1)
                    AND DATE(agendas.agData) BETWEEN atendimentos.opPeriodoIni AND atendimentos.opPeriodoFim
                )
                OR
                (atendimentos.opTipoFechamento = 'Mensal' 
                    AND DATE_FORMAT(agendas.agData, '%Y-%m') < DATE_FORMAT(CURDATE(), '%Y-%m')
                    AND DATE(agendas.agData) BETWEEN atendimentos.opPeriodoIni AND atendimentos.opPeriodoFim
                )
            )
        `;
  }

  if (tipoFechamento === 'Diario') {
    tipoFechamentoQuery = /*sql*/ `
            AND atendimentos.opTipoFechamento = 'Diario'
            AND DATE(agendas.agData) < CURDATE()
        `;
  }

  if (tipoFechamento === 'Semanal') {
    tipoFechamentoQuery = /*sql*/ `
            AND atendimentos.opTipoFechamento = 'Semanal'
            AND YEARWEEK(agendas.agData, 1) < YEARWEEK(CURDATE(), 1)
            AND DATE(agendas.agData) BETWEEN atendimentos.opPeriodoIni AND atendimentos.opPeriodoFim
        `;
  }

  if (tipoFechamento === 'Mensal') {
    tipoFechamentoQuery = /*sql*/ `
            AND atendimentos.opTipoFechamento = 'Mensal'
            AND DATE_FORMAT(agendas.agData, '%Y-%m') < DATE_FORMAT(CURDATE(), '%Y-%m')
            AND DATE(agendas.agData) BETWEEN atendimentos.opPeriodoIni AND atendimentos.opPeriodoFim
        `;
  }

  if (tipoFechamento === 'Manual') {
    tipoFechamentoQuery = /*sql*/ `
            AND atendimentos.opTipoFechamento = 'Manual'
            AND DATE(agendas.agData) <= CURDATE()
        `;
  }

  const sql = /*sql*/ `
            SELECT 
                atendimentos.isInstSaude,
                atendimentos.laNome,
                atendimentos.esEspecialidade,
                atendimentos.ocNrContrato,
                atendimentos.clCliente,
                atendimentos.ceTipoPagamento,
                atendimentos.opNrPlantao,
                atendimentos.usCPFUsuarioAprovador,
                atendimentos.opDataDivulgacao,
                atendimentos.opAtivo,
                atendimentos.opTipoFechamento,
                atendimentos.opDataFechamento,
                atendimentos.opPeriodoIni,
                atendimentos.opPeriodoFim,
                atendimentos.psCPF,
                atendimentos.opDatarecebimento,
                atendimentos.opDataAntecipacao,
                atendimentos.opQtdHorasRequisitada,
                atendimentos.opQtdHorasRealizadas,
                atendimentos.opValorFixo,
                atendimentos.opValorHora,
                atendimentos.opValorUnit,
                atendimentos.opChaveAcesso,
                atendimentos.opSituacao,
                atendimentos.dtInclusao,
                atendimentos.dtModificacao,
                atendimentos.opDiaFechamento
            FROM
                capInstSaudePlantao as atendimentos
            JOIN 
                capInstSaudePlantaoAgenda as agendas ON 
                atendimentos.laNome = agendas.laNome
                AND atendimentos.opNrPlantao = agendas.opNrPlantao
                AND atendimentos.ceTipoPagamento = agendas.ceTipoPagamento
                AND atendimentos.clCliente = agendas.clCliente
                AND atendimentos.ocNrContrato = agendas.ocNrContrato
                AND atendimentos.esEspecialidade = agendas.esEspecialidade
                AND atendimentos.isInstSaude = agendas.isInstSaude
            LEFT JOIN 
                (
                    SELECT 
                        checks.isInstSaude,
                        checks.laNome,
                        checks.esEspecialidade,
                        checks.ocNrContrato,
                        checks.clCliente,
                        checks.ceTipoPagamento,
                        checks.opNrPlantao,
                        checks.agData,
                        SEC_TO_TIME(SUM(TIME_TO_SEC(TIMEDIFF(checks.ocCheckOut, checks.ocCheckIn)))) as totalHorasTrabalhadas
                    FROM 
                        capOperPlantaoCheck checks
                    GROUP BY 
                        checks.isInstSaude,
                        checks.laNome,
                        checks.esEspecialidade,
                        checks.ocNrContrato,
                        checks.clCliente,
                        checks.ceTipoPagamento,
                        checks.opNrPlantao,
                        checks.agData
                ) as totalCheckInOut ON (
                    totalCheckInOut.isInstSaude = agendas.isInstSaude
                    AND totalCheckInOut.laNome = agendas.laNome
                    AND totalCheckInOut.opNrPlantao = agendas.opNrPlantao
                    AND totalCheckInOut.ceTipoPagamento = agendas.ceTipoPagamento
                    AND totalCheckInOut.clCliente = agendas.clCliente
                    AND totalCheckInOut.ocNrContrato = agendas.ocNrContrato
                    AND totalCheckInOut.esEspecialidade = agendas.esEspecialidade
                    AND DATE(totalCheckInOut.agData) = DATE(agendas.agData)
                )
            WHERE
                atendimentos.clCliente = ?
                AND atendimentos.opAtivo = 1
                AND atendimentos.opSituacao IN ('EmExecucao', 'AguardExecucao')
                AND (
                    totalCheckInOut.totalHorasTrabalhadas IS NULL 
                    OR TIME_TO_SEC(totalCheckInOut.totalHorasTrabalhadas) < 
                    ( TIMESTAMPDIFF(SECOND, agDataIni, agDataFim)
                        - TIME_TO_SEC(COALESCE(agIntervalo, '00:00:00')) )
                )
                AND atendimentos.opNrPlantao > ?
                ${tipoFechamentoQuery}
            GROUP BY 
                atendimentos.opNrPlantao
            ORDER BY 
                atendimentos.opDataDivulgacao ASC
            LIMIT ?;
        `;

  return await dbQuery(sql, [page, cliente, tipoFechamento, limit]);
};

/**
 * Calcula as horas realizadas e aprovadas entre check-in e check-out usando SQL
 * @param {Date} ocCheckIn - Data e hora do check-in
 * @param {Date} ocCheckOut - Data e hora do check-out
 * @param {string} ocQtGlosadas - Horas glosadas no formato HH:MM:SS
 * @param {string} ocIntervalo - Tempo de intervalo no formato HH:MM
 * @returns {Promise<{qtRealizadas: string, qtAprovadas: string}>}
 */
const calcularQtRealizadasEaprovadas = async (
  ocCheckIn,
  ocCheckOut,
  ocQtGlosadas = '00:00:00',
  ocIntervalo = null
) => {
  // Calcular tudo usando SQL diretamente
  const result = await dbQuery(
    /*sql*/ `
        SELECT
            -- Calcular segundos entre check-in e check-out
            TIMESTAMPDIFF(SECOND, ?, ?) AS total_segundos,
            
            -- Calcular segundos de intervalo (se existir)
            IF(? IS NOT NULL, 
               TIME_TO_SEC(?), 
               0
            ) AS segundos_intervalo,
            
            -- Calcular segundos glosados
            TIME_TO_SEC(?) AS segundos_glosados,
            
            -- Calcular segundos realizados (total - intervalo)
            GREATEST(0, TIMESTAMPDIFF(SECOND, ?, ?) - IF(? IS NOT NULL, TIME_TO_SEC(?), 0)) AS segundos_realizados,
            
            -- Formatar tempo realizado como HH:MM:SS
            SEC_TO_TIME(
                GREATEST(0, TIMESTAMPDIFF(SECOND, ?, ?) - IF(? IS NOT NULL, TIME_TO_SEC(?), 0))
            ) AS qt_realizadas,
            
            -- Formatar tempo aprovado como HH:MM:SS
            SEC_TO_TIME(
                GREATEST(0, 
                    TIMESTAMPDIFF(SECOND, ?, ?) - 
                    IF(? IS NOT NULL, TIME_TO_SEC(?), 0) - 
                    TIME_TO_SEC(?)
                )
            ) AS qt_aprovadas
    `,
    [
      ocCheckIn,
      ocCheckOut, // Para total_segundos
      ocIntervalo,
      ocIntervalo, // Para segundos_intervalo
      ocQtGlosadas, // Para segundos_glosados
      ocCheckIn,
      ocCheckOut,
      ocIntervalo,
      ocIntervalo, // Para segundos_realizados
      ocCheckIn,
      ocCheckOut,
      ocIntervalo,
      ocIntervalo, // Para qt_realizadas
      ocCheckIn,
      ocCheckOut,
      ocIntervalo,
      ocIntervalo,
      ocQtGlosadas, // Para qt_aprovadas
    ]
  );

  // Verificar se temos resultados
  if (!result || !result[0]) {
    throw new Error('Falha ao calcular horas realizadas e aprovadas');
  }

  // Validar se horas glosadas não são maiores que realizadas
  if (result[0].segundos_glosados > result[0].segundos_realizados) {
    throw new Error('ocQtGlosadas não pode ser maior que qtRealizadas');
  }

  // Retornar resultado formatado
  // O SQL já formatou para HH:MM:SS, mas podemos garantir o formato se necessário
  const qtRealizadas = result[0].qt_realizadas;
  const qtAprovadas = result[0].qt_aprovadas;

  return { qtRealizadas, qtAprovadas };
};

function capHandler(actions = {}) {
  return async (event) => {
    try {
      const userFromToken = await getUserFromToken(event.headers?.Authorization);

      if (!userFromToken) {
        return baseResponse.unauthorized('Token de sessão expirado');
      }

      if (!(event.body?.method in actions || event.method in actions)) {
        return baseResponse.notFound('Método não encontrado');
      }

      return {
        newToken: userFromToken?.body?.newToken,
        ...(await actions[event.body?.method || event.method]({
          ...(event.body || event),
          ...userFromToken,
        })),
      };
    } catch (error) {
      console.log('Erro ao processar a requisição', error);
      return baseResponse.error('Erro ao processar a requisição');
    }
  };
}

export * from './types.mjs';

export {
  connection,
  createResponse,
  createToken,
  checkToken,
  verifyToken,
  getUserFromToken,
  checkData,
  sendToLog,
  deleteEntity,
  paginationList,
  execQuery,
  insert,
  update,
  find,
  lambdaInvoke,
  baseResponse,
  dbQuery,
  onlyNumber,
  isEmailValid,
  UserGroupTypes,
  formataHora,
  horarioAtual,
  calculaHorasRealizadas,
  plantaoPorSituacao,
  list,
  calculaValorFechamento,
  calcularQtRealizadasEaprovadas,
  buscaAtendimento,
  enviarCheckAprovadosParaFechamento,
  timeToMinutes,
  getNextDay,
  buscaTotalHorasTotalValor,
  getAtendimentos,
  capHandler,
};
