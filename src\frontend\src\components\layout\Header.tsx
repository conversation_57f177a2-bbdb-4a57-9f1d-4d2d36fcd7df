// =====================================================
// HEADER COMPONENT - COMPONENTE DE CABEÇALHO
// Barra superior com navegação e controles
// =====================================================

import React from 'react'
import { motion } from 'framer-motion'
import { useTranslation } from 'react-i18next'
import {
  Menu,
  Bell,
  Search,
  Sun,
  Moon,
  Monitor,
  Globe,
  User,
  Settings,
  LogOut,
  ChevronDown
} from 'lucide-react'
import { cn } from '@/utils'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { useAuth } from '@/hooks/useAuth'
import { useTheme } from '@/stores/themeStore'
import { changeLanguage, getAvailableLanguages, getCurrentLanguage } from '@/locales/i18n'

interface HeaderProps {
  onMenuToggle: () => void
  isSidebarOpen: boolean
}

export function Header({ onMenuToggle, isSidebarOpen }: HeaderProps) {
  const { t } = useTranslation()
  const { user, logout } = useAuth()
  const { theme, currentTheme, setTheme, toggleTheme } = useTheme()
  
  const [showUserMenu, setShowUserMenu] = React.useState(false)
  const [showLanguageMenu, setShowLanguageMenu] = React.useState(false)
  const [showThemeMenu, setShowThemeMenu] = React.useState(false)
  const [searchQuery, setSearchQuery] = React.useState('')

  const userMenuRef = React.useRef<HTMLDivElement>(null)
  const languageMenuRef = React.useRef<HTMLDivElement>(null)
  const themeMenuRef = React.useRef<HTMLDivElement>(null)

  const availableLanguages = getAvailableLanguages()
  const currentLanguage = getCurrentLanguage()

  // Fechar menus ao clicar fora
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }
      if (languageMenuRef.current && !languageMenuRef.current.contains(event.target as Node)) {
        setShowLanguageMenu(false)
      }
      if (themeMenuRef.current && !themeMenuRef.current.contains(event.target as Node)) {
        setShowThemeMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLanguageChange = (languageCode: string) => {
    changeLanguage(languageCode)
    setShowLanguageMenu(false)
  }

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme)
    setShowThemeMenu(false)
  }

  const handleLogout = () => {
    logout()
    setShowUserMenu(false)
  }

  const themeIcons = {
    light: Sun,
    dark: Moon,
    system: Monitor
  }

  const ThemeIcon = themeIcons[theme]

  return (
    <header className={cn(
      'sticky top-0 z-40 w-full bg-white/95 backdrop-blur-sm border-b border-secondary-200',
      'dark:bg-secondary-900/95 dark:border-secondary-700',
      'transition-all duration-200'
    )}>
      <div className="flex items-center justify-between h-16 px-4 lg:px-6">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          {/* Menu Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onMenuToggle}
            className="lg:hidden"
          >
            <Menu className="w-5 h-5" />
          </Button>

          {/* Search */}
          <div className="hidden md:block w-80">
            <Input
              type="text"
              placeholder={t('common.search')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<Search className="w-4 h-4" />}
              size="sm"
            />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-2">
          {/* Search Mobile */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
          >
            <Search className="w-5 h-5" />
          </Button>

          {/* Notifications */}
          <Button
            variant="ghost"
            size="sm"
            className="relative"
          >
            <Bell className="w-5 h-5" />
            <span className="absolute -top-1 -right-1 w-3 h-3 bg-error-500 rounded-full text-xs flex items-center justify-center">
              <span className="w-1.5 h-1.5 bg-white rounded-full" />
            </span>
          </Button>

          {/* Theme Selector */}
          <div className="relative" ref={themeMenuRef}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowThemeMenu(!showThemeMenu)}
            >
              <ThemeIcon className="w-5 h-5" />
            </Button>

            {showThemeMenu && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="absolute right-0 mt-2 w-48 bg-white dark:bg-secondary-800 rounded-lg shadow-strong border border-secondary-200 dark:border-secondary-700 py-1 z-50"
              >
                {[
                  { key: 'light', label: t('common.light'), icon: Sun },
                  { key: 'dark', label: t('common.dark'), icon: Moon },
                  { key: 'system', label: t('common.system'), icon: Monitor }
                ].map(({ key, label, icon: Icon }) => (
                  <button
                    key={key}
                    onClick={() => handleThemeChange(key as any)}
                    className={cn(
                      'w-full flex items-center space-x-3 px-4 py-2 text-sm transition-colors',
                      'hover:bg-secondary-100 dark:hover:bg-secondary-700',
                      theme === key && 'bg-primary-50 text-primary-700 dark:bg-primary-900/20 dark:text-primary-300'
                    )}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{label}</span>
                  </button>
                ))}
              </motion.div>
            )}
          </div>

          {/* Language Selector */}
          <div className="relative" ref={languageMenuRef}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowLanguageMenu(!showLanguageMenu)}
            >
              <Globe className="w-5 h-5" />
            </Button>

            {showLanguageMenu && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="absolute right-0 mt-2 w-56 bg-white dark:bg-secondary-800 rounded-lg shadow-strong border border-secondary-200 dark:border-secondary-700 py-1 z-50"
              >
                {availableLanguages.map((language) => (
                  <button
                    key={language.code}
                    onClick={() => handleLanguageChange(language.code)}
                    className={cn(
                      'w-full flex items-center space-x-3 px-4 py-2 text-sm transition-colors',
                      'hover:bg-secondary-100 dark:hover:bg-secondary-700',
                      currentLanguage === language.code && 'bg-primary-50 text-primary-700 dark:bg-primary-900/20 dark:text-primary-300'
                    )}
                  >
                    <span className="text-lg">{language.flag}</span>
                    <span>{language.name}</span>
                  </button>
                ))}
              </motion.div>
            )}
          </div>

          {/* User Menu */}
          <div className="relative" ref={userMenuRef}>
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className={cn(
                'flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors',
                'hover:bg-secondary-100 dark:hover:bg-secondary-800',
                'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2'
              )}
            >
              <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-white" />
              </div>
              
              <div className="hidden md:block text-left">
                <p className="text-sm font-medium text-heading">
                  {user?.name || 'Usuário'}
                </p>
                <p className="text-xs text-muted">
                  {user?.groups?.[0]?.name || 'Usuário'}
                </p>
              </div>

              <ChevronDown className="w-4 h-4 text-muted" />
            </button>

            {showUserMenu && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="absolute right-0 mt-2 w-64 bg-white dark:bg-secondary-800 rounded-lg shadow-strong border border-secondary-200 dark:border-secondary-700 py-1 z-50"
              >
                {/* User Info */}
                <div className="px-4 py-3 border-b border-secondary-200 dark:border-secondary-700">
                  <p className="text-sm font-medium text-heading">
                    {user?.name}
                  </p>
                  <p className="text-xs text-muted">
                    {user?.email}
                  </p>
                  <p className="text-xs text-muted mt-1">
                    {user?.groups?.map(g => g.name).join(', ')}
                  </p>
                </div>

                {/* Menu Items */}
                <div className="py-1">
                  <button
                    onClick={() => setShowUserMenu(false)}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-body hover:bg-secondary-100 dark:hover:bg-secondary-700 transition-colors"
                  >
                    <User className="w-4 h-4" />
                    <span>{t('common.profile')}</span>
                  </button>

                  <button
                    onClick={() => setShowUserMenu(false)}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-body hover:bg-secondary-100 dark:hover:bg-secondary-700 transition-colors"
                  >
                    <Settings className="w-4 h-4" />
                    <span>{t('common.settings')}</span>
                  </button>

                  <div className="border-t border-secondary-200 dark:border-secondary-700 my-1" />

                  <button
                    onClick={handleLogout}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-error-600 dark:text-error-400 hover:bg-error-50 dark:hover:bg-error-900/20 transition-colors"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>{t('common.logout')}</span>
                  </button>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
