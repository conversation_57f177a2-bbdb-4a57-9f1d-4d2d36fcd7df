// =====================================================
// API SERVICE - SERVIÇO BASE DE API
// Cliente HTTP configurado com interceptors
// =====================================================

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import type { ApiResponse, ApiError } from '@/types'
import { useAuthStore } from '@/stores/authStore'
import toast from 'react-hot-toast'

// Configuração base da API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'

class ApiService {
  private client: AxiosInstance
  private token: string | null = null

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  /**
   * Configurar interceptors
   */
  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Adicionar token de autorização
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`
        }

        // Log da requisição em desenvolvimento
        if (import.meta.env.DEV) {
          console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, {
            data: config.data,
            params: config.params,
          })
        }

        return config
      },
      (error) => {
        console.error('❌ Request error:', error)
        return Promise.reject(error)
      }
    )

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        // Log da resposta em desenvolvimento
        if (import.meta.env.DEV) {
          console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url}`, {
            status: response.status,
            data: response.data,
          })
        }

        // Verificar se há novo token na resposta
        const newToken = response.headers['x-new-token']
        if (newToken) {
          this.setToken(newToken)
          useAuthStore.getState().updateUser({ token: newToken } as any)
        }

        return response
      },
      (error) => {
        const response = error.response

        // Log do erro em desenvolvimento
        if (import.meta.env.DEV) {
          console.error(`❌ ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
            status: response?.status,
            data: response?.data,
            message: error.message,
          })
        }

        // Tratar erros específicos
        if (response?.status === 401) {
          // Token inválido ou expirado
          this.handleUnauthorized()
        } else if (response?.status === 403) {
          // Sem permissão
          toast.error('Você não tem permissão para realizar esta ação')
        } else if (response?.status >= 500) {
          // Erro do servidor
          toast.error('Erro interno do servidor. Tente novamente mais tarde.')
        } else if (!response) {
          // Erro de rede
          toast.error('Erro de conexão. Verifique sua internet.')
        }

        return Promise.reject(error)
      }
    )
  }

  /**
   * Tratar erro de não autorizado
   */
  private handleUnauthorized() {
    this.clearToken()
    useAuthStore.getState().logout()
    toast.error('Sessão expirada. Faça login novamente.')
    
    // Redirecionar para login se não estiver na página de login
    if (window.location.pathname !== '/login') {
      window.location.href = '/login'
    }
  }

  /**
   * Definir token de autorização
   */
  setToken(token: string) {
    this.token = token
  }

  /**
   * Limpar token de autorização
   */
  clearToken() {
    this.token = null
  }

  /**
   * GET request
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.get<ApiResponse<T>>(url, config)
    return response.data
  }

  /**
   * POST request
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.post<ApiResponse<T>>(url, data, config)
    return response.data
  }

  /**
   * PUT request
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.put<ApiResponse<T>>(url, data, config)
    return response.data
  }

  /**
   * PATCH request
   */
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.patch<ApiResponse<T>>(url, data, config)
    return response.data
  }

  /**
   * DELETE request
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.delete<ApiResponse<T>>(url, config)
    return response.data
  }

  /**
   * Upload de arquivo
   */
  async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    const config: AxiosRequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    }

    const response = await this.client.post<ApiResponse<T>>(url, formData, config)
    return response.data
  }

  /**
   * Download de arquivo
   */
  async download(url: string, filename?: string): Promise<void> {
    const response = await this.client.get(url, {
      responseType: 'blob',
    })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    window.URL.revokeObjectURL(downloadUrl)
  }

  /**
   * Requisição para lambda (compatibilidade com sistema antigo)
   */
  async lambda<T = any>(lambdaName: string, data: any): Promise<ApiResponse<T>> {
    return this.post<T>(`/${lambdaName}`, data)
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.get('/health')
      return response.success
    } catch {
      return false
    }
  }
}

// Instância singleton
export const apiService = new ApiService()

// Hook para usar API service
export const useApi = () => {
  return {
    api: apiService,
    get: apiService.get.bind(apiService),
    post: apiService.post.bind(apiService),
    put: apiService.put.bind(apiService),
    patch: apiService.patch.bind(apiService),
    delete: apiService.delete.bind(apiService),
    upload: apiService.upload.bind(apiService),
    download: apiService.download.bind(apiService),
    lambda: apiService.lambda.bind(apiService),
  }
}
