-- =====================================================
-- DADOS INICIAIS - SISTEMA GS2 REFATORADO
-- Dados de referência e configurações iniciais
-- =====================================================

-- Grupos de usuários
INSERT INTO user_groups (uuid, name, description, type, permissions) VALUES
(UUID(), 'Master', 'Acesso total ao sistema', 'master', '{"all": true}'),
(UUID(), 'Administrador', 'Administrador da plataforma', 'admin', '{"users": true, "clients": true, "reports": true}'),
(UUID(), 'Profissional de Saúde', 'Profissional de saúde', 'profissional', '{"shifts": true, "advances": true, "profile": true}'),
(UUID(), 'Atendente', 'Atendente/Escalista', 'atendente', '{"shifts": true, "schedule": true}');

-- Especialidades médicas
INSERT INTO specialties (uuid, name, description) VALUES
(UUID(), 'Cardiologia', 'Especialidade médica que se dedica ao diagnóstico e tratamento das doenças do coração'),
(UUID(), 'Neurologia', 'Especialidade médica que trata dos distúrbios do sistema nervoso'),
(UUID(), 'Ortopedia', 'Especialidade médica que cuida do aparelho locomotor'),
(UUID(), 'Pediatria', 'Especialidade médica dedicada à assistência à criança e ao adolescente'),
(UUID(), 'Ginecologia', 'Especialidade médica que trata da saúde do aparelho reprodutor feminino'),
(UUID(), 'Urologia', 'Especialidade médica que trata do trato urinário de homens e mulheres'),
(UUID(), 'Dermatologia', 'Especialidade médica que se dedica ao diagnóstico e tratamento das doenças da pele'),
(UUID(), 'Oftalmologia', 'Especialidade médica que investiga e trata as doenças relacionadas aos olhos'),
(UUID(), 'Psiquiatria', 'Especialidade médica que lida com a prevenção, diagnóstico e tratamento de transtornos mentais'),
(UUID(), 'Anestesiologia', 'Especialidade médica que estuda e proporciona anestesia, analgesia e reanimação'),
(UUID(), 'Radiologia', 'Especialidade médica que utiliza radiações para diagnóstico e tratamento'),
(UUID(), 'Enfermagem', 'Profissão na área da saúde dedicada ao cuidado de indivíduos'),
(UUID(), 'Fisioterapia', 'Ciência da saúde que estuda o movimento humano'),
(UUID(), 'Nutrição', 'Ciência que estuda os alimentos e sua relação com a saúde'),
(UUID(), 'Psicologia', 'Ciência que estuda os processos mentais e o comportamento humano');

-- Conselhos de classe profissional
INSERT INTO professional_councils (uuid, code, name) VALUES
(UUID(), 'CRM', 'Conselho Regional de Medicina'),
(UUID(), 'COREN', 'Conselho Regional de Enfermagem'),
(UUID(), 'CREFITO', 'Conselho Regional de Fisioterapia e Terapia Ocupacional'),
(UUID(), 'CRN', 'Conselho Regional de Nutricionistas'),
(UUID(), 'CRP', 'Conselho Regional de Psicologia'),
(UUID(), 'CRF', 'Conselho Regional de Farmácia'),
(UUID(), 'CRO', 'Conselho Regional de Odontologia'),
(UUID(), 'CRMV', 'Conselho Regional de Medicina Veterinária');

-- Bancos principais
INSERT INTO banks (uuid, code, name) VALUES
(UUID(), '001', 'Banco do Brasil S.A.'),
(UUID(), '033', 'Banco Santander (Brasil) S.A.'),
(UUID(), '104', 'Caixa Econômica Federal'),
(UUID(), '237', 'Banco Bradesco S.A.'),
(UUID(), '341', 'Itaú Unibanco S.A.'),
(UUID(), '260', 'Nu Pagamentos S.A. (Nubank)'),
(UUID(), '077', 'Banco Inter S.A.'),
(UUID(), '212', 'Banco Original S.A.'),
(UUID(), '290', 'PagSeguro PagBank'),
(UUID(), '323', 'Mercado Pago'),
(UUID(), '336', 'Banco C6 S.A.'),
(UUID(), '364', 'Gerencianet Pagamentos do Brasil');

-- Tipos de pagamento
INSERT INTO payment_types (uuid, name, type, description) VALUES
(UUID(), 'Pagamento por Hora', 'hora', 'Pagamento baseado na quantidade de horas trabalhadas'),
(UUID(), 'Pagamento Fixo', 'fixo', 'Valor fixo independente das horas trabalhadas'),
(UUID(), 'Pagamento Unitário', 'unitario', 'Pagamento por unidade de serviço prestado');

-- Tipos de PIX
INSERT INTO pix_types (uuid, code, name, description) VALUES
(UUID(), '1', 'Email', 'Chave PIX baseada em endereço de email'),
(UUID(), '2', 'CPF', 'Chave PIX baseada no CPF'),
(UUID(), '3', 'CNPJ', 'Chave PIX baseada no CNPJ'),
(UUID(), '4', 'Celular', 'Chave PIX baseada no número de celular'),
(UUID(), '5', 'Aleatória', 'Chave PIX aleatória gerada pelo banco');

-- Tipos de assinatura eletrônica
INSERT INTO electronic_signature_types (uuid, code, name, description) VALUES
(UUID(), '1', 'Assinatura Eletrônica', 'Assinatura eletrônica simples'),
(UUID(), '2', 'e-CNPJ', 'Certificado digital e-CNPJ'),
(UUID(), '3', 'e-CPF', 'Certificado digital e-CPF');

-- Justificativas para check-in/check-out
INSERT INTO check_justifications (uuid, name, description) VALUES
(UUID(), 'Glosada pela Instituição de Saúde', 'Horas glosadas pela instituição de saúde'),
(UUID(), 'Glosada pela quantidade contratada', 'Horas glosadas por exceder a quantidade contratada'),
(UUID(), 'Lançamento Horas a Maior', 'Lançamento de horas superior ao realizado'),
(UUID(), 'Lançamento Horas a Menor', 'Lançamento de horas inferior ao realizado'),
(UUID(), 'Intervalo não computado', 'Tempo de intervalo não computado nas horas trabalhadas'),
(UUID(), 'Atraso no check-in', 'Atraso no horário de entrada'),
(UUID(), 'Saída antecipada', 'Saída antes do horário previsto');

-- Templates de mensagens básicos
INSERT INTO message_templates (uuid, code, meta_model, title, event_type, message_content, frequency) VALUES
(UUID(), 'check2h001', 'check2h001', 'Lembrete Check-in 2h', 'check_reminder', 'Lembrete: Você tem um plantão em 2 horas. Não esqueça de fazer o check-in!', 'hora'),
(UUID(), 'checkin001', 'checkin001', 'Confirmação Check-in', 'check_in', 'Check-in realizado com sucesso para o plantão de {data} às {hora}.', 'unico'),
(UUID(), 'checkout001', 'checkout001', 'Confirmação Check-out', 'check_out', 'Check-out realizado com sucesso. Plantão finalizado às {hora}.', 'unico'),
(UUID(), 'plantao_aprovado', 'plantao_aprovado', 'Plantão Aprovado', 'shift_approved', 'Parabéns! Sua solicitação para o plantão de {data} foi aprovada.', 'unico'),
(UUID(), 'plantao_rejeitado', 'plantao_rejeitado', 'Plantão Rejeitado', 'shift_rejected', 'Sua solicitação para o plantão de {data} foi rejeitada.', 'unico'),
(UUID(), 'antecipacao_aprovada', 'antecipacao_aprovada', 'Antecipação Aprovada', 'advance_approved', 'Sua solicitação de antecipação no valor de R$ {valor} foi aprovada.', 'unico'),
(UUID(), 'antecipacao_rejeitada', 'antecipacao_rejeitada', 'Antecipação Rejeitada', 'advance_rejected', 'Sua solicitação de antecipação foi rejeitada. Motivo: {motivo}', 'unico');

-- Configurações do sistema
INSERT INTO system_configurations (uuid, config_key, config_value, description, data_type, is_public) VALUES
(UUID(), 'sistema.nome', 'GS2 - Gestão de Saúde', 'Nome do sistema', 'string', true),
(UUID(), 'sistema.versao', '2.0.0', 'Versão atual do sistema', 'string', true),
(UUID(), 'jwt.expiration', '24', 'Tempo de expiração do JWT em horas', 'number', false),
(UUID(), 'antecipacao.taxa_minima', '0.01', 'Taxa mínima de antecipação (1%)', 'number', false),
(UUID(), 'antecipacao.taxa_maxima', '0.15', 'Taxa máxima de antecipação (15%)', 'number', false),
(UUID(), 'plantao.horas_minimas', '4', 'Quantidade mínima de horas por plantão', 'number', false),
(UUID(), 'plantao.horas_maximas', '24', 'Quantidade máxima de horas por plantão', 'number', false),
(UUID(), 'whatsapp.token_verificacao', 'gs2_webhook_token', 'Token de verificação do webhook WhatsApp', 'string', false),
(UUID(), 'email.smtp_host', 'smtp.gmail.com', 'Servidor SMTP para envio de emails', 'string', false),
(UUID(), 'email.smtp_port', '587', 'Porta do servidor SMTP', 'number', false);

-- Usuário administrador padrão (senha: admin123)
INSERT INTO users (uuid, cpf, email, password_hash, name, phone, active, lgpd_accepted) VALUES
(UUID(), '00000000000', '<EMAIL>', '$2a$10$1CVwKyv7NkGaZ1Vw.kHpduH2f7ypZtLVBYboPzBqWeAooe83R2BFS', 'Administrador do Sistema', '11999999999', true, true);

-- Associar usuário administrador ao grupo Master
INSERT INTO user_group_memberships (uuid, user_id, group_id, active) 
SELECT UUID(), u.id, g.id, true 
FROM users u, user_groups g 
WHERE u.email = '<EMAIL>' AND g.name = 'Master';

-- Inicializar sequências
INSERT INTO protocol_numbers (uuid, protocol_number) VALUES (UUID(), 1);
INSERT INTO closing_codes (uuid, closing_code) VALUES (UUID(), 1);

-- Comentários sobre a migração
-- =====================================================
-- INSTRUÇÕES PARA MIGRAÇÃO DOS DADOS EXISTENTES
-- =====================================================
/*
Para migrar os dados do schema antigo para o novo:

1. USUÁRIOS:
   - Migrar capUsuario -> users
   - Criar registros em health_professionals para profissionais de saúde
   - Migrar endereços para user_addresses

2. CLIENTES:
   - Migrar capCliente -> clients
   - Migrar endereços para client_addresses
   - Migrar capClienteUsuario -> client_users

3. INSTITUIÇÕES:
   - Migrar capInstSaude -> health_institutions
   - Migrar capInstSaudeLocalAtendimento -> service_locations
   - Migrar capInstSaudeUsuario -> health_institution_users

4. CONTRATOS:
   - Migrar capInstSaudeContrato -> contracts
   - Migrar capInstSaudeContratoEspec -> contract_specialties

5. PLANTÕES:
   - Migrar capInstSaudePlantao -> shifts
   - Migrar capInstSaudePlantaoAgenda -> shift_schedules
   - Migrar capInstSaudePlantaoSolicitacao -> shift_requests

6. CHECK-IN/CHECK-OUT:
   - Migrar capOperPlantaoCheck -> shift_checks

7. ANTECIPAÇÕES:
   - Migrar capAntecipacao -> advances
   - Migrar capAntecipacaoHistorico -> advance_status_history

8. MENSAGERIA:
   - Migrar capMensageria -> message_templates
   - Migrar capMensagemWhatsapp -> whatsapp_messages

IMPORTANTE: 
- Todos os IDs antigos devem ser preservados em campos de mapeamento temporários
- Executar validações de integridade após a migração
- Fazer backup completo antes de iniciar a migração
- Testar em ambiente de desenvolvimento primeiro
*/
