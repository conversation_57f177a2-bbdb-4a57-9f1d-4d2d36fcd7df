// =====================================================
// PROTECTED ROUTE - ROTA PROTEGIDA
// Componente para proteger rotas que requerem autenticação
// =====================================================

import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Shield, AlertTriangle } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useAuth, usePermissions } from '@/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredPermission?: string
  requiredGroup?: string
  requiredGroups?: string[]
  fallback?: React.ReactNode
}

export function ProtectedRoute({
  children,
  requiredPermission,
  requiredGroup,
  requiredGroups,
  fallback
}: ProtectedRouteProps) {
  const { t } = useTranslation()
  const location = useLocation()
  const { isAuthenticated, isLoading, user, logout } = useAuth()
  const { hasPermission, hasGroup, hasAnyGroup } = usePermissions()

  // Mostrar loading enquanto verifica autenticação
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-secondary-50 dark:bg-secondary-900">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <LoadingSpinner size="lg" className="mx-auto mb-4" />
          <p className="text-muted">{t('common.loading')}</p>
        </motion.div>
      </div>
    )
  }

  // Redirecionar para login se não autenticado
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // Verificar permissões específicas
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return fallback || <UnauthorizedAccess onLogout={logout} />
  }

  // Verificar grupo específico
  if (requiredGroup && !hasGroup(requiredGroup)) {
    return fallback || <UnauthorizedAccess onLogout={logout} />
  }

  // Verificar múltiplos grupos
  if (requiredGroups && !hasAnyGroup(requiredGroups)) {
    return fallback || <UnauthorizedAccess onLogout={logout} />
  }

  // Renderizar conteúdo protegido
  return <>{children}</>
}

// Componente para acesso não autorizado
function UnauthorizedAccess({ onLogout }: { onLogout: () => void }) {
  const { t } = useTranslation()

  return (
    <div className="min-h-screen flex items-center justify-center bg-secondary-50 dark:bg-secondary-900 p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="w-full max-w-md"
      >
        <Card className="shadow-strong">
          <CardHeader className="text-center pb-6">
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, duration: 0.3 }}
              className="w-16 h-16 bg-error-100 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <AlertTriangle className="w-8 h-8 text-error-600" />
            </motion.div>

            <CardTitle className="text-xl font-bold text-heading">
              {t('auth.unauthorized')}
            </CardTitle>
            <CardDescription className="text-muted">
              Você não tem permissão para acessar esta página. Entre em contato com o administrador se acredita que isso é um erro.
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-4">
            <div className="flex flex-col space-y-3">
              <Button
                variant="outline"
                onClick={() => window.history.back()}
                fullWidth
              >
                {t('common.back')}
              </Button>
              
              <Button
                variant="primary"
                onClick={onLogout}
                fullWidth
              >
                {t('auth.logout')}
              </Button>
            </div>

            <div className="pt-4 border-t border-secondary-200 dark:border-secondary-700">
              <div className="flex items-center justify-center space-x-2 text-sm text-muted">
                <Shield className="w-4 h-4" />
                <span>Acesso protegido pelo sistema de segurança</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}

// Hook para verificar se uma rota é acessível
export function useRouteAccess(
  requiredPermission?: string,
  requiredGroup?: string,
  requiredGroups?: string[]
) {
  const { isAuthenticated } = useAuth()
  const { hasPermission, hasGroup, hasAnyGroup } = usePermissions()

  if (!isAuthenticated) {
    return { hasAccess: false, reason: 'not_authenticated' }
  }

  if (requiredPermission && !hasPermission(requiredPermission)) {
    return { hasAccess: false, reason: 'insufficient_permission' }
  }

  if (requiredGroup && !hasGroup(requiredGroup)) {
    return { hasAccess: false, reason: 'insufficient_group' }
  }

  if (requiredGroups && !hasAnyGroup(requiredGroups)) {
    return { hasAccess: false, reason: 'insufficient_groups' }
  }

  return { hasAccess: true, reason: null }
}
