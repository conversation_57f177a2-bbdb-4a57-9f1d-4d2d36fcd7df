import { describe, test, expect, beforeEach, jest } from '@jest/globals';

// Mock mysql module
const mockConnection = {
    connect: jest.fn(),
    query: jest.fn(),
    end: jest.fn()
};

const mockCreateConnection = jest.fn(() => mockConnection);

jest.unstable_mockModule('mysql', () => ({
    createConnection: mockCreateConnection
}));

// Import Database after mocking mysql
const Database = (await import('../src/lambdas/gs2api/wpphook/database.mjs')).default;

describe('wpphook Database Operations', () => {
    let database;

    beforeEach(() => {
        jest.clearAllMocks();
        database = new Database();
        
        // Setup default successful connection mock
        mockConnection.connect.mockImplementation((callback) => {
            callback(null);
        });
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('Connection Management', () => {
        test('should initialize database connection successfully', async () => {
            await database.initialize();

            expect(mockCreateConnection).toHaveBeenCalledWith(
                expect.objectContaining({
                    host: process.env.MYSQL_HOST,
                    user: process.env.MYSQL_USER,
                    password: process.env.MYSQL_PASSWORD,
                    database: 'wpphook'
                })
            );
            expect(mockConnection.connect).toHaveBeenCalled();
        });

        test('should handle connection errors', async () => {
            const connectionError = new Error('Connection failed');
            mockConnection.connect.mockImplementation((callback) => {
                callback(connectionError);
            });

            await expect(database.initialize()).rejects.toThrow('Connection failed');
        });

        test('should reuse existing connection', async () => {
            await database.initialize();
            await database.initialize(); // Second call

            expect(mockCreateConnection).toHaveBeenCalledTimes(1);
        });

        test('should close database connection', async () => {
            await database.initialize();

            mockConnection.end.mockImplementation((callback) => {
                callback();
            });

            await database.close();

            expect(mockConnection.end).toHaveBeenCalled();
            expect(database.connection).toBe(null);
        });
    });

    describe('Query Execution', () => {
        beforeEach(async () => {
            await database.initialize();
        });

        test('should execute query successfully', async () => {
            const mockResults = [{ id: 1, name: 'test' }];
            mockConnection.query.mockImplementation((query, params, callback) => {
                callback(null, mockResults);
            });

            const result = await database.executeQuery('SELECT * FROM test', []);

            expect(mockConnection.query).toHaveBeenCalledWith(
                'SELECT * FROM test',
                [],
                expect.any(Function)
            );
            expect(result).toEqual(mockResults);
        });

        test('should handle query errors with retry', async () => {
            let callCount = 0;
            mockConnection.query.mockImplementation((query, params, callback) => {
                callCount++;
                if (callCount < 3) {
                    callback(new Error('Temporary error'));
                } else {
                    callback(null, []);
                }
            });

            const result = await database.executeQuery('SELECT * FROM test');

            expect(mockConnection.query).toHaveBeenCalledTimes(3);
            expect(result).toEqual([]);
        });

        test('should fail after max retries', async () => {
            mockConnection.query.mockImplementation((query, params, callback) => {
                callback(new Error('Persistent error'));
            });

            await expect(database.executeQuery('SELECT * FROM test')).rejects.toThrow(
                'Database query failed after 3 attempts'
            );

            expect(mockConnection.query).toHaveBeenCalledTimes(3);
        });
    });

    describe('User Session Operations', () => {
        beforeEach(async () => {
            await database.initialize();
        });

        test('should get user session successfully', async () => {
            const mockSession = {
                id: '123456789',
                phone_number: '+5511999999999',
                current_state: 'GREETING',
                session_data: '{"test": "data"}',
                created_at: new Date(),
                updated_at: new Date()
            };

            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(query).toContain('SELECT * FROM user_sessions');
                expect(params).toEqual(['123456789']);
                callback(null, [mockSession]);
            });

            const result = await database.getUserSession('123456789');

            expect(result).toMatchObject({
                id: '123456789',
                phone_number: '+5511999999999',
                current_state: 'GREETING',
                session_data: { test: 'data' } // Should be parsed from JSON
            });
        });

        test('should return null for non-existent session', async () => {
            mockConnection.query.mockImplementation((query, params, callback) => {
                callback(null, []);
            });

            const result = await database.getUserSession('nonexistent');

            expect(result).toBe(null);
        });

        test('should handle invalid JSON in session data', async () => {
            const mockSession = {
                id: '123456789',
                session_data: 'invalid json'
            };

            mockConnection.query.mockImplementation((query, params, callback) => {
                callback(null, [mockSession]);
            });

            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            const result = await database.getUserSession('123456789');

            expect(result.session_data).toEqual({});
            expect(consoleSpy).toHaveBeenCalledWith(
                'Failed to parse session data:',
                expect.any(Error)
            );

            consoleSpy.mockRestore();
        });

        test('should create user session successfully', async () => {
            const userId = '123456789';
            const phoneNumber = '+5511999999999';
            const initialState = 'INITIAL';

            mockConnection.query
                .mockImplementationOnce((query, params, callback) => {
                    expect(query).toContain('INSERT INTO user_sessions');
                    expect(params).toEqual([
                        userId,
                        phoneNumber,
                        initialState,
                        expect.stringContaining(userId)
                    ]);
                    callback(null, { insertId: 1 });
                })
                .mockImplementationOnce((query, params, callback) => {
                    // Mock the getUserSession call
                    callback(null, [{
                        id: userId,
                        phone_number: phoneNumber,
                        current_state: initialState,
                        session_data: JSON.stringify({ userId, phoneNumber })
                    }]);
                });

            const result = await database.createUserSession(userId, phoneNumber, initialState);

            expect(result).toMatchObject({
                id: userId,
                phone_number: phoneNumber,
                current_state: initialState
            });
        });

        test('should update user session successfully', async () => {
            const userId = '123456789';
            const state = 'GREETING';
            const data = { test: 'data' };

            mockConnection.query
                .mockImplementationOnce((query, params, callback) => {
                    expect(query).toContain('UPDATE user_sessions');
                    expect(params).toEqual([
                        state,
                        JSON.stringify(data),
                        userId
                    ]);
                    callback(null, { affectedRows: 1 });
                })
                .mockImplementationOnce((query, params, callback) => {
                    // Mock the getUserSession call
                    callback(null, [{
                        id: userId,
                        current_state: state,
                        session_data: JSON.stringify(data)
                    }]);
                });

            const result = await database.updateUserSession(userId, state, data);

            expect(result).toMatchObject({
                id: userId,
                current_state: state,
                session_data: data
            });
        });

        test('should delete user session successfully', async () => {
            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(query).toContain('DELETE FROM user_sessions');
                expect(params).toEqual(['123456789']);
                callback(null, { affectedRows: 1 });
            });

            await database.deleteUserSession('123456789');

            expect(mockConnection.query).toHaveBeenCalled();
        });
    });

    describe('Clinic Operations', () => {
        beforeEach(async () => {
            await database.initialize();
        });

        test('should find clinic by name', async () => {
            const mockClinics = [{
                id: 1,
                name: 'Hospital Teste',
                razao_social: 'Hospital Teste Ltda',
                cnpj: '12345678000199',
                active: true
            }];

            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(query).toContain('SELECT * FROM clinics');
                expect(query).toContain('LIKE LOWER(?)');
                expect(params).toEqual(['%Hospital Teste%']);
                callback(null, mockClinics);
            });

            const result = await database.findClinic('Hospital Teste');

            expect(result).toEqual(mockClinics);
        });

        test('should get clinic by ID', async () => {
            const mockClinic = {
                id: 1,
                name: 'Hospital Teste',
                active: true
            };

            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(query).toContain('SELECT * FROM clinics');
                expect(query).toContain('WHERE id = ?');
                expect(params).toEqual([1]);
                callback(null, [mockClinic]);
            });

            const result = await database.getClinicById(1);

            expect(result).toEqual(mockClinic);
        });

        test('should return null for non-existent clinic', async () => {
            mockConnection.query.mockImplementation((query, params, callback) => {
                callback(null, []);
            });

            const result = await database.getClinicById(999);

            expect(result).toBe(null);
        });

        test('should get all active clinics', async () => {
            const mockClinics = [
                { id: 1, name: 'Hospital A', active: true },
                { id: 2, name: 'Hospital B', active: true }
            ];

            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(query).toContain('SELECT * FROM clinics');
                expect(query).toContain('WHERE active = TRUE');
                expect(query).toContain('ORDER BY name');
                callback(null, mockClinics);
            });

            const result = await database.getAllClinics();

            expect(result).toEqual(mockClinics);
        });
    });

    describe('User Data Operations', () => {
        beforeEach(async () => {
            await database.initialize();
        });

        test('should save user data successfully', async () => {
            const sessionId = '123456789';
            const clinicId = 1;
            const userData = {
                fullName: 'João da Silva',
                cpf: '12345678901',
                email: '<EMAIL>',
                termsAccepted: true
            };

            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(query).toContain('INSERT INTO user_data');
                expect(query).toContain('ON DUPLICATE KEY UPDATE');
                expect(params).toEqual([
                    sessionId,
                    clinicId,
                    userData.fullName,
                    userData.cpf,
                    userData.email,
                    userData.termsAccepted,
                    expect.any(Date)
                ]);
                callback(null, { insertId: 1 });
            });

            await database.saveUserData(sessionId, clinicId, userData);

            expect(mockConnection.query).toHaveBeenCalled();
        });

        test('should get user data with clinic info', async () => {
            const mockUserData = {
                session_id: '123456789',
                clinic_id: 1,
                full_name: 'João da Silva',
                cpf: '12345678901',
                email: '<EMAIL>',
                terms_accepted: true,
                clinic_name: 'Hospital Teste',
                razao_social: 'Hospital Teste Ltda',
                cnpj: '12345678000199'
            };

            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(query).toContain('SELECT ud.*, c.name as clinic_name');
                expect(query).toContain('FROM user_data ud');
                expect(query).toContain('JOIN clinics c');
                expect(params).toEqual(['123456789']);
                callback(null, [mockUserData]);
            });

            const result = await database.getUserData('123456789');

            expect(result).toEqual(mockUserData);
        });

        test('should update user data field', async () => {
            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(query).toContain('UPDATE user_data SET full_name = ?');
                expect(params).toEqual(['João Silva Santos', '123456789']);
                callback(null, { affectedRows: 1 });
            });

            await database.updateUserDataField('123456789', 'full_name', 'João Silva Santos');

            expect(mockConnection.query).toHaveBeenCalled();
        });

        test('should reject invalid field names', async () => {
            await expect(
                database.updateUserDataField('123456789', 'invalid_field', 'value')
            ).rejects.toThrow('Invalid field: invalid_field');
        });

        test('should mark terms as accepted', async () => {
            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(query).toContain('UPDATE user_data');
                expect(query).toContain('SET terms_accepted = TRUE');
                expect(params).toEqual(['123456789']);
                callback(null, { affectedRows: 1 });
            });

            await database.markTermsAccepted('123456789');

            expect(mockConnection.query).toHaveBeenCalled();
        });
    });

    describe('Interaction Logging', () => {
        beforeEach(async () => {
            await database.initialize();
        });

        test('should log interaction successfully', async () => {
            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(query).toContain('INSERT INTO interaction_logs');
                expect(params).toEqual(['123456789', 'INCOMING', 'GS2']);
                callback(null, { insertId: 1 });
            });

            await database.logInteraction('123456789', 'INCOMING', 'GS2');

            expect(mockConnection.query).toHaveBeenCalled();
        });

        test('should get interaction history', async () => {
            const mockHistory = [
                { session_id: '123456789', message_type: 'INCOMING', message_content: 'GS2' },
                { session_id: '123456789', message_type: 'OUTGOING', message_content: 'Olá!' }
            ];

            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(query).toContain('SELECT * FROM interaction_logs');
                expect(query).toContain('ORDER BY timestamp DESC');
                expect(query).toContain('LIMIT ?');
                expect(params).toEqual(['123456789', 50]);
                callback(null, mockHistory);
            });

            const result = await database.getInteractionHistory('123456789');

            expect(result).toEqual(mockHistory);
        });

        test('should use custom limit for interaction history', async () => {
            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(params).toEqual(['123456789', 100]);
                callback(null, []);
            });

            await database.getInteractionHistory('123456789', 100);

            expect(mockConnection.query).toHaveBeenCalled();
        });
    });

    describe('Cleanup Operations', () => {
        beforeEach(async () => {
            await database.initialize();
        });

        test('should cleanup old sessions', async () => {
            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(query).toContain('DELETE FROM user_sessions');
                expect(query).toContain('WHERE updated_at < DATE_SUB(NOW(), INTERVAL ? HOUR)');
                expect(params).toEqual([24]);
                callback(null, { affectedRows: 5 });
            });

            const result = await database.cleanupOldSessions(24);

            expect(result).toBe(5);
        });

        test('should use default hours for cleanup', async () => {
            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(params).toEqual([24]);
                callback(null, { affectedRows: 0 });
            });

            const result = await database.cleanupOldSessions();

            expect(result).toBe(0);
        });
    });

    describe('Health Check', () => {
        beforeEach(async () => {
            await database.initialize();
        });

        test('should return healthy status', async () => {
            mockConnection.query.mockImplementation((query, params, callback) => {
                expect(query).toBe('SELECT 1 as health');
                callback(null, [{ health: 1 }]);
            });

            const result = await database.healthCheck();

            expect(result).toMatchObject({
                status: 'healthy',
                timestamp: expect.any(String)
            });
        });

        test('should return unhealthy status on error', async () => {
            mockConnection.query.mockImplementation((query, params, callback) => {
                callback(new Error('Health check failed'));
            });

            const result = await database.healthCheck();

            expect(result).toMatchObject({
                status: 'unhealthy',
                error: 'Health check failed',
                timestamp: expect.any(String)
            });
        });
    });
});