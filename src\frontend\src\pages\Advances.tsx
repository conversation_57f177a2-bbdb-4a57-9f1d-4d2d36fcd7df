// =====================================================
// ADVANCES PAGE - PÁGINA DE ANTECIPAÇÕES
// Gerenciamento de antecipações de recebíveis
// =====================================================

import React from 'react'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'
import { CreditCard, Plus, Search, Filter } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

export function Advances() {
  const { t } = useTranslation()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-heading flex items-center space-x-3">
            <CreditCard className="w-8 h-8 text-primary-600" />
            <span>{t('advances.title')}</span>
          </h1>
          <p className="text-muted mt-1">
            {t('advances.subtitle')}
          </p>
        </div>
        
        <Button
          variant="primary"
          icon={<Plus className="w-5 h-5" />}
        >
          {t('advances.createAdvance')}
        </Button>
      </div>

      {/* Coming Soon */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-12"
      >
        <Card className="max-w-md mx-auto">
          <CardContent className="py-12">
            <CreditCard className="w-16 h-16 text-primary-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-heading mb-2">
              Em Desenvolvimento
            </h3>
            <p className="text-muted">
              A página de gerenciamento de antecipações está sendo desenvolvida e estará disponível em breve.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
