// =====================================================
// FORGOT PASSWORD MODAL - MODAL DE RECUPERAÇÃO DE SENHA
// Modal para solicitar recuperação de senha
// =====================================================

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useTranslation } from 'react-i18next'
import { Mail, ArrowLeft } from 'lucide-react'
import { Modal, ModalContent, ModalFooter } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { usePasswordRecovery } from '@/hooks/useAuth'
import { isValidEmail } from '@/utils'

// Schema de validação
const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, 'E-mail é obrigatório')
    .refine((email) => isValidEmail(email), 'E-mail inválido')
})

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>

interface ForgotPasswordModalProps {
  isOpen: boolean
  onClose: () => void
}

export function ForgotPasswordModal({ isOpen, onClose }: ForgotPasswordModalProps) {
  const { t } = useTranslation()
  const { requestReset, isRequestingReset } = usePasswordRecovery()
  const [emailSent, setEmailSent] = React.useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    mode: 'onChange'
  })

  // Reset form when modal closes
  React.useEffect(() => {
    if (!isOpen) {
      reset()
      setEmailSent(false)
    }
  }, [isOpen, reset])

  const onSubmit = async (data: ForgotPasswordFormData) => {
    try {
      await requestReset(data.email)
      setEmailSent(true)
    } catch (error) {
      // Error is handled by the hook
    }
  }

  const handleClose = () => {
    onClose()
    setEmailSent(false)
  }

  const handleBackToLogin = () => {
    setEmailSent(false)
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={emailSent ? 'E-mail Enviado' : t('auth.forgotPasswordTitle')}
      size="sm"
      closeOnOverlayClick={!isRequestingReset}
      closeOnEscape={!isRequestingReset}
    >
      <ModalContent>
        {emailSent ? (
          // Success state
          <div className="text-center py-4">
            <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Mail className="w-8 h-8 text-success-600" />
            </div>
            
            <h3 className="text-lg font-semibold text-heading mb-2">
              E-mail de recuperação enviado!
            </h3>
            
            <p className="text-muted mb-6">
              Verifique sua caixa de entrada e siga as instruções para redefinir sua senha.
            </p>
            
            <div className="bg-secondary-50 dark:bg-secondary-800 rounded-lg p-4 text-sm text-muted">
              <p className="mb-2">
                <strong>Não recebeu o e-mail?</strong>
              </p>
              <ul className="text-left space-y-1">
                <li>• Verifique sua pasta de spam</li>
                <li>• Aguarde alguns minutos</li>
                <li>• Tente novamente com outro e-mail</li>
              </ul>
            </div>
          </div>
        ) : (
          // Form state
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="text-center mb-6">
              <p className="text-muted">
                {t('auth.forgotPasswordSubtitle')}
              </p>
            </div>

            <Input
              {...register('email')}
              type="email"
              label={t('common.email')}
              placeholder="<EMAIL>"
              leftIcon={<Mail className="w-5 h-5" />}
              error={errors.email?.message}
              autoComplete="email"
              autoFocus
            />

            <div className="bg-secondary-50 dark:bg-secondary-800 rounded-lg p-4 text-sm text-muted">
              <p>
                <strong>Lembre-se:</strong> Use o e-mail cadastrado em sua conta para receber as instruções de recuperação.
              </p>
            </div>
          </form>
        )}
      </ModalContent>

      <ModalFooter>
        {emailSent ? (
          <div className="flex space-x-3 w-full">
            <Button
              variant="outline"
              onClick={handleBackToLogin}
              icon={<ArrowLeft className="w-4 h-4" />}
              fullWidth
            >
              Tentar Outro E-mail
            </Button>
            <Button
              variant="primary"
              onClick={handleClose}
              fullWidth
            >
              Fechar
            </Button>
          </div>
        ) : (
          <div className="flex space-x-3 w-full">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isRequestingReset}
              fullWidth
            >
              {t('common.cancel')}
            </Button>
            <Button
              variant="primary"
              onClick={handleSubmit(onSubmit)}
              loading={isRequestingReset}
              disabled={!isValid || isRequestingReset}
              icon={<Mail className="w-4 h-4" />}
              fullWidth
            >
              {t('auth.sendResetLink')}
            </Button>
          </div>
        )}
      </ModalFooter>
    </Modal>
  )
}
