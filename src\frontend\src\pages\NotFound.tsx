// =====================================================
// NOT FOUND PAGE - PÁGINA 404
// Página de erro 404 - Página não encontrada
// =====================================================

import React from 'react'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useTranslation } from 'react-i18next'
import { Home, ArrowLeft, Search } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'

export function NotFound() {
  const { t } = useTranslation()

  return (
    <div className="min-h-screen flex items-center justify-center bg-secondary-50 dark:bg-secondary-900 p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="shadow-strong">
          <CardContent className="text-center py-12">
            {/* 404 Animation */}
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, duration: 0.3 }}
              className="mb-8"
            >
              <div className="text-8xl font-bold text-primary-600 mb-4">
                404
              </div>
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
                className="text-4xl mb-4"
              >
                🔍
              </motion.div>
            </motion.div>

            {/* Content */}
            <div className="space-y-4 mb-8">
              <h1 className="text-2xl font-bold text-heading">
                Página não encontrada
              </h1>
              <p className="text-muted">
                A página que você está procurando não existe ou foi movida para outro local.
              </p>
            </div>

            {/* Actions */}
            <div className="space-y-3">
              <Button
                as={Link}
                to="/dashboard"
                variant="primary"
                size="lg"
                fullWidth
                icon={<Home className="w-5 h-5" />}
              >
                Ir para o Dashboard
              </Button>

              <Button
                onClick={() => window.history.back()}
                variant="outline"
                size="lg"
                fullWidth
                icon={<ArrowLeft className="w-5 h-5" />}
              >
                Voltar à página anterior
              </Button>
            </div>

            {/* Help */}
            <div className="mt-8 pt-6 border-t border-secondary-200 dark:border-secondary-700">
              <p className="text-sm text-muted mb-4">
                Precisa de ajuda? Entre em contato conosco:
              </p>
              <div className="flex items-center justify-center space-x-4 text-sm">
                <button className="text-primary-600 hover:text-primary-700 transition-colors">
                  Suporte Técnico
                </button>
                <span className="text-secondary-300">•</span>
                <button className="text-primary-600 hover:text-primary-700 transition-colors">
                  Documentação
                </button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
          className="text-center mt-6"
        >
          <p className="text-xs text-muted">
            Sistema GS2 - Gestão de Saúde
          </p>
        </motion.div>
      </motion.div>
    </div>
  )
}
