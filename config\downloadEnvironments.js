const fs = require('fs');
const { getArg, getListFunctions } = require('./functions');
const lambdas = require('./lambdas')['us-east-2'];

const profile = getArg('profile');
const region = getArg('region');

if (!profile || !region) {
  console.log(
    '\x1b[31m%s\x1b[0m',
    '\nVocê precisa passar "profile" e "region"\n'
  );
  process.exit();
}

const JSON_NAME = 'Functions.local.json';

const createEnvFromFunctions = () => {
  const env = Object.values(require(`../${JSON_NAME}`)).reduce((a, c) => {
    if (c?.Environment?.Variables) {
      Object.keys(c?.Environment?.Variables).forEach((key) => {
        if (!a[key]) {
          a[key] = c.Environment.Variables[key];
        }
      });
    }

    return a;
  }, {});

  console.log(env);

  fs.writeFileSync('ENV.local.json', JSON.stringify(env, null, 2));
};

const init = async function () {
  try {
    const { Functions } = JSON.parse(await getListFunctions(profile, region));

    const getAllFunctionListResult = Functions.filter((f) => {
      return lambdas.some((n) => f.FunctionName === n);
    });

    fs.writeFileSync(JSON_NAME, JSON.stringify(getAllFunctionListResult));

    console.log(
      'lembre de alterar as variaveis de ambiente no arquivo Functions.json'
    );

    createEnvFromFunctions();
  } catch (e) {
    console.log('error', e);
  }
};

if (!profile || !region) {
  console.log('Tem de configurar profile e region no arquivo env.js');
  process.exit();
}

console.log('Profile: ' + profile);
console.log('Region: ' + region);

init();
