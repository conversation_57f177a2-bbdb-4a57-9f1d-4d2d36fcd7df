import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import db from './database.mjs';
import auditLogger from './audit.mjs';

class AuthService {
  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'gs2-secret-key';
    this.jwtExpiration = process.env.JWT_EXPIRATION || '24h';
  }

  /**
   * Função para limpar números (manter apenas dígitos)
   */
  onlyNumber(str) {
    return String(str).replace(/\D/g, '');
  }

  /**
   * Gerar token JWT
   */
  generateToken(user) {
    const payload = {
      id: user.id,
      cpf: user.cpf,
      email: user.email,
      nome: user.nome,
      iat: Math.floor(Date.now() / 1000)
    };
    
    return jwt.sign(payload, this.jwtSecret, {
      expiresIn: this.jwtExpiration
    });
  }

  /**
   * Verificar token JWT
   */
  verifyToken(token) {
    try {
      return jwt.verify(token, this.jwtSecret);
    } catch (error) {
      throw new Error('Token inválido');
    }
  }

  /**
   * Extrair informações do request
   */
  extractRequestInfo(request) {
    return {
      ipAddress: request.headers['x-forwarded-for'] || 
                request.headers['x-real-ip'] || 
                request.headers['cf-connecting-ip'] || 
                'unknown',
      userAgent: request.headers['user-agent'] || 'unknown'
    };
  }

  /**
   * Fazer login
   */
  async login(request) {
    const { username, password } = request.body;
    const { ipAddress, userAgent } = this.extractRequestInfo(request);
    
    if (!username || !password) {
      await auditLogger.logLogin(null, null, null, false, ipAddress, userAgent, 'Credenciais não fornecidas');
      return {
        statusCode: 400,
        success: false,
        message: 'CPF e senha são obrigatórios'
      };
    }

    const cpf = this.onlyNumber(username);

    try {
      // Buscar usuário
      const usuario = await db('usuarios')
        .select('id', 'cpf', 'senha', 'email', 'nome', 'ativo', 'aceite_lgpd', 'onboarding_pendente')
        .where('cpf', cpf)
        .where('ativo', true)
        .first();

      if (!usuario) {
        await auditLogger.logLogin(cpf, null, null, false, ipAddress, userAgent, 'Usuário não encontrado');
        return {
          statusCode: 401,
          success: false,
          message: 'Usuário ou senha inválida'
        };
      }

      // Verificar senha
      const isValidPassword = bcrypt.compareSync(password, usuario.senha || '');

      if (!isValidPassword) {
        await auditLogger.logLogin(cpf, usuario.nome, usuario.email, false, ipAddress, userAgent, 'Senha incorreta');
        return {
          statusCode: 401,
          success: false,
          message: 'Usuário ou senha inválida'
        };
      }

      // Buscar grupos do usuário
      const grupos = await db('usuario_grupos as ug')
        .join('grupos as g', 'ug.grupo_id', 'g.id')
        .select('g.nome', 'g.descricao', 'g.nivel_acesso')
        .where('ug.usuario_id', usuario.id)
        .where('ug.ativo', true)
        .where('g.ativo', true);

      // Buscar rotas/permissões
      const rotas = await db('usuario_grupos as ug')
        .join('grupo_rotas as gr', 'ug.grupo_id', 'gr.grupo_id')
        .join('rotas as r', 'gr.rota_id', 'r.id')
        .select('r.rota', 'r.nome_tela', 'r.icone', 'r.ordem', 'r.categoria', 'gr.permissoes')
        .where('ug.usuario_id', usuario.id)
        .where('ug.ativo', true)
        .where('r.ativo', true)
        .orderBy('r.ordem');

      // Buscar configurações do sistema
      const configuracoes = await db('configuracoes')
        .select('chave', 'valor', 'tipo')
        .whereIn('chave', ['valor_minimo_antecipacao', 'valor_maximo_antecipacao']);

      const configMap = {};
      configuracoes.forEach(config => {
        let valor = config.valor;
        if (config.tipo === 'number') {
          valor = parseFloat(valor) || 0;
        } else if (config.tipo === 'boolean') {
          valor = valor === 'true';
        }
        configMap[config.chave] = valor;
      });

      // Atualizar último login
      await db('usuarios')
        .where('id', usuario.id)
        .update({ ultimo_login: new Date() });

      // Montar dados do usuário
      const userData = {
        id: usuario.id,
        cpf: usuario.cpf,
        nome: usuario.nome,
        email: usuario.email,
        aceiteLGPD: usuario.aceite_lgpd,
        psOnboardingPendente: usuario.onboarding_pendente,
        grupos: grupos || [],
        rotas: usuario.onboarding_pendente ? [] : rotas || [],
        valorMinimoAntecipacao: configMap['valor_minimo_antecipacao'] || 0,
        valorMaximoAntecipacao: configMap['valor_maximo_antecipacao'] || 0
      };

      // Gerar token
      const token = this.generateToken(userData);
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();

      // Log de sucesso
      await auditLogger.logLogin(cpf, usuario.nome, usuario.email, true, ipAddress, userAgent);

      return {
        statusCode: 200,
        success: true,
        message: 'Login realizado com sucesso',
        data: {
          user: userData,
          token,
          expires_at: expiresAt
        }
      };

    } catch (error) {
      console.error('Erro no login:', error);
      await auditLogger.logLogin(cpf, null, null, false, ipAddress, userAgent, `Erro interno: ${error.message}`);

      return {
        statusCode: 500,
        success: false,
        message: 'Erro interno do servidor'
      };
    }
  }

  /**
   * Verificar se usuário está autenticado
   */
  async verifyAuth(request) {
    try {
      const authHeader = request.headers.authorization || request.headers.Authorization;

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return {
          statusCode: 401,
          success: false,
          message: 'Token não fornecido'
        };
      }

      const token = authHeader.substring(7);
      const decoded = this.verifyToken(token);

      // Buscar usuário atual
      const usuario = await db('usuarios')
        .select('id', 'cpf', 'email', 'nome', 'ativo')
        .where('id', decoded.id)
        .where('ativo', true)
        .first();

      if (!usuario) {
        return {
          statusCode: 401,
          success: false,
          message: 'Usuário não encontrado'
        };
      }

      return {
        statusCode: 200,
        success: true,
        data: {
          user: {
            id: usuario.id,
            cpf: usuario.cpf,
            nome: usuario.nome,
            email: usuario.email
          }
        }
      };

    } catch (error) {
      return {
        statusCode: 401,
        success: false,
        message: 'Token inválido'
      };
    }
  }

  /**
   * Middleware de autenticação
   */
  async authMiddleware(request) {
    const authResult = await this.verifyAuth(request);
    
    if (!authResult.success) {
      throw new Error(authResult.message);
    }
    
    // Adicionar usuário ao request
    request.user = authResult.data.user;
    return request;
  }
}

export default new AuthService();
