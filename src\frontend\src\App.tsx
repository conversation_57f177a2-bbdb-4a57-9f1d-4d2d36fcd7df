// =====================================================
// APP COMPONENT - COMPONENTE PRINCIPAL DA APLICAÇÃO
// Configuração de rotas e providers principais
// =====================================================

import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { Layout, AuthLayout, ErrorLayout } from '@/components/layout/Layout'

// Pages
import { Dashboard } from '@/pages/Dashboard'
import { Login } from '@/pages/Login'
import { Users } from '@/pages/Users'
import { Professionals } from '@/pages/Professionals'
import { Clients } from '@/pages/Clients'
import { Shifts } from '@/pages/Shifts'
import { Advances } from '@/pages/Advances'
import { WhatsApp } from '@/pages/WhatsApp'
import { Audit } from '@/pages/Audit'
import { Reports } from '@/pages/Reports'
import { Settings } from '@/pages/Settings'
import { NotFound } from '@/pages/NotFound'

export function App() {
  return (
    <Routes>
      {/* Rota raiz - redirecionar para dashboard */}
      <Route path="/" element={<Navigate to="/dashboard" replace />} />

      {/* Rotas de autenticação */}
      <Route element={<AuthLayout />}>
        <Route path="/login" element={<Login />} />
      </Route>

      {/* Rotas protegidas */}
      <Route
        element={
          <ProtectedRoute>
            <Layout />
          </ProtectedRoute>
        }
      >
        {/* Dashboard */}
        <Route path="/dashboard" element={<Dashboard />} />

        {/* Usuários */}
        <Route
          path="/users/*"
          element={
            <ProtectedRoute requiredPermission="users.read">
              <Users />
            </ProtectedRoute>
          }
        />

        {/* Profissionais */}
        <Route
          path="/professionals/*"
          element={
            <ProtectedRoute requiredPermission="professionals.read">
              <Professionals />
            </ProtectedRoute>
          }
        />

        {/* Clientes */}
        <Route
          path="/clients/*"
          element={
            <ProtectedRoute requiredPermission="clients.read">
              <Clients />
            </ProtectedRoute>
          }
        />

        {/* Plantões */}
        <Route
          path="/shifts/*"
          element={
            <ProtectedRoute requiredPermission="shifts.read">
              <Shifts />
            </ProtectedRoute>
          }
        />

        {/* Antecipações */}
        <Route
          path="/advances/*"
          element={
            <ProtectedRoute requiredPermission="advances.read">
              <Advances />
            </ProtectedRoute>
          }
        />

        {/* WhatsApp */}
        <Route
          path="/whatsapp/*"
          element={
            <ProtectedRoute requiredPermission="whatsapp.read">
              <WhatsApp />
            </ProtectedRoute>
          }
        />

        {/* Auditoria */}
        <Route
          path="/audit/*"
          element={
            <ProtectedRoute requiredPermission="audit.read">
              <Audit />
            </ProtectedRoute>
          }
        />

        {/* Relatórios */}
        <Route
          path="/reports/*"
          element={
            <ProtectedRoute requiredPermission="reports.read">
              <Reports />
            </ProtectedRoute>
          }
        />

        {/* Configurações */}
        <Route
          path="/settings/*"
          element={
            <ProtectedRoute requiredPermission="settings.read">
              <Settings />
            </ProtectedRoute>
          }
        />
      </Route>

      {/* Página 404 */}
      <Route element={<ErrorLayout />}>
        <Route path="*" element={<NotFound />} />
      </Route>
    </Routes>
  )
}
