// =====================================================
// AUTH STORE - STORE DE AUTENTICAÇÃO
// Gerenciamento de estado de autenticação com Zustand
// =====================================================

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { User, AuthResponse } from '@/types'
import { modernAuthService } from '@/services/modernAuthService'

interface AuthState {
  // Estado
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  
  // Ações
  login: (cpf: string, password: string) => Promise<boolean>
  logout: () => void
  refreshToken: () => Promise<boolean>
  updateUser: (user: Partial<User>) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  
  // Verificações
  hasPermission: (permission: string) => boolean
  hasGroup: (group: string) => boolean
  hasAnyGroup: (groups: string[]) => boolean
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Estado inicial
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Ação de login
      login: async (cpf: string, password: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await modernAuthService.login({ cpf, password })
          
          // A API moderna já retorna os dados diretamente
          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })

          return true
        } catch (error: any) {
          set({
            error: error.message || 'Erro de conexão',
            isLoading: false
          })
          return false
        }
      },

      // Ação de logout
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null
        })
      },

      // Renovar token
      refreshToken: async () => {
        const { token } = get()

        if (!token) return false

        try {
          const response = await modernAuthService.refreshToken()

          set({
            token: response.token,
            isAuthenticated: true
          })

          return true
        } catch (error) {
          get().logout()
          return false
        }
      },

      // Atualizar dados do usuário
      updateUser: (userData: Partial<User>) => {
        const { user } = get()
        
        if (user) {
          set({
            user: { ...user, ...userData }
          })
        }
      },

      // Limpar erro
      clearError: () => {
        set({ error: null })
      },

      // Definir loading
      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      // Verificar permissão
      hasPermission: (permission: string) => {
        const { user } = get()
        
        if (!user || !user.permissions) return false
        
        return user.permissions[permission] === true
      },

      // Verificar grupo
      hasGroup: (group: string) => {
        const { user } = get()
        
        if (!user || !user.groups) return false
        
        return user.groups.some(g => g.name === group)
      },

      // Verificar se tem algum dos grupos
      hasAnyGroup: (groups: string[]) => {
        const { user } = get()
        
        if (!user || !user.groups) return false
        
        return user.groups.some(g => groups.includes(g.name))
      }
    }),
    {
      name: 'gs2-auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      }),
      onRehydrateStorage: () => (state) => {
        // Token será configurado automaticamente pelos interceptors
        console.log('Auth store rehydrated', { hasToken: !!state?.token })
      }
    }
  )
)

// Hook para verificar se o usuário está autenticado
export const useAuth = () => {
  const { user, isAuthenticated, isLoading } = useAuthStore()
  
  return {
    user,
    isAuthenticated,
    isLoading,
    isGuest: !isAuthenticated
  }
}

// Hook para verificar permissões
export const usePermissions = () => {
  const { hasPermission, hasGroup, hasAnyGroup } = useAuthStore()
  
  return {
    hasPermission,
    hasGroup,
    hasAnyGroup,
    isMaster: hasGroup('Master'),
    isAdmin: hasAnyGroup(['Master', 'Admin']),
    isProfessional: hasGroup('Profissional'),
    isFinancial: hasAnyGroup(['Master', 'Admin', 'Financeiro'])
  }
}
