// =====================================================
// LOGIN PAGE - PÁGINA DE LOGIN
// Página de autenticação do sistema
// =====================================================

import React from 'react'
import { Navigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useTranslation } from 'react-i18next'
import { LoginForm } from '@/components/auth/LoginForm'
import { useAuth } from '@/hooks/useAuth'
import { useModal } from '@/components/ui/Modal'
import { ForgotPasswordModal } from '@/components/auth/ForgotPasswordModal'

export function Login() {
  const { t } = useTranslation()
  const { isAuthenticated } = useAuth()
  const forgotPasswordModal = useModal()

  // Redirecionar se já estiver autenticado
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />
  }

  return (
    <>
      <div className="w-full">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <h1 className="text-3xl font-bold text-heading mb-2">
            Sistema GS2
          </h1>
          <p className="text-muted">
            Gestão de Saúde e Plantões
          </p>
        </motion.div>

        {/* Login Form */}
        <LoginForm onForgotPassword={forgotPasswordModal.open} />

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
          className="text-center mt-8 space-y-4"
        >
          <div className="text-xs text-muted">
            <p>© 2024 Sistema GS2. Todos os direitos reservados.</p>
            <p className="mt-1">
              Desenvolvido com ❤️ para profissionais de saúde
            </p>
          </div>

          {/* Links úteis */}
          <div className="flex items-center justify-center space-x-4 text-xs">
            <button className="text-primary-600 hover:text-primary-700 transition-colors">
              Política de Privacidade
            </button>
            <span className="text-secondary-300">•</span>
            <button className="text-primary-600 hover:text-primary-700 transition-colors">
              Termos de Uso
            </button>
            <span className="text-secondary-300">•</span>
            <button className="text-primary-600 hover:text-primary-700 transition-colors">
              Suporte
            </button>
          </div>
        </motion.div>
      </div>

      {/* Modal de recuperação de senha */}
      <ForgotPasswordModal
        isOpen={forgotPasswordModal.isOpen}
        onClose={forgotPasswordModal.close}
      />
    </>
  )
}
