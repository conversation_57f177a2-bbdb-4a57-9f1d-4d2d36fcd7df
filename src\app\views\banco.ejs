<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title><PERSON>co <PERSON> <PERSON><PERSON> (Dracula Theme)</title>
    <style>
      body {
        background: #282a36;
        color: #f8f8f2;
        font-family: Arial, sans-serif;
        margin: 20px;
      }
      .container {
        display: flex;
      }
      .sidebar {
        width: 20%;
        border-right: 1px solid #6272a4;
        padding-right: 10px;
      }
      .content {
        width: 80%;
        padding-left: 10px;
      }
      ul {
        list-style: none;
        padding: 0;
      }
      li {
        padding: 10px;
        border-bottom: 1px solid #6272a4;
        cursor: pointer;
      }
      li:hover {
        background-color: #44475a;
      }
      .search {
        margin-bottom: 10px;
      }
      .table-filter {
        margin: 10px 0;
      }
      input,
      textarea {
        background: #44475a;
        border: 1px solid #6272a4;
        color: #f8f8f2;
        padding: 8px;
        border-radius: 3px;
      }
      button {
        background: #6272a4;
        border: none;
        color: #f8f8f2;
        padding: 8px 12px;
        border-radius: 3px;
        cursor: pointer;
      }
      button:hover {
        background: #50fa7b;
      }
      .pagination button {
        margin: 5px;
      }
      #tableResults {
        max-height: 80vh;
        overflow-y: auto;
      }
      pre {
        font-family: 'Cascadia Code', monospace;
        font-size: 16px;
        line-height: 1.4;
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }
      /* Syntax highlighting for JSON */
      .json-key {
        color: #8be9fd;
      }
      .json-string {
        color: #f1fa8c;
      }
      .json-number {
        color: #bd93f9;
      }
      .json-boolean {
        color: #ff79c6;
      }
      .json-null {
        color: #6272a4;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="sidebar">
        <h1>Tabelas</h1>
        <div class="table-filter">
          <input id="tableFilterInput" placeholder="Filtrar tabelas..." onkeyup="filterTables()" />
        </div>
        <ul id="tables">
          <% tables.forEach(function(t){ %>
          <li onclick="loadTableData('<%= t %>', 0, '')"><%= t %></li>
          <% }); %>
        </ul>
      </div>
      <div class="content">
        <div class="search">
          <input id="searchInput" placeholder="Pesquisar..." />
          <button onclick="reloadCurrentTable()">Pesquisar</button>
          <button onclick="toggleViewMode()">
            Alternar Vista (Atual: <span id="viewModeLabel">JSON</span>)
          </button>
          <!-- Optional custom WHERE clause textarea -->
          <textarea
            id="whereScript"
            placeholder="Digite a condição WHERE (opcional)..."
            rows="3"
            style="width: 100%; margin-top: 8px"
          ></textarea>
        </div>
        <div id="tableData">
          <h2>Selecione uma tabela para ver os dados</h2>
        </div>
      </div>
    </div>
    <script>
      var viewMode = "json";
      var currentTable = "", currentPage = 0;
      function updateUrl(table, page, search) {
        var params = new URLSearchParams();
        if(table) { params.set("table", table); }
        if(page) { params.set("page", page); }
        if(search) { params.set("search", search); }
        params.set("view", "html");
        window.history.pushState({}, "", "?" + params.toString());
      }
      function jsonHighlight(json) {
        if (typeof json !== "string") { json = JSON.stringify(json, null, 2); }
        json = json.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
        return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\\s*:)?|\b(true|false|null)\b|\b-?\\d+(\\.\\d+)?\b)/g, function(match) {
          var cls = "json-number";
          if (/^"/.test(match)) { cls = /:$/.test(match) ? "json-key" : "json-string"; }
          else if (/true|false/.test(match)) { cls = "json-boolean"; }
          else if (/null/.test(match)) { cls = "json-null"; }
          return '<span class="'+cls+'">'+match+'</span>';
        });
      }
      function toggleViewMode() {
        viewMode = (viewMode === "json" ? "table" : "json");
        document.getElementById("viewModeLabel").textContent = viewMode.toUpperCase();
        if(currentTable) { loadTableData(currentTable, currentPage, document.getElementById("searchInput").value); }
      }
      function filterTables() {
        var filter = document.getElementById("tableFilterInput").value.toLowerCase();
        var nodes = document.querySelectorAll("#tables li");
        nodes.forEach(function(node) {
          var text = node.textContent.toLowerCase();
          node.style.display = (text.indexOf(filter) !== -1) ? "block" : "none";
        });
      }
      function reloadCurrentTable() {
        loadTableData(currentTable, 0, document.getElementById("searchInput").value);
      }
      function loadTableData(table, page, search) {
        currentTable = table;
        currentPage = page;
        updateUrl(table, page, search);
        var customWhere = document.getElementById("whereScript").value;
        var xhrUrl = "/banco?ajax=true&table=" + table +
                     "&page=" + page +
                     "&search=" + encodeURIComponent(search) +
                     "&whereScript=" + encodeURIComponent(customWhere);
        fetch(xhrUrl)
          .then(function(res) { return res.json(); })
          .then(function(json) {
            var content = "<h2>Dados da Tabela: " + table + "</h2>";
            if(viewMode === "json") {
              var highlighted = jsonHighlight(json.data);
              content += "<div id='tableResults'><pre>" + highlighted + "</pre></div>";
            } else {
              if(json.data.length > 0) {
                var headers = Object.keys(json.data[0]);
                var tableHtml = "<table border='1' cellpadding='5' cellspacing='0' style='width:100%; border-collapse: collapse;'><thead><tr>";
                headers.forEach(function(header) { tableHtml += "<th>" + header + "</th>"; });
                tableHtml += "</tr></thead><tbody>";
                json.data.forEach(function(row) {
                  tableHtml += "<tr>";
                  headers.forEach(function(header) { tableHtml += "<td>" + (row[header] || "") + "</td>"; });
                  tableHtml += "</tr>";
                });
                tableHtml += "</tbody></table>";
                content += "<div id='tableResults'>" + tableHtml + "</div>";
              } else { content += "<div id='tableResults'><p>Nenhum dado encontrado.</p></div>"; }
            }
            content += "<div class='pagination'>";
            if(page > 0) {
              content += "<button onclick=\"loadTableData('" + table + "', " + (page-1) + ", document.getElementById('searchInput').value)\">Anterior</button>";
            }
            if(json.data.length === <%= LIMIT %>) {
              content += "<button onclick=\"loadTableData('" + table + "', " + (page+1) + ", document.getElementById('searchInput').value)\">Próxima</button>";
            }
            content += "</div>";
            document.getElementById("tableData").innerHTML = content;
          });
      }
      window.addEventListener("load", function() {
        var params = new URLSearchParams(window.location.search);
        var table = params.get("table");
        var page = Number(params.get("page")) || 0;
        var search = params.get("search") || "";
        if(table) {
          document.getElementById("searchInput").value = search;
          loadTableData(table, page, search);
        }
      });
    </script>
  </body>
</html>
