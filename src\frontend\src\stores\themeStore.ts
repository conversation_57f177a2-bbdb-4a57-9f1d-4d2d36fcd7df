// =====================================================
// THEME STORE - STORE DE TEMA
// Gerenciamento de tema (light/dark/system) com Zustand
// =====================================================

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { Theme } from '@/types'

interface ThemeState {
  // Estado
  theme: Theme
  systemTheme: 'light' | 'dark'
  currentTheme: 'light' | 'dark'
  
  // Ações
  setTheme: (theme: Theme) => void
  toggleTheme: () => void
  initializeTheme: () => void
}

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      // Estado inicial
      theme: 'system',
      systemTheme: 'light',
      currentTheme: 'light',

      // Definir tema
      setTheme: (theme: Theme) => {
        const { systemTheme } = get()
        const currentTheme = theme === 'system' ? systemTheme : theme
        
        set({ theme, currentTheme })
        
        // Aplicar tema no DOM
        applyThemeToDOM(currentTheme)
      },

      // Alternar tema
      toggleTheme: () => {
        const { theme } = get()
        
        if (theme === 'system') {
          set({ theme: 'light', currentTheme: 'light' })
          applyThemeToDOM('light')
        } else if (theme === 'light') {
          set({ theme: 'dark', currentTheme: 'dark' })
          applyThemeToDOM('dark')
        } else {
          set({ theme: 'system' })
          const systemTheme = getSystemTheme()
          set({ systemTheme, currentTheme: systemTheme })
          applyThemeToDOM(systemTheme)
        }
      },

      // Inicializar tema
      initializeTheme: () => {
        const { theme } = get()
        const systemTheme = getSystemTheme()
        const currentTheme = theme === 'system' ? systemTheme : theme
        
        set({ systemTheme, currentTheme })
        applyThemeToDOM(currentTheme)
        
        // Escutar mudanças no tema do sistema
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        
        const handleSystemThemeChange = (e: MediaQueryListEvent) => {
          const newSystemTheme = e.matches ? 'dark' : 'light'
          const { theme } = get()
          
          set({ systemTheme: newSystemTheme })
          
          if (theme === 'system') {
            set({ currentTheme: newSystemTheme })
            applyThemeToDOM(newSystemTheme)
          }
        }
        
        mediaQuery.addEventListener('change', handleSystemThemeChange)
        
        // Cleanup function (seria usado em um useEffect)
        return () => {
          mediaQuery.removeEventListener('change', handleSystemThemeChange)
        }
      }
    }),
    {
      name: 'gs2-theme-storage',
      partialize: (state) => ({
        theme: state.theme
      })
    }
  )
)

// Função para obter tema do sistema
function getSystemTheme(): 'light' | 'dark' {
  if (typeof window === 'undefined') return 'light'
  
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
}

// Função para aplicar tema no DOM
function applyThemeToDOM(theme: 'light' | 'dark') {
  const root = document.documentElement
  
  if (theme === 'dark') {
    root.classList.add('dark')
  } else {
    root.classList.remove('dark')
  }
  
  // Atualizar meta theme-color para mobile
  const metaThemeColor = document.querySelector('meta[name="theme-color"]')
  if (metaThemeColor) {
    metaThemeColor.setAttribute('content', theme === 'dark' ? '#0f172a' : '#ffffff')
  }
}

// Hook para usar tema
export const useTheme = () => {
  const { theme, currentTheme, setTheme, toggleTheme } = useThemeStore()
  
  return {
    theme,
    currentTheme,
    isDark: currentTheme === 'dark',
    isLight: currentTheme === 'light',
    isSystem: theme === 'system',
    setTheme,
    toggleTheme
  }
}
