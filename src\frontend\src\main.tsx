// =====================================================
// MAIN - PONTO DE ENTRADA DA APLICAÇÃO
// Configuração inicial do React e providers
// =====================================================

import React from 'react'
import ReactDOM from 'react-dom/client'
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { Toaster } from 'react-hot-toast'
import { I18nextProvider } from 'react-i18next'

import { App } from './App'
import { i18n } from './locales/i18n'
import { useThemeStore } from './stores/themeStore'
import { I18nProvider } from '@/components/I18nProvider'

import './styles/globals.css'

// Configuração do React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutos
      gcTime: 10 * 60 * 1000, // 10 minutos (anteriormente cacheTime)
      retry: (failureCount, error: any) => {
        // Não tentar novamente para erros 4xx
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false
        }
        return failureCount < 3
      },
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: false,
    },
  },
})

// Componente para inicializar tema
function ThemeInitializer({ children }: { children: React.ReactNode }) {
  React.useEffect(() => {
    useThemeStore.getState().initializeTheme()
  }, [])

  return <>{children}</>
}

// Configuração do Toaster
const toasterConfig = {
  position: 'top-right' as const,
  duration: 4000,
  style: {
    background: 'var(--toast-bg)',
    color: 'var(--toast-color)',
    border: '1px solid var(--toast-border)',
    borderRadius: '8px',
    fontSize: '14px',
    fontWeight: '500',
  },
  success: {
    iconTheme: {
      primary: '#22c55e',
      secondary: '#ffffff',
    },
  },
  error: {
    iconTheme: {
      primary: '#ef4444',
      secondary: '#ffffff',
    },
  },
  loading: {
    iconTheme: {
      primary: '#3b82f6',
      secondary: '#ffffff',
    },
  },
}

// Render da aplicação
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <I18nextProvider i18n={i18n}>
          <I18nProvider>
            <ThemeInitializer>
              <App />
              
              {/* Toast notifications */}
              <Toaster {...toasterConfig} />
              
              {/* React Query DevTools (apenas em desenvolvimento) */}
              {import.meta.env.DEV && (
                <ReactQueryDevtools 
                  initialIsOpen={false}
                  position="bottom-right"
                />
              )}
            </ThemeInitializer>
          </I18nProvider>
        </I18nextProvider>
      </QueryClientProvider>
    </BrowserRouter>
  </React.StrictMode>
)
